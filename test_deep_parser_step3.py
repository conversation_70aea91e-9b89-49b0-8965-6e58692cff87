"""
深度SQL解析器第三步测试
测试聚合函数和HAVING子句解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sql_deep_parser import DeepSQLParser, RuleType, FieldType, AggregationType


def test_simple_having_clause():
    """测试简单的HAVING子句"""
    print("=== 测试简单HAVING子句 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    SELECT 结算单据号, SUM(数量) as 总数量
    FROM 医保住院结算明细
    WHERE 医保项目名称 = '血液透析'
    GROUP BY 结算单据号
    HAVING SUM(数量) > 2
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        print(f"聚合函数数量: {len(rule_ir.aggregations)}")
        print(f"条件数量: {len(rule_ir.conditions)}")
        
        # 检查聚合函数
        print("\n聚合函数:")
        for i, agg in enumerate(rule_ir.aggregations):
            print(f"  {i+1}. {agg.function.value}({agg.field.field_name}) AS {agg.alias}")
        
        # 检查条件
        print("\n条件:")
        for i, cond in enumerate(rule_ir.conditions):
            print(f"  {i+1}. {cond.field.field_name} {cond.operator.value} {cond.value}")
            print(f"      字段类型: {cond.field.field_type.value}")
        
        # 检查是否识别为超量使用规则
        expected_type = RuleType.EXCESSIVE_USAGE
        print(f"\n规则类型识别正确: {rule_ir.rule_type == expected_type}")
        
        return rule_ir.rule_type == expected_type
    else:
        print(f"解析失败: {result.error_message}")
        return False


def test_complex_aggregation():
    """测试复杂聚合查询"""
    print("\n=== 测试复杂聚合查询 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    WITH tab1 AS (
        SELECT 结算单据号, 项目使用日期::DATE, 医保项目名称,
               SUM(数量) AS 使用总数量,
               COUNT(*) AS 使用次数,
               AVG(金额) AS 平均金额,
               (SUM(数量) - 1.5) AS 违规数量
        FROM 医保住院结算明细 B
        WHERE B.医保项目名称 IN ('经皮椎体成形术')
        GROUP BY 结算单据号, 项目使用日期::DATE, 医保项目名称
        HAVING SUM(数量) > 1.5 AND COUNT(*) > 1
    )
    SELECT * FROM 医保住院结算明细 B
    JOIN tab1 C ON B.结算单据号 = C.结算单据号
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        print(f"聚合函数数量: {len(rule_ir.aggregations)}")
        print(f"条件数量: {len(rule_ir.conditions)}")
        
        # 检查聚合函数
        print("\n聚合函数:")
        for i, agg in enumerate(rule_ir.aggregations):
            print(f"  {i+1}. {agg.function.value}({agg.field.field_name})")
            if agg.alias:
                print(f"      别名: {agg.alias}")
        
        # 检查HAVING条件
        having_conditions = [cond for cond in rule_ir.conditions 
                           if 'SUM(' in cond.field.field_name or 'COUNT(' in cond.field.field_name]
        print(f"\nHAVING条件数量: {len(having_conditions)}")
        for i, cond in enumerate(having_conditions):
            print(f"  {i+1}. {cond.field.field_name} {cond.operator.value} {cond.value}")
        
        return len(rule_ir.aggregations) >= 3 and len(having_conditions) >= 2
    else:
        print(f"解析失败: {result.error_message}")
        return False


def test_quantity_threshold_detection():
    """测试数量阈值检测"""
    print("\n=== 测试数量阈值检测 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    SELECT 结算单据号, 医保项目名称, SUM(数量) as 总数量
    FROM 医保住院结算明细
    WHERE 医保项目名称 = '某检查项目'
    GROUP BY 结算单据号, 医保项目名称
    HAVING SUM(数量) >= 3
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        
        # 查找数量相关的HAVING条件
        quantity_conditions = []
        for cond in rule_ir.conditions:
            if (cond.field.field_type == FieldType.QUANTITY or 
                '数量' in cond.field.field_name):
                quantity_conditions.append(cond)
        
        print(f"数量相关条件数量: {len(quantity_conditions)}")
        for i, cond in enumerate(quantity_conditions):
            print(f"  {i+1}. {cond.field.field_name} {cond.operator.value} {cond.value}")
        
        # 检查是否识别为超量使用规则
        is_excessive_usage = rule_ir.rule_type == RuleType.EXCESSIVE_USAGE
        print(f"识别为超量使用规则: {is_excessive_usage}")
        
        return is_excessive_usage and len(quantity_conditions) > 0
    else:
        print(f"解析失败: {result.error_message}")
        return False


def test_multiple_aggregation_functions():
    """测试多种聚合函数"""
    print("\n=== 测试多种聚合函数 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    SELECT 
        结算单据号,
        SUM(数量) as 总数量,
        COUNT(*) as 记录数,
        AVG(金额) as 平均金额,
        MAX(金额) as 最大金额,
        MIN(金额) as 最小金额
    FROM 医保住院结算明细
    WHERE 医保项目名称 = '测试项目'
    GROUP BY 结算单据号
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"聚合函数数量: {len(rule_ir.aggregations)}")
        
        # 检查各种聚合函数类型
        agg_types = {}
        for agg in rule_ir.aggregations:
            agg_type = agg.function
            if agg_type not in agg_types:
                agg_types[agg_type] = 0
            agg_types[agg_type] += 1
        
        print("聚合函数类型统计:")
        for agg_type, count in agg_types.items():
            print(f"  {agg_type.value}: {count}")
        
        # 检查是否包含所有预期的聚合函数类型
        expected_types = {
            AggregationType.SUM, AggregationType.COUNT, 
            AggregationType.AVG, AggregationType.MAX, AggregationType.MIN
        }
        found_types = set(agg_types.keys())
        
        print(f"找到的聚合函数类型: {len(found_types)}")
        print(f"预期的聚合函数类型: {len(expected_types)}")
        
        return len(found_types) >= 4  # 至少找到4种聚合函数
    else:
        print(f"解析失败: {result.error_message}")
        return False


def main():
    """主测试函数"""
    print("开始深度SQL解析器第三步测试...\n")
    
    tests = [
        ("简单HAVING子句", test_simple_having_clause),
        ("复杂聚合查询", test_complex_aggregation),
        ("数量阈值检测", test_quantity_threshold_detection),
        ("多种聚合函数", test_multiple_aggregation_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 第三步测试全部通过！可以继续下一步开发。")
    else:
        print("⚠️  存在失败的测试，需要修复后再继续。")


if __name__ == "__main__":
    main()
