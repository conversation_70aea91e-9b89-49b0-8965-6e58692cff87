#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试医院已采用规则API接口
"""

import requests
import json
import sys

def test_adopted_rules_api():
    """测试已采用规则API"""
    base_url = 'http://localhost:5001'
    
    print("=== 测试医院已采用规则API ===")
    
    # 1. 首先获取医院列表
    try:
        response = requests.get(f'{base_url}/api/hospitals', timeout=10)
        print(f'获取医院列表 - 状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('hospitals'):
                hospitals = data['hospitals']
                print(f'找到 {len(hospitals)} 家医院')
                
                # 测试第一家医院的已采用规则
                if hospitals:
                    hospital = hospitals[0]
                    hospital_id = hospital['医院ID']
                    hospital_name = hospital['医院名称']
                    print(f'测试医院: {hospital_name} (ID: {hospital_id})')
                    
                    # 2. 测试获取已采用规则API
                    try:
                        adopted_url = f'{base_url}/api/hospital-rules/adopted/{hospital_id}'
                        print(f'请求URL: {adopted_url}')
                        
                        response = requests.get(adopted_url, timeout=10)
                        print(f'获取已采用规则 - 状态码: {response.status_code}')
                        print(f'响应头: {dict(response.headers)}')
                        
                        if response.status_code == 200:
                            try:
                                data = response.json()
                                print(f'响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}')
                                
                                if data.get('success'):
                                    rules = data.get('rules', [])
                                    print(f'已采用规则数量: {len(rules)}')
                                    
                                    if rules:
                                        print("前3条规则详情:")
                                        for i, rule in enumerate(rules[:3]):
                                            print(f"  规则 {i+1}:")
                                            print(f"    适用ID: {rule.get('适用ID')}")
                                            print(f"    规则ID: {rule.get('规则ID')}")
                                            print(f"    规则名称: {rule.get('规则名称')}")
                                            print(f"    行为认定: {rule.get('行为认定')}")
                                            print(f"    匹配度: {rule.get('匹配度')}")
                                            print(f"    城市: {rule.get('城市')}")
                                            print(f"    规则来源: {rule.get('规则来源')}")
                                    else:
                                        print("该医院暂无已采用规则")
                                else:
                                    print(f'API返回失败: {data.get("error")}')
                            except json.JSONDecodeError as e:
                                print(f'JSON解析失败: {e}')
                                print(f'原始响应: {response.text[:500]}...')
                        else:
                            print(f'HTTP错误: {response.status_code}')
                            print(f'错误内容: {response.text[:500]}...')
                            
                    except requests.exceptions.RequestException as e:
                        print(f'请求异常: {e}')
                    except Exception as e:
                        print(f'其他异常: {e}')
                        
                else:
                    print("没有找到医院数据")
            else:
                print(f'获取医院列表失败: {data.get("error") if data else "无响应数据"}')
        else:
            print(f'获取医院列表HTTP错误: {response.status_code}')
            print(f'错误内容: {response.text[:500]}...')
            
    except requests.exceptions.RequestException as e:
        print(f'请求医院列表异常: {e}')
    except Exception as e:
        print(f'其他异常: {e}')

def test_specific_hospital(hospital_id):
    """测试指定医院的已采用规则"""
    base_url = 'http://localhost:5001'
    
    print(f"=== 测试医院ID {hospital_id} 的已采用规则 ===")
    
    try:
        adopted_url = f'{base_url}/api/hospital-rules/adopted/{hospital_id}'
        print(f'请求URL: {adopted_url}')
        
        response = requests.get(adopted_url, timeout=10)
        print(f'状态码: {response.status_code}')
        print(f'响应头: {dict(response.headers)}')
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f'响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}')
            except json.JSONDecodeError as e:
                print(f'JSON解析失败: {e}')
                print(f'原始响应: {response.text}')
        else:
            print(f'HTTP错误: {response.status_code}')
            print(f'错误内容: {response.text}')
            
    except Exception as e:
        print(f'异常: {e}')

if __name__ == '__main__':
    if len(sys.argv) > 1:
        # 如果提供了医院ID参数，测试指定医院
        try:
            hospital_id = int(sys.argv[1])
            test_specific_hospital(hospital_id)
        except ValueError:
            print("医院ID必须是数字")
    else:
        # 否则运行完整测试
        test_adopted_rules_api()
