WITH tab1 AS (
  SELECT `结算单据号` FROM ZZS_YB_ZDYFY_9LY.`医保住院结算明细` WHERE `医保项目编码` = '002705000020000-270500002'
 and `结算单据号` in
 (SELECT `结算单据号` FROM ZZS_YB_ZDYFY_9LY.`医保住院结算明细` WHERE `医保项目编码` in ('002704000010000-270400001') )
)
SELECT
  A.`病案号` `病案号`,
  A.`结算单据号` `结算单据号`,
  A.`医疗机构编码` `医疗机构编码`,
  A.`医疗机构名称` `医疗机构名称`,
  A.`结算日期` `结算日期`,
  A.`住院号` `住院号`,
  A.`个人编码` `个人编码`,
  A.`患者社会保障号码` `患者社会保障号码`,
  A.`身份证号` `身份证号`,
  A.`险种类型` `险种类型`,
  A.`入院科室` `入院科室`,
  A.`出院科室` `出院科室`,
  A.`主诊医师姓名` `主诊医师姓名`,
  A.`患者姓名` `患者姓名`,
  A.`患者年龄` `患者年龄`,
  A.`患者性别` `患者性别`,
  A.`异地标志` `异地标志`,
  A.`入院日期` `入院日期`,
  A.`出院日期` `出院日期`,
  toDate(A.`出院日期`) - toDate(A.`入院日期`) as `aa`,
  A.`医疗总费用` `医疗总费用`,
  A.`基本统筹支付` `基本统筹支付`,
  A.`个人自付` `个人自付`,
  A.`个人自费` `个人自费`,
  A.`符合基本医疗保险的费用` `符合基本医疗保险的费用`,
  A.`入院诊断编码` `入院诊断编码`,
  A.`入院诊断名称` `入院诊断名称`,
  A.`出院诊断编码` `出院诊断编码`,
  A.`出院诊断名称` `出院诊断名称`,
  A.`主手术及操作编码` `主手术及操作编码`,
  A.`主手术及操作名称` `主手术及操作名称`,
  A.`其他手术及操作编码` `其他手术及操作编码`,
  A.`其他手术及操作名称` `其他手术及操作名称`,
  B.`开单科室名称` `开单科室名称`,
  B.`执行科室名称` `执行科室名称`,
  B.`开单医师姓名` `开单医师姓名`, 
  B.`费用类别` `费用类别`,
  B.`项目使用日期` `项目使用日期`,
  B.`医院项目编码` `医院项目编码`,
  B.`医院项目名称` `医院项目名称`,
  B.`医保项目编码` `医保项目编码`,
  B.`医保项目名称` `医保项目名称`,
  B.`支付类别` `支付类别`,
  B.`报销比例` `报销比例`, 
  B.`自付比例` `自付比例`,
  B.`支付地点类别` `支付地点类别`,
  B.`记账流水号` `记账流水号`,
  B.`规格` `规格`,
  B.`单价` `单价`,
  B.`数量` `数量`,
  B.`金额` `金额`,
  B.`医保范围内金额` `医保范围内金额`,
CASE WHEN B.`医保项目编码` in ('002704000010000-270400001') THEN B.`数量` ELSE 0 END AS `使用总数量`,
CASE WHEN B.`医保项目编码` in ('002704000010000-270400001') THEN B.`金额` ELSE 0 END AS `使用总金额`,
CASE WHEN B.`医保项目编码` in ('002704000010000-270400001') THEN B.`数量` ELSE 0 END AS `违规数量`,
CASE WHEN B.`医保项目编码` in ('002704000010000-270400001') THEN B.`金额` ELSE 0 END AS `违规金额`
FROM
 ZZS_YB_ZDYFY_9LY.`医保住院结算明细` B
  join tab1 on B.`结算单据号` = tab1.`结算单据号`
   join ZZS_YB_ZDYFY_9LY.`医保住院结算主单` A on A.`结算单据号` = B.`结算单据号` where `违规数量`>0
GROUP BY
  A.`病案号`,
  A.`结算单据号`,
  A.`医疗机构编码`,
  A.`医疗机构名称`,
  A.`结算日期`,
  A.`住院号`,
  A.`个人编码`