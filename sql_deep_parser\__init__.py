"""
深度SQL规则解析器
基于AST的SQL语义分析和业务逻辑提取
"""

from .models import (
    RuleLogicIR, Condition, FieldReference, DataSource,
    RuleType, FieldType, OperatorType, LogicType,
    AgeRange, DuplicateBillingPattern, ParseResult,
    Aggregation, AggregationType
)
from .parser import DeepSQLParser

__version__ = "1.0.0"
__all__ = [
    "DeepSQLParser",
    "RuleLogicIR",
    "Condition",
    "FieldReference",
    "DataSource",
    "RuleType",
    "FieldType",
    "OperatorType",
    "LogicType",
    "AgeRange",
    "DuplicateBillingPattern",
    "ParseResult",
    "Aggregation",
    "AggregationType"
]
