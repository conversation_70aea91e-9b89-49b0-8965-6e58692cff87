#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复规则检测算法重构测试脚本

测试重构后的detect_duplicate_rules函数：
1. 基于对照ID和医保名称字段的重复检测
2. 分隔符拆分和索引构建
3. 交集比较和分组算法

使用方法：
python test_duplicate_rules_refactor.py [hospital_id]

作者: Augment Agent
日期: 2025-07-21
"""

import sys
import requests
import json
from datetime import datetime

def test_duplicate_analysis_api(hospital_id=1):
    """测试重构后的重复规则分析API"""
    url = f"http://localhost:5000/api/hospital-rules/duplicate-analysis/{hospital_id}"
    
    print(f"测试重构后的重复规则分析API")
    print(f"URL: {url}")
    print("=" * 60)
    
    try:
        response = requests.get(url, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✓ API调用成功")
                print(f"总规则数: {data.get('total_rules', 0)}")
                print(f"重复规则数: {data.get('duplicate_rules', 0)}")
                print(f"重复组数: {data.get('duplicate_groups_count', 0)}")
                
                # 显示重复组详情
                groups = data.get('duplicate_groups', [])
                if groups:
                    print(f"\n重复组详情:")
                    for i, group in enumerate(groups):
                        print(f"\n组 {i+1}:")
                        print(f"  - 规则数量: {group.get('rule_count', 0)}")
                        print(f"  - 相似度: {group.get('similarity', 0):.2f}")
                        print(f"  - 共同医保项目: {', '.join(group.get('common_medical_names', []))}")
                        print(f"  - 对照ID: {group.get('compare_ids', [])}")
                        
                        # 显示规则详情
                        rules = group.get('rules', [])
                        print(f"  - 包含规则:")
                        for rule in rules[:3]:  # 只显示前3条
                            print(f"    * 适用ID: {rule.get('适用ID')}, 对照ID: {rule.get('对照ID')}")
                            print(f"      规则名称: {rule.get('规则名称', 'N/A')}")
                            print(f"      医保名称1: {rule.get('医保名称1', 'N/A')}")
                            print(f"      医保名称2: {rule.get('医保名称2', 'N/A')}")
                        
                        if len(rules) > 3:
                            print(f"    ... 还有 {len(rules) - 3} 条规则")
                else:
                    print("\n没有发现重复规则")
                
                return True
            else:
                print(f"✗ API返回错误: {data.get('error')}")
                if data.get('details'):
                    print(f"详细错误信息:")
                    print(data.get('details'))
                return False
        else:
            print(f"✗ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return False

def test_algorithm_logic():
    """测试算法逻辑的理论验证"""
    print("\n算法逻辑验证:")
    print("=" * 60)
    
    print("重构后的算法特点:")
    print("1. ✓ 基于对照ID进行分组，而不是适用ID")
    print("2. ✓ 分别处理医保名称1和医保名称2字段")
    print("3. ✓ 使用分隔符（、,，|;；）拆分医保项目名称")
    print("4. ✓ 构建医保项目名称到对照ID的索引")
    print("5. ✓ 对医保名称1和医保名称2分别进行交集比较")
    print("6. ✓ 使用并查集算法对有共同医保项目的对照ID分组")
    
    print("\n预期改进效果:")
    print("- 更准确的重复检测：基于实际的对照关系")
    print("- 更合理的分组：相同对照ID下的规则自然归组")
    print("- 更好的性能：减少不必要的规则间比较")
    print("- 更清晰的逻辑：分离医保名称1和医保名称2的处理")

def test_frontend_changes():
    """测试前端界面改进"""
    print("\n前端界面改进验证:")
    print("=" * 60)
    
    print("界面改进内容:")
    print("1. ✓ 重复规则分组默认设置为折叠状态")
    print("2. ✓ 在'清除选择'按钮后增加'批量取消采用'按钮")
    print("3. ✓ 添加折叠/展开时的图标切换动画")
    print("4. ✓ 改进按钮状态同步逻辑")
    
    print("\n手动验证步骤:")
    print("1. 打开医院规则管理页面")
    print("2. 选择医院，点击'已采用'")
    print("3. 点击'重复规则审查'")
    print("4. 验证分组默认为折叠状态")
    print("5. 验证展开/折叠图标正确切换")
    print("6. 验证批量取消采用按钮功能")

def compare_old_vs_new():
    """对比新旧算法的差异"""
    print("\n新旧算法对比:")
    print("=" * 60)
    
    print("旧算法（基于适用ID）:")
    print("- 直接比较规则的医保名称字段")
    print("- 以适用ID为单位进行分组")
    print("- 可能将不相关的规则归为一组")
    print("- 算法复杂度较高")
    
    print("\n新算法（基于对照ID）:")
    print("- 先构建对照ID索引，再比较医保名称")
    print("- 以对照ID为单位进行分组")
    print("- 更准确地识别真正的重复关系")
    print("- 算法逻辑更清晰，性能更好")
    
    print("\n关键改进:")
    print("1. 数据索引构建：医保项目名称 → 对照ID列表")
    print("2. 分离处理：医保名称1和医保名称2独立处理")
    print("3. 交集比较：在对照ID级别进行重复检测")
    print("4. 分组优化：基于对照ID的连通性分组")

def main():
    """主测试函数"""
    hospital_id = 1
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("重复规则检测算法重构测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试医院ID: {hospital_id}")
    print("=" * 60)
    
    try:
        # 1. 测试重构后的API
        api_success = test_duplicate_analysis_api(hospital_id)
        
        # 2. 验证算法逻辑
        test_algorithm_logic()
        
        # 3. 测试前端改进
        test_frontend_changes()
        
        # 4. 对比新旧算法
        compare_old_vs_new()
        
        print("\n" + "=" * 60)
        if api_success:
            print("✓ 重构测试完成，API功能正常")
        else:
            print("✗ 重构测试发现问题，请检查API实现")
        print("请手动验证前端界面改进效果")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
