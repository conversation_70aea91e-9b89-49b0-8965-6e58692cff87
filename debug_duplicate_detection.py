#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复检测调试脚本

调试具体案例：规则ID 1696已被采用，规则ID 2023应该显示重复信息
医保名称1：言语能力评定

使用方法：
python debug_duplicate_detection.py [hospital_id]

作者: Augment Agent
日期: 2025-07-22
"""

import sys
import requests
import json
from datetime import datetime

def debug_specific_case(hospital_id=1):
    """调试具体的重复检测案例"""
    print("=" * 80)
    print("调试: 规则ID 1696 vs 2023 重复检测")
    print("=" * 80)

    print("通过API测试重复检测功能...")
    return True
                
                # 1. 检查规则ID 1696的状态和信息
                print("1. 检查规则ID 1696（应该已被采用）:")
                rule_1696_query = """
                SELECT h.适用ID, h.规则ID, r.规则名称, r.类型, h.状态, h.匹配项目,
                       c.医保名称1, c.医保名称2, c.城市, c.规则来源
                FROM 医院适用规则表 h
                JOIN 飞检规则知识库 r ON h.规则ID = r.ID
                LEFT JOIN 规则医保编码对照 c ON h.对照ID = c.对照ID
                WHERE h.医院ID = :hospital_id AND h.规则ID = 1696
                """
                
                cursor.execute(rule_1696_query, {'hospital_id': hospital_id})
                rule_1696_results = cursor.fetchall()
                
                if rule_1696_results:
                    for result in rule_1696_results:
                        print(f"  适用ID: {result[0]}")
                        print(f"  规则ID: {result[1]}")
                        print(f"  规则名称: {result[2]}")
                        print(f"  类型: {result[3]}")
                        print(f"  状态: {result[4]}")
                        print(f"  匹配项目: {result[5]}")
                        print(f"  医保名称1: {result[6]}")
                        print(f"  医保名称2: {result[7]}")
                        print(f"  城市: {result[8]}")
                        print(f"  规则来源: {result[9]}")
                        print()
                else:
                    print("  ❌ 未找到规则ID 1696的记录")
                
                # 2. 检查规则ID 2023的信息
                print("2. 检查规则ID 2023（推荐规则）:")
                rule_2023_query = """
                SELECT h.适用ID, h.规则ID, r.规则名称, r.规则类型, h.状态, h.匹配项目,
                       c.医保名称1, c.医保名称2, c.城市, c.规则来源
                FROM 医院适用规则表 h
                JOIN 飞检规则知识库 r ON h.规则ID = r.ID
                LEFT JOIN 规则医保编码对照 c ON h.对照ID = c.对照ID
                WHERE h.医院ID = :hospital_id AND h.规则ID = 2023
                """
                
                cursor.execute(rule_2023_query, {'hospital_id': hospital_id})
                rule_2023_results = cursor.fetchall()
                
                if rule_2023_results:
                    for result in rule_2023_results:
                        print(f"  适用ID: {result[0]}")
                        print(f"  规则ID: {result[1]}")
                        print(f"  规则名称: {result[2]}")
                        print(f"  规则类型: {result[3]}")
                        print(f"  状态: {result[4]}")
                        print(f"  匹配项目: {result[5]}")
                        print(f"  医保名称1: {result[6]}")
                        print(f"  医保名称2: {result[7]}")
                        print(f"  城市: {result[8]}")
                        print(f"  规则来源: {result[9]}")
                        print()
                else:
                    print("  ❌ 未找到规则ID 2023的记录")
                
                # 3. 检查两个规则的类型是否相同
                print("3. 检查规则类型匹配:")
                if rule_1696_results and rule_2023_results:
                    rule_1696_type = rule_1696_results[0][3]  # 规则类型
                    rule_2023_type = rule_2023_results[0][3]  # 规则类型
                    
                    print(f"  规则ID 1696 类型: {rule_1696_type}")
                    print(f"  规则ID 2023 类型: {rule_2023_type}")
                    print(f"  类型匹配: {'✓' if rule_1696_type == rule_2023_type else '❌'}")
                    print()
                
                # 4. 检查医保名称交集
                print("4. 检查医保名称交集:")
                if rule_1696_results and rule_2023_results:
                    import re
                    
                    # 获取规则1696的医保名称
                    rule_1696_names = set()
                    if rule_1696_results[0][6]:  # 医保名称1
                        names = [name.strip() for name in re.split(r'[、,，|;；]', rule_1696_results[0][6]) if name.strip()]
                        rule_1696_names.update(names)
                        print(f"  规则ID 1696 医保名称1: {rule_1696_results[0][6]}")
                        print(f"  解析后: {names}")
                    
                    if rule_1696_results[0][7]:  # 医保名称2
                        names = [name.strip() for name in re.split(r'[、,，|;；]', rule_1696_results[0][7]) if name.strip()]
                        rule_1696_names.update(names)
                        print(f"  规则ID 1696 医保名称2: {rule_1696_results[0][7]}")
                        print(f"  解析后: {names}")
                    
                    print(f"  规则ID 1696 所有医保名称: {rule_1696_names}")
                    print()
                    
                    # 获取规则2023的医保名称
                    rule_2023_names = set()
                    if rule_2023_results[0][6]:  # 医保名称1
                        names = [name.strip() for name in re.split(r'[、,，|;；]', rule_2023_results[0][6]) if name.strip()]
                        rule_2023_names.update(names)
                        print(f"  规则ID 2023 医保名称1: {rule_2023_results[0][6]}")
                        print(f"  解析后: {names}")
                    
                    if rule_2023_results[0][7]:  # 医保名称2
                        names = [name.strip() for name in re.split(r'[、,，|;；]', rule_2023_results[0][7]) if name.strip()]
                        rule_2023_names.update(names)
                        print(f"  规则ID 2023 医保名称2: {rule_2023_results[0][7]}")
                        print(f"  解析后: {names}")
                    
                    print(f"  规则ID 2023 所有医保名称: {rule_2023_names}")
                    print()
                    
                    # 计算交集
                    common_names = rule_1696_names.intersection(rule_2023_names)
                    print(f"  医保名称交集: {common_names}")
                    print(f"  是否有重复: {'✓' if common_names else '❌'}")
                    
                    if common_names:
                        print(f"  重复项目: {list(common_names)}")
                        if "言语能力评定" in common_names:
                            print("  ✓ 确认包含'言语能力评定'")
                        else:
                            print("  ❌ 未包含'言语能力评定'")
                    print()
                
                # 5. 测试重复检测函数
                print("5. 测试重复检测函数:")
                if rule_2023_results:
                    rule_2023_category = rule_2023_results[0][3]  # 规则类型
                    rule_2023_med_name1 = rule_2023_results[0][6]  # 医保名称1
                    rule_2023_med_name2 = rule_2023_results[0][7]  # 医保名称2
                    
                    print(f"  调用参数:")
                    print(f"    hospital_id: {hospital_id}")
                    print(f"    rule_category: {rule_2023_category}")
                    print(f"    med_names1: {rule_2023_med_name1}")
                    print(f"    med_names2: {rule_2023_med_name2}")
                    print()
                    
                    # 模拟重复检测函数的查询
                    duplicate_query = """
                    SELECT h.适用ID, h.规则ID, r.规则名称, r.规则类型, h.匹配项目,
                           c.医保名称1, c.医保名称2, c.城市, c.规则来源
                    FROM 医院适用规则表 h
                    JOIN 飞检规则知识库 r ON h.规则ID = r.ID
                    LEFT JOIN 规则医保编码对照 c ON h.对照ID = c.对照ID
                    WHERE h.医院ID = :hospital_id AND h.状态 = '已采用' AND r.规则类型 = :rule_category
                    """
                    
                    cursor.execute(duplicate_query, {
                        'hospital_id': hospital_id,
                        'rule_category': rule_2023_category
                    })
                    adopted_rules = cursor.fetchall()
                    
                    print(f"  查询到的已采用规则数量: {len(adopted_rules)}")
                    
                    for adopted_rule in adopted_rules:
                        print(f"    规则ID: {adopted_rule[1]}, 名称: {adopted_rule[2]}, 医保名称1: {adopted_rule[5]}, 医保名称2: {adopted_rule[6]}")
                    
                    print()
                    
                    # 检查是否包含规则1696
                    rule_1696_found = any(rule[1] == 1696 for rule in adopted_rules)
                    print(f"  已采用规则中是否包含规则ID 1696: {'✓' if rule_1696_found else '❌'}")
                    
                    if not rule_1696_found:
                        print("  ❌ 问题：规则ID 1696未在已采用规则查询结果中")
                        print("     可能原因：")
                        print("     1. 规则ID 1696状态不是'已采用'")
                        print("     2. 规则类型不匹配")
                        print("     3. 医院ID不匹配")
                
                return True
                
    except Exception as e:
        print(f"❌ 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_response(hospital_id=1):
    """测试API响应中的重复信息"""
    print("\n" + "=" * 80)
    print("测试: API响应中的重复信息")
    print("=" * 80)
    
    base_url = "http://localhost:5000"
    
    try:
        print(f"调用推荐规则生成API，医院ID: {hospital_id}")
        
        response = requests.post(f"{base_url}/api/hospital-rules/generate", 
                               json={"hospital_id": hospital_id}, 
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                recommendations = result.get('recommendations', [])
                
                print(f"✓ API调用成功，推荐规则数量: {len(recommendations)}")
                
                # 查找规则ID 2023
                rule_2023 = None
                for rec in recommendations:
                    if rec.get('规则ID') == 2023:
                        rule_2023 = rec
                        break
                
                if rule_2023:
                    print(f"\n✓ 找到规则ID 2023:")
                    print(f"  规则名称: {rule_2023.get('规则名称')}")
                    print(f"  规则类型: {rule_2023.get('规则类型')}")
                    print(f"  状态: {rule_2023.get('状态')}")
                    print(f"  匹配项目: {rule_2023.get('匹配项目')}")
                    
                    duplicate_rules = rule_2023.get('重复已采用规则', [])
                    print(f"  重复已采用规则数量: {len(duplicate_rules)}")
                    
                    if duplicate_rules:
                        print(f"  重复规则详情:")
                        for i, dup_rule in enumerate(duplicate_rules, 1):
                            print(f"    {i}. 规则ID: {dup_rule.get('规则ID')}")
                            print(f"       规则名称: {dup_rule.get('规则名称')}")
                            print(f"       规则类型: {dup_rule.get('规则类型')}")
                            print(f"       重复项目: {dup_rule.get('重复项目')}")
                            
                            if dup_rule.get('规则ID') == 1696:
                                print(f"       ✓ 找到规则ID 1696!")
                                if '言语能力评定' in dup_rule.get('重复项目', []):
                                    print(f"       ✓ 确认包含'言语能力评定'")
                                else:
                                    print(f"       ❌ 未包含'言语能力评定'")
                    else:
                        print(f"  ❌ 未找到重复已采用规则")
                        print(f"     这表明重复检测功能可能存在问题")
                else:
                    print(f"❌ 未找到规则ID 2023")
                    print(f"   可能原因：")
                    print(f"   1. 规则ID 2023不存在")
                    print(f"   2. 规则ID 2023未被推荐给该医院")
                    print(f"   3. 推荐生成逻辑有问题")
                
                return True
            else:
                print(f"❌ API返回错误: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主调试函数"""
    hospital_id = 1
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("重复检测调试")
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"医院ID: {hospital_id}")
    print(f"测试案例: 规则ID 1696（已采用）vs 规则ID 2023（推荐）")
    print(f"预期重复项目: 言语能力评定")
    
    try:
        # 调试1: 数据库直接查询
        success1 = debug_specific_case(hospital_id)
        
        # 调试2: API响应测试
        success2 = test_api_response(hospital_id)
        
        # 输出调试结果
        print("\n" + "=" * 80)
        print("调试结果汇总")
        print("=" * 80)
        
        print(f"数据库查询调试: {'✓ 通过' if success1 else '❌ 失败'}")
        print(f"API响应测试: {'✓ 通过' if success2 else '❌ 失败'}")
        
        if success1 and success2:
            print("\n✅ 调试完成，请检查上述输出找出问题原因")
        else:
            print("\n❌ 调试过程中发现问题，请检查错误信息")
            
        return success1 and success2
            
    except Exception as e:
        print(f"\n❌ 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
