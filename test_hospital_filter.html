<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试医院筛选功能</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>测试医院筛选功能</h2>
        
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="hospitalSelect" class="form-label">选择医院</label>
                <select class="form-select" id="hospitalSelect">
                    <option value="">请选择医院</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button class="btn btn-primary" onclick="testHospitalFilter()">
                        <i class="bi bi-search"></i> 测试医院筛选
                    </button>
                    <button class="btn btn-secondary" onclick="testGetCompareIds()">
                        <i class="bi bi-list"></i> 测试获取对照ID
                    </button>
                </div>
            </div>
        </div>
        
        <div id="resultSection" class="mt-4">
            <!-- 结果将显示在这里 -->
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 页面初始化
        $(document).ready(function() {
            loadHospitals();
        });

        // 显示提示消息
        function showMessage(message, type = 'info') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.getElementById('resultSection').prepend(alert);
        }

        // 加载医院列表
        function loadHospitals() {
            fetch('/api/hospitals')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = document.getElementById('hospitalSelect');
                        select.innerHTML = '<option value="">请选择医院</option>';
                        data.hospitals.forEach(hospital => {
                            const option = document.createElement('option');
                            option.value = hospital.医院ID;
                            option.textContent = hospital.医院名称;
                            select.appendChild(option);
                        });
                        showMessage(`成功加载 ${data.hospitals.length} 家医院`, 'success');
                    } else {
                        showMessage('加载医院列表失败: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    console.error('加载医院列表失败:', error);
                    showMessage('加载医院列表失败', 'danger');
                });
        }

        // 测试医院筛选功能
        function testHospitalFilter() {
            const hospitalId = document.getElementById('hospitalSelect').value;
            if (!hospitalId) {
                showMessage('请先选择医院', 'warning');
                return;
            }

            const resultSection = document.getElementById('resultSection');
            resultSection.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在测试医院筛选功能...</p></div>';

            // 步骤1：获取医院已采用规则
            fetch(`/api/hospital-rules/adopted/${hospitalId}`)
                .then(response => response.json())
                .then(data => {
                    console.log('步骤1 - 获取医院已采用规则:', data);
                    
                    if (data.success && data.rules && data.rules.length > 0) {
                        const ruleIds = data.rules.map(rule => rule.规则ID);
                        console.log('提取的规则ID列表:', ruleIds);
                        
                        // 步骤2：根据规则ID获取对照ID
                        return fetch('/api/get_compare_ids_by_rule_ids', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ rule_ids: ruleIds })
                        });
                    } else {
                        throw new Error('该医院没有已采用规则');
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('步骤2 - 获取对照ID列表:', data);
                    
                    if (data.success) {
                        const compareIds = data.compare_ids;
                        
                        // 步骤3：获取系统规则列表并筛选
                        return fetch('/api/sql_history')
                            .then(response => response.json())
                            .then(systemRules => {
                                console.log('步骤3 - 获取系统规则列表:', systemRules.length, '条规则');
                                
                                // 筛选出匹配的规则
                                const matchedRules = systemRules.filter(rule => 
                                    compareIds.includes(rule.compare_id.toString())
                                );
                                
                                console.log('筛选结果:', matchedRules.length, '条匹配规则');
                                
                                displayResults(compareIds, matchedRules);
                            });
                    } else {
                        throw new Error('获取对照ID失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('测试失败:', error);
                    resultSection.innerHTML = `
                        <div class="alert alert-danger">
                            <h5><i class="bi bi-exclamation-triangle"></i> 测试失败</h5>
                            <p>错误信息: ${error.message}</p>
                        </div>
                    `;
                });
        }

        // 测试获取对照ID功能
        function testGetCompareIds() {
            const hospitalId = document.getElementById('hospitalSelect').value;
            if (!hospitalId) {
                showMessage('请先选择医院', 'warning');
                return;
            }

            const resultSection = document.getElementById('resultSection');
            resultSection.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在测试获取对照ID功能...</p></div>';

            // 获取医院已采用规则
            fetch(`/api/hospital-rules/adopted/${hospitalId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.rules && data.rules.length > 0) {
                        const ruleIds = data.rules.map(rule => rule.规则ID);
                        
                        // 测试API
                        return fetch('/api/get_compare_ids_by_rule_ids', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ rule_ids: ruleIds })
                        });
                    } else {
                        throw new Error('该医院没有已采用规则');
                    }
                })
                .then(response => response.json())
                .then(data => {
                    let html = `
                        <div class="alert alert-success">
                            <h5><i class="bi bi-check-circle"></i> API测试成功</h5>
                            <p>成功获取 ${data.compare_ids ? data.compare_ids.length : 0} 个对照ID</p>
                        </div>
                    `;

                    if (data.success && data.compare_ids && data.compare_ids.length > 0) {
                        html += `
                            <div class="card">
                                <div class="card-header">
                                    <h6>对照ID列表 (显示前20个)</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                        `;

                        data.compare_ids.slice(0, 20).forEach((id, index) => {
                            html += `<div class="col-md-2 mb-2"><span class="badge bg-primary">${id}</span></div>`;
                        });

                        html += `
                                    </div>
                                    ${data.compare_ids.length > 20 ? `<p class="text-muted mt-2">还有 ${data.compare_ids.length - 20} 个对照ID未显示</p>` : ''}
                                </div>
                            </div>
                        `;
                    }

                    resultSection.innerHTML = html;
                })
                .catch(error => {
                    console.error('测试失败:', error);
                    resultSection.innerHTML = `
                        <div class="alert alert-danger">
                            <h5><i class="bi bi-exclamation-triangle"></i> 测试失败</h5>
                            <p>错误信息: ${error.message}</p>
                        </div>
                    `;
                });
        }

        // 显示测试结果
        function displayResults(compareIds, matchedRules) {
            let html = `
                <div class="alert alert-success">
                    <h5><i class="bi bi-check-circle"></i> 医院筛选功能测试成功</h5>
                    <p>找到 ${compareIds.length} 个对照ID，匹配 ${matchedRules.length} 条系统规则</p>
                </div>
            `;

            if (matchedRules.length > 0) {
                html += `
                    <div class="card">
                        <div class="card-header">
                            <h6>匹配的系统规则 (显示前10条)</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>对照ID</th>
                                            <th>规则ID</th>
                                            <th>规则名称</th>
                                            <th>城市</th>
                                            <th>规则来源</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                `;

                matchedRules.slice(0, 10).forEach(rule => {
                    html += `
                        <tr>
                            <td>${rule.compare_id}</td>
                            <td>${rule.rule_id}</td>
                            <td>${rule.rule_name}</td>
                            <td>${rule.city}</td>
                            <td>${rule.rule_source}</td>
                        </tr>
                    `;
                });

                html += `
                                    </tbody>
                                </table>
                            </div>
                            ${matchedRules.length > 10 ? `<p class="text-muted mt-2">还有 ${matchedRules.length - 10} 条规则未显示</p>` : ''}
                        </div>
                    </div>
                `;
            }

            document.getElementById('resultSection').innerHTML = html;
        }
    </script>
</body>
</html>
