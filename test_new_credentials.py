#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的用户名密码配置
"""

import urllib.request
import json
import time
from datetime import datetime

def test_new_credentials():
    """测试新的用户名密码配置"""
    base_url = "http://127.0.0.1:5001"
    
    print("=" * 70)
    print("测试新的用户名密码配置")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 1. 测试默认PostgreSQL连接（新密码）
    print("\n🔍 测试1: 默认PostgreSQL连接（用户: postgres, 密码: P@ssw0rd）")
    try:
        test_data = {
            "sql": "SELECT current_user as current_user, version() as version",
            "database": "pg",
            "host": "default"
        }
        
        req_data = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=15) as response:
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                data = json.loads(content)
                
                if data.get('success'):
                    print("✅ PostgreSQL默认连接成功!")
                    print(f"   数据库: {data.get('database', 'Unknown')}")
                    print(f"   主机: {data.get('host', 'Unknown')}")
                    if data.get('data') and len(data['data']) > 0:
                        row = data['data'][0]
                        print(f"   当前用户: {row[0] if len(row) > 0 else 'Unknown'}")
                        print(f"   版本信息: {row[1][:50] if len(row) > 1 else 'Unknown'}...")
                else:
                    print(f"❌ 执行失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.getcode()}")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 2. 测试默认Oracle连接（新用户名密码）
    print("\n🔍 测试2: 默认Oracle连接（用户: datachange, 密码: drgs2019）")
    try:
        test_data = {
            "sql": "SELECT USER as current_user, BANNER as version FROM v$version WHERE ROWNUM = 1",
            "database": "oracle",
            "host": "default"
        }
        
        req_data = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=15) as response:
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                data = json.loads(content)
                
                if data.get('success'):
                    print("✅ Oracle默认连接成功!")
                    print(f"   数据库: {data.get('database', 'Unknown')}")
                    print(f"   主机: {data.get('host', 'Unknown')}")
                    if data.get('data') and len(data['data']) > 0:
                        row = data['data'][0]
                        print(f"   当前用户: {row[0] if len(row) > 0 else 'Unknown'}")
                        print(f"   版本信息: {row[1][:50] if len(row) > 1 else 'Unknown'}...")
                else:
                    print(f"❌ 执行失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.getcode()}")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 3. 测试获取Oracle Schema（使用新用户）
    print("\n🔍 测试3: 获取Oracle Schema列表（使用datachange用户）")
    try:
        test_data = {
            "database": "oracle",
            "host": "default"
        }
        
        req_data = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/database_schemas",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=15) as response:
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                data = json.loads(content)
                
                if data.get('success'):
                    schemas = data.get('schemas', [])
                    print("✅ 获取Oracle Schema列表成功!")
                    print(f"   找到 {len(schemas)} 个用户Schema:")
                    for schema in schemas:
                        print(f"     - {schema}")
                else:
                    print(f"❌ 获取Schema列表失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.getcode()}")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 4. 测试获取PostgreSQL Schema（使用新密码）
    print("\n🔍 测试4: 获取PostgreSQL Schema列表（使用新密码）")
    try:
        test_data = {
            "database": "pg",
            "host": "default"
        }
        
        req_data = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/database_schemas",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=15) as response:
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                data = json.loads(content)
                
                if data.get('success'):
                    schemas = data.get('schemas', [])
                    print("✅ 获取PostgreSQL Schema列表成功!")
                    print(f"   找到 {len(schemas)} 个Schema:")
                    for schema in schemas:
                        print(f"     - {schema}")
                else:
                    print(f"❌ 获取Schema列表失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.getcode()}")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 5. 测试连接信息显示
    print("\n🔍 测试5: 验证连接信息显示")
    print("✅ 前端提示信息已更新:")
    print("   Oracle: 输入Oracle主机IP (默认: 127.0.0.1, 用户: datachange)")
    print("   PostgreSQL: 输入PostgreSQL主机IP (默认: *************, 用户: postgres)")
    
    print("\n" + "=" * 70)
    print("新用户名密码配置测试完成!")

if __name__ == "__main__":
    test_new_credentials()
