<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查找重复文件</title>
    <style>
        :root {
            --primary: #0078D4;
            --primary-hover: #106EBE;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --border-color: rgba(0, 0, 0, 0.1);
            --radius: 12px;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --transition: all 0.2s ease;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        nav {
            margin-bottom: 2rem;
        }

        nav a {
            color: var(--primary);
            text-decoration: none;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: var(--transition);
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav a:hover {
            background-color: rgba(0, 120, 212, 0.1);
        }

        h2 {
            margin-bottom: 1rem;
        }
        .duplicate-list {
            margin-top: 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            background-color: #ffffff;
        }
        .duplicate-item {
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            background-color: #f9fafb;
        }
        .duplicate-item p {
            margin: 0;
        }
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            border: none;
            background-color: #0078D4;
            color: white;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn:hover {
            background-color: #0056a1;
        }
        .message {
            margin: 1rem 0;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            background-color: #e2e8f0;
        }
        .export-section {
            margin-top: 1rem;
        }
        .duplicate-group {
            margin-bottom: 1.5rem;
            padding: 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background-color: #ffffff;
        }
        .group-header {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #0078D4;
        }
        .file-path {
            margin-left: 1rem;
            color: #4a5568;
        }
        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 1rem;
        }
        .input-group input[type="text"] {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
        }
        .input-group input[type="file"] {
            display: none;
        }
        .browse-btn {
            padding: 0.5rem 1rem;
            background-color: #4a5568;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .browse-btn:hover {
            background-color: #2d3748;
        }
        .progress-container {
            display: none;
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background-color: white;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress {
            width: 0%;
            height: 100%;
            background-color: #0078D4;
            transition: width 0.3s ease;
        }
        .progress-text {
            margin-top: 5px;
            text-align: center;
            color: #4a5568;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav>
            <a href="{{ url_for('index') }}">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M9.78 12.78a.75.75 0 01-1.06 0L4.47 8.53a.75.75 0 010-1.06l4.25-4.25a.75.75 0 011.06 1.06L6.06 8l3.72 3.72a.75.75 0 010 1.06z"/>
                </svg>
                返回主页
            </a>
        </nav>    
        <h2>查找重复文件</h2>
        <form id="searchForm" method="post">
            <div class="input-group">
                <input type="text" id="directory" name="directory" required placeholder="请选择或输入目录路径">
                <input type="file" id="folderInput" webkitdirectory directory>
                <button type="button" class="browse-btn" onclick="document.getElementById('folderInput').click()">
                    选择文件夹
                </button>
            </div>
            <button type="submit" class="btn">查找重复文件</button>
        </form>

        <!-- 进度条容器 -->
        <div id="progressContainer" class="progress-container">
            <div class="progress-bar">
                <div id="progress" class="progress"></div>
            </div>
            <div id="progressText" class="progress-text">正在扫描文件...</div>
        </div>
        
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="message">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div id="results">
            {% if duplicate_groups %}
                <div class="duplicate-list">
                    <h3>找到的重复文件 (共 {{ total_groups }} 组):</h3>
                    {% for group in duplicate_groups %}
                        <div class="duplicate-group">
                            <div class="group-header">重复文件组 {{ loop.index }}:</div>
                            {% for file_path in group %}
                                <div class="file-path">{{ file_path }}</div>
                            {% endfor %}
                        </div>
                    {% endfor %}
                    
                    {% if duplicate_file_path %}
                    <div class="export-section">
                        <form action="{{ url_for('export_duplicates') }}" method="post">
                            <input type="hidden" name="file_path" value="{{ duplicate_file_path }}">
                            <button type="submit" class="btn">导出到Excel</button>
                        </form>
                    </div>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        document.getElementById('folderInput').addEventListener('change', function(e) {
            // 获取选择的文件夹路径
            var files = e.target.files;
            if (files.length > 0) {
                // 获取第一个文件的路径并提取文件夹路径
                var path = files[0].webkitRelativePath;
                var folderPath = path.split('/')[0];
                document.getElementById('directory').value = folderPath;
            }
        });

        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const directory = document.getElementById('directory').value;
            const progressContainer = document.getElementById('progressContainer');
            const progress = document.getElementById('progress');
            const progressText = document.getElementById('progressText');
            const results = document.getElementById('results');

            progressContainer.style.display = 'block';
            progress.style.width = '0%';
            results.innerHTML = '';

            // 开始处理文件
            fetch('/find_duplicates', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'directory=' + encodeURIComponent(directory)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'processing') {
                    // 开始轮询进度
                    checkProgress();
                }
            });
        });

        function checkProgress() {
            fetch('/check_progress')
            .then(response => response.json())
            .then(data => {
                const progress = document.getElementById('progress');
                const progressText = document.getElementById('progressText');
                
                progress.style.width = data.progress + '%';
                progressText.textContent = data.message;

                if (data.status !== 'complete') {
                    // 如果还没完成，继续轮询
                    setTimeout(checkProgress, 500);
                } else {
                    // 完成后更新结果
                    document.getElementById('results').innerHTML = data.results;
                }
            });
        }
    </script>
</body>
</html> 