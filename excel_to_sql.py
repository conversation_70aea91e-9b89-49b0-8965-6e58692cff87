import pandas as pd
import os

# 使用原始字符串（raw string）来处理文件路径，或使用正斜杠
excel_path = r'D:\Personal\Desktop\规则整理处理\通用规则.xlsx'  # 使用 r 前缀
# 或者
# excel_path = 'D:/01工作/00飞行检查/02规则备份/sql模板/检验检查规则.xlsx'  # 使用正斜杠

try:
    # 读取Excel文件
    df = pd.read_excel(excel_path)
    
    # 创建一个目录来存储生成的.sql文件
    if not os.path.exists('sql_files'):
        os.makedirs('sql_files')

    # 遍历DataFrame中的每一行
    for index, row in df.iterrows():
        rule_name = str(row['规则名称']).strip().replace('/', '_').replace('\\', '_')  # 替换文件名中不允许的字符
        sql_column = 'SQL' if 'SQL' in row else 'sql'
        rule_content = str(row[sql_column]).strip()
        rule_description = str(row['规则内涵']).strip() if '规则内涵' in row else ''
        policy_basis = str(row['政策依据']).strip() if '政策依据' in row else ''

        # 移除字符串中的空字符
        rule_name = rule_name.replace('\0', '')
        rule_content = rule_content.replace('\0', '')
        rule_description = rule_description.replace('\0', '')
        policy_basis = policy_basis.replace('\0', '')

        # 构造文件名
        filename = f"{rule_name}.sql"

        # 构造SQL文件的内容
        sql_content = f"-- 规则名称: {rule_name}\n"
        if rule_description:
            sql_content += f"-- 规则内涵: {rule_description}\n"
        if policy_basis:
            sql_content += f"-- 政策依据: {policy_basis}\n"
        sql_content += rule_content

        # 写入.sql文件
        with open(os.path.join('sql_files', filename), 'w', encoding='utf-8') as file:
            file.write(sql_content)

    print("所有.sql文件已生成完毕。")
except ValueError as e:
    print(f"读取文件时出错: {e}")
    print("请确保Excel文件路径正确且文件未被损坏")
except Exception as e:
    print(f"发生未知错误: {e}")