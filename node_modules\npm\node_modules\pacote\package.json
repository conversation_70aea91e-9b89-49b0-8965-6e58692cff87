{"name": "pacote", "version": "21.0.0", "description": "JavaScript package downloader", "author": "GitHub Inc.", "bin": {"pacote": "bin/index.js"}, "license": "ISC", "main": "lib/index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "tap": {"timeout": 300, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/arborist": "^8.0.0", "@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.4", "hosted-git-info": "^8.0.0", "mutate-fs": "^2.1.1", "nock": "^13.2.4", "npm-registry-mock": "^1.3.2", "rimraf": "^6.0.1", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "keywords": ["packages", "npm", "git"], "dependencies": {"@npmcli/git": "^6.0.0", "@npmcli/installed-package-contents": "^3.0.0", "@npmcli/package-json": "^6.0.0", "@npmcli/promise-spawn": "^8.0.0", "@npmcli/run-script": "^9.0.0", "cacache": "^19.0.0", "fs-minipass": "^3.0.0", "minipass": "^7.0.2", "npm-package-arg": "^12.0.0", "npm-packlist": "^10.0.0", "npm-pick-manifest": "^10.0.0", "npm-registry-fetch": "^18.0.0", "proc-log": "^5.0.0", "promise-retry": "^2.0.1", "sigstore": "^3.0.0", "ssri": "^12.0.0", "tar": "^6.1.11"}, "engines": {"node": "^20.17.0 || >=22.9.0"}, "repository": {"type": "git", "url": "git+https://github.com/npm/pacote.git"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.4", "windowsCI": false, "publish": "true"}}