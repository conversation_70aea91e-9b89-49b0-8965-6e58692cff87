"""
调试IN条件解析问题
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_simple_in_condition():
    """测试简单的IN条件"""
    print("=== 测试简单IN条件 ===")
    
    sql_content = """
    -- 规则名称: 简单IN条件测试
    -- 城市: 测试城市
    -- 行为认定: 病案提取
    SELECT A.病案号, B.医保项目编码
    FROM 医保门诊结算主单 A
    JOIN 医保门诊结算明细 B ON A.结算单据号 = B.结算单据号
    WHERE B.医保项目编码 IN ('CODE1', 'CODE2', 'CODE3')
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"HTTP状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ 简单IN条件解析成功")
            
            deep_analysis = result['deep_analysis']
            print(f"条件数量: {deep_analysis.get('conditions_count', 'N/A')}")
            
            # 解析JSON输出查看详细信息
            json_output = deep_analysis.get('json_output', '{}')
            try:
                json_obj = json.loads(json_output)
                
                print(f"\n🔍 条件详情:")
                conditions = json_obj.get('conditions', [])
                for i, cond in enumerate(conditions, 1):
                    field = cond.get('field', {})
                    print(f"  {i}. {field.get('field_name', 'N/A')} ({field.get('field_type', 'N/A')}) {cond.get('operator', 'N/A')} {cond.get('value', 'N/A')}")
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
            
            return True
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


def test_clickhouse_in_condition():
    """测试ClickHouse格式的IN条件"""
    print("\n=== 测试ClickHouse IN条件 ===")
    
    sql_content = """
    -- 规则名称: ClickHouse IN条件测试
    -- 城市: 测试城市
    -- 行为认定: 病案提取
    SELECT A.`病案号`, B.`医保项目编码`
    FROM ZZS_YB_ZDYFY_9LY.`医保门诊结算主单` A
    JOIN ZZS_YB_ZDYFY_9LY.`医保门诊结算明细` B ON A.`结算单据号` = B.`结算单据号`
    WHERE B.`医保项目编码` IN (
        'XL03AAJ213B002010104021',
        'XL03AAJ213B002020104089',
        'XL03AAJ213B002010304021'
    )
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"HTTP状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ ClickHouse IN条件解析成功")
            
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"规则类型: {rule_info.get('rule_type', 'N/A')}")
            print(f"条件数量: {deep_analysis.get('conditions_count', 'N/A')}")
            
            # 解析JSON输出查看详细信息
            json_output = deep_analysis.get('json_output', '{}')
            try:
                json_obj = json.loads(json_output)
                
                print(f"\n🔍 条件详情:")
                conditions = json_obj.get('conditions', [])
                for i, cond in enumerate(conditions, 1):
                    field = cond.get('field', {})
                    print(f"  {i}. {field.get('field_name', 'N/A')} ({field.get('field_type', 'N/A')}) {cond.get('operator', 'N/A')} {cond.get('value', 'N/A')}")
                
                # 检查医保项目
                print(f"\n🏥 医保项目:")
                medical_items = rule_info.get('medical_items', [])
                if medical_items:
                    for item in medical_items:
                        print(f"  - {item}")
                else:
                    print("  未检测到医保项目")
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
            
            return True
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


if __name__ == "__main__":
    print("开始调试IN条件解析...\n")
    
    tests = [
        ("简单IN条件", test_simple_in_condition),
        ("ClickHouse IN条件", test_clickhouse_in_condition)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
