// 项目编码规范
const projectRules = {
  // 文件命名规范
  fileNaming: {
    python: {
      modules: "lowercase_with_underscores.py",
      classes: "PascalCase.py",
      tests: "test_*.py",
      config: "config_*.py"
    },
    sql: {
      rules: "rule_*.sql",
      queries: "query_*.sql",
      templates: "template_*.sql"
    },
    static: {
      css: "*.css",
      js: "*.js",
      images: "*.{png,jpg,jpeg,gif}"
    }
  },

  // 目录结构规范
  directoryStructure: {
    root: [
      "app/",
      "templates/",
      "static/",
      "config/",
      "tests/",
      "docs/",
      "logs/",
      "uploads/"
    ],
    app: [
      "models/",
      "routes/",
      "utils/",
      "services/",
      "schemas/",
      "__init__.py"
    ],
    templates: [
      "rule-pg-code/",
      "rule-pg-name/",
      "rule-general/",
      "excel/",
      "manual/"
    ],
    static: [
      "css/",
      "js/",
      "images/"
    ]
  },

  // Python编码规范
  python: {
    // 导入顺序
    imports: [
      "标准库",
      "第三方库",
      "本地模块"
    ],
    // 命名规范
    naming: {
      variables: "lowercase_with_underscores",
      constants: "UPPERCASE_WITH_UNDERSCORES",
      functions: "lowercase_with_underscores",
      classes: "PascalCase",
      private: "_leading_underscore"
    },
    // 代码风格
    style: {
      maxLineLength: 120,
      indentSize: 4,
      docstringStyle: "google",
      quoteStyle: "single"
    }
  },

  // SQL编码规范
  sql: {
    // 命名规范
    naming: {
      tables: "lowercase_with_underscores",
      columns: "lowercase_with_underscores",
      views: "v_lowercase_with_underscores",
      functions: "f_lowercase_with_underscores"
    },
    // 代码风格
    style: {
      keywords: "UPPERCASE",
      indentSize: 2,
      maxLineLength: 100,
      alignment: "left"
    },
    // 查询规范
    query: {
      select: "SELECT\n  column1,\n  column2\nFROM",
      where: "WHERE\n  condition1\n  AND condition2",
      groupBy: "GROUP BY\n  column1,\n  column2",
      orderBy: "ORDER BY\n  column1,\n  column2"
    }
  },

  // 前端编码规范
  frontend: {
    // HTML规范
    html: {
      indentSize: 2,
      maxLineLength: 100,
      selfClosingTags: true,
      attributeQuotes: "double"
    },
    // CSS规范
    css: {
      indentSize: 2,
      maxLineLength: 100,
      selectorFormat: "lowercase-with-hyphens",
      propertyOrder: [
        "position",
        "display",
        "box-sizing",
        "width",
        "height",
        "margin",
        "padding",
        "border",
        "background",
        "color",
        "font",
        "text",
        "transition"
      ]
    },
    // JavaScript规范
    javascript: {
      indentSize: 2,
      maxLineLength: 100,
      quoteStyle: "single",
      semicolons: true
    }
  },

  // 数据库规范
  database: {
    // 表设计规范
    tableDesign: {
      primaryKey: "id",
      timestamps: ["created_at", "updated_at"],
      softDelete: "deleted_at"
    },
    // 索引规范
    indexes: {
      foreignKeys: true,
      uniqueConstraints: true,
      performance: true
    }
  },

  // 文档规范
  documentation: {
    // 代码注释
    comments: {
      functions: "docstring",
      classes: "docstring",
      complexLogic: "inline"
    },
    // 文档格式
    format: {
      readme: "markdown",
      api: "openapi",
      database: "erd"
    }
  },

  // 测试规范
  testing: {
    // 单元测试
    unit: {
      coverage: 80,
      naming: "test_*.py",
      organization: "test_*.py"
    },
    // 集成测试
    integration: {
      coverage: 70,
      naming: "test_integration_*.py",
      organization: "tests/integration/"
    }
  },

  // 版本控制规范
  versionControl: {
    // 分支命名
    branches: {
      main: "main",
      develop: "develop",
      feature: "feature/*",
      bugfix: "bugfix/*",
      release: "release/*"
    },
    // 提交信息
    commits: {
      format: "<type>(<scope>): <subject>",
      types: ["feat", "fix", "docs", "style", "refactor", "test", "chore"]
    }
  }
};

// 项目特定规则
const projectSpecificRules = {
  // SQL规则模板规范
  sqlRuleTemplates: {
    // 模板结构
    structure: {
      withClause: true,
      selectClause: true,
      fromClause: true,
      whereClause: true,
      groupByClause: true,
      orderByClause: true
    },
    // 参数规范
    parameters: {
      format: "{参数名}",
      types: ["医保编码", "排除诊断", "排除科室", "违规数量", "违规金额", "合理疗程", "合理次数"]
    },
    // 注释规范
    comments: {
      header: "/* 规则名称 */",
      parameters: "/* 参数说明 */",
      logic: "/* 逻辑说明 */"
    }
  },

  // 医保规则规范
  medicalInsuranceRules: {
    // 规则分类
    categories: [
      "住院规则",
      "门诊规则",
      "药品规则",
      "检查规则",
      "治疗规则"
    ],
    // 规则参数
    parameters: {
      required: [
        "医保编码",
        "排除诊断",
        "排除科室"
      ],
      optional: [
        "违规数量",
        "违规金额",
        "合理疗程",
        "合理次数"
      ]
    }
  }
};

// 导出规则
module.exports = {
  projectRules,
  projectSpecificRules
}; 