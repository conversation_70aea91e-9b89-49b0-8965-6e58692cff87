WITH tab1 AS (
	SELECT * FROM 医保住院结算明细 WHERE 医保项目名称 = '骨密度测定'  AND 数量 > 3
)
SELECT a.结算单据号,
	a.医疗机构编码,
	a.医疗机构名称,
	a.结算日期,
	a.个人编码,
	a.住院号,
	a.险种类型,
	a.入院科室,
	a.出院科室,
	a.主诊医师姓名,
	a.患者姓名,
	a.患者性别,
	a.患者出生日期,
	a.患者年龄,
	a.入院日期,
	a.出院日期,
	TRUNC(a.出院日期) - TRUNC(a.入院日期)+1,
	a.医疗总费用,
	a.基本统筹支付,
	a.入院诊断名称,
	a.出院诊断名称,
	a.大病保险,
	b.项目使用日期,
	b.医保项目编码,
	b.医保项目名称,
	b.医院项目编码,
	b.医院项目名称,
	b.费用类别,
	b.支付类别,
	b.规格,
	b.单价 FROM 医保住院结算主单 a INNER JOIN  tab1 b ON a.结算单据号 = b.结算单据号

WHERE 1=1 
