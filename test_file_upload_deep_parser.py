"""
测试文件上传和目录批量解析是否使用深度SQL解析器
"""

import requests
import json
import os
import tempfile

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_file_upload_parsing():
    """测试文件上传解析功能"""
    print("=== 测试文件上传解析功能 ===")
    
    # 创建测试SQL文件
    test_sql_content = """
    -- 规则名称: 测试病案提取规则
    -- 城市: 测试城市
    -- 行为认定: 病案提取
    SELECT A.病案号, A.结算单据号, B.医保项目编码, B.医保项目名称
    FROM 医保住院结算明细 B
    JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
    WHERE B.医保项目编码 IN ('ZA07AAX0718010501697')
    AND NOT A.入院诊断编码 IN ('R40.100', 'R40.100x002', 'R40.100x003', 'R40.100x005')
    """
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False, encoding='utf-8') as f:
        f.write(test_sql_content)
        temp_file_path = f.name
    
    try:
        # 准备文件上传
        with open(temp_file_path, 'rb') as f:
            files = {'files': ('test_rule.sql', f, 'text/plain')}
            
            response = requests.post(
                f"{BASE_URL}/api/parse_sql_files",
                files=files
            )
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print("✅ 文件上传解析成功")
                print(f"解析规则数量: {result['total_count']}")
                
                if result['results']:
                    rule = result['results'][0]
                    print(f"规则名称: {rule.get('rule_name', 'N/A')}")
                    print(f"规则类型: {rule.get('rule_type', 'N/A')}")
                    print(f"医保项目: {rule.get('medical_items', [])}")
                    
                    # 检查是否有深度分析结果
                    deep_analysis = rule.get('deep_analysis', {})
                    if deep_analysis:
                        print("\n🔍 深度分析结果:")
                        print(f"  数据源数量: {deep_analysis.get('data_sources_count', 'N/A')}")
                        print(f"  条件数量: {deep_analysis.get('conditions_count', 'N/A')}")
                        print(f"  聚合函数数量: {deep_analysis.get('aggregations_count', 'N/A')}")
                        
                        # 检查JSON输出
                        json_output = deep_analysis.get('json_output', '{}')
                        try:
                            json_obj = json.loads(json_output)
                            conditions = json_obj.get('conditions', [])
                            print(f"  解析的条件:")
                            for i, cond in enumerate(conditions, 1):
                                field = cond.get('field', {})
                                print(f"    {i}. {field.get('field_name', 'N/A')} ({field.get('field_type', 'N/A')}) {cond.get('operator', 'N/A')} {cond.get('value', 'N/A')}")
                            
                            # 验证是否提取了NOT IN条件
                            not_in_found = any(cond.get('operator') == '不包含于' for cond in conditions)
                            if not_in_found:
                                print("  ✅ 成功提取NOT IN条件（使用深度解析器）")
                                return True
                            else:
                                print("  ❌ 未提取NOT IN条件")
                                return False
                        except json.JSONDecodeError:
                            print("  ❌ JSON解析失败")
                            return False
                    else:
                        print("❌ 没有深度分析结果")
                        return False
                else:
                    print("❌ 没有解析结果")
                    return False
            else:
                print(f"❌ 解析失败: {result['error']}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
    
    finally:
        # 清理临时文件
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)


def test_directory_parsing():
    """测试目录批量解析功能"""
    print("\n=== 测试目录批量解析功能 ===")
    
    # 创建临时目录和测试文件
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试SQL文件1
        test_sql1 = """
        -- 规则名称: 测试LIKE条件病案提取
        -- 城市: 测试城市1
        -- 行为认定: 病案提取
        SELECT A.病案号, B.医保项目编码
        FROM 医保住院结算明细 B
        JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
        WHERE B.医保项目编码 LIKE '%XB02BXC117B002010101313%'
        """

        # 创建测试SQL文件2
        test_sql2 = """
        -- 规则名称: 测试等值条件病案提取
        -- 城市: 测试城市2
        -- 行为认定: 病案提取
        SELECT A.病案号, B.医保项目名称
        FROM 医保门诊结算明细 B
        JOIN 医保门诊结算主单 A ON A.结算单据号 = B.结算单据号
        WHERE B.医保项目名称 = '注射用福沙匹坦双葡甲胺'
        """
        
        # 保存测试文件
        with open(os.path.join(temp_dir, 'test_rule1.sql'), 'w', encoding='utf-8') as f:
            f.write(test_sql1)
        
        with open(os.path.join(temp_dir, 'test_rule2.sql'), 'w', encoding='utf-8') as f:
            f.write(test_sql2)
        
        # 调用目录解析API
        response = requests.post(
            f"{BASE_URL}/api/parse_directory",
            json={"directory_path": temp_dir},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print("✅ 目录批量解析成功")
                print(f"解析规则数量: {result['total_count']}")
                print(f"规则类型统计: {result['stats']['rule_types']}")
                
                # 验证所有规则都被识别为病案提取
                all_case_extraction = True
                deep_analysis_found = False
                
                for rule in result['results']:
                    if rule.get('rule_type') != '病案提取':
                        all_case_extraction = False
                        print(f"❌ 规则 {rule.get('rule_name')} 类型错误: {rule.get('rule_type')}")
                    
                    # 检查是否有深度分析结果
                    deep_analysis = rule.get('deep_analysis', {})
                    if deep_analysis and deep_analysis.get('conditions_count', 0) > 0:
                        deep_analysis_found = True
                
                if all_case_extraction:
                    print("✅ 所有规则都正确识别为病案提取")
                else:
                    print("❌ 部分规则类型识别错误")
                
                if deep_analysis_found:
                    print("✅ 发现深度分析结果（使用深度解析器）")
                    return True
                else:
                    print("❌ 没有深度分析结果")
                    return False
            else:
                print(f"❌ 解析失败: {result['error']}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False


if __name__ == "__main__":
    print("开始测试文件上传和目录批量解析功能...\n")
    
    tests = [
        ("文件上传解析", test_file_upload_parsing),
        ("目录批量解析", test_directory_parsing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有功能都已升级为使用深度SQL解析器！")
        print("\n📋 验证的功能:")
        print("✅ 文件上传解析 - 使用深度SQL解析器")
        print("✅ 目录批量解析 - 使用深度SQL解析器")
        print("✅ NOT条件解析 - 提取排除诊断")
        print("✅ 规则类型识别 - 病案提取规则")
    else:
        print("⚠️  仍有问题需要解决。")
