"""
测试ClickHouse SQL解析修复
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_clickhouse_sql():
    """测试ClickHouse SQL解析"""
    print("=== 测试ClickHouse SQL解析 ===")
    
    sql_content = """
    SELECT
    A.`病案号` AS `病案号`,
    A.`结算单据号` AS `结算单据号`,
    A.`医疗机构编码` AS `医疗机构编码`,
    A.`医疗机构名称` AS `医疗机构名称`,
    A.`结算日期` AS `结算日期`
    FROM 医保住院结算主单 A
    WHERE A.`患者年龄` > 65
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"HTTP状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ ClickHouse SQL解析成功")
            
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"规则类型: {rule_info['rule_type']}")
            print(f"数据源数量: {deep_analysis['data_sources_count']}")
            print(f"条件数量: {deep_analysis['conditions_count']}")
            
            return True
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        try:
            error_info = response.json()
            print(f"错误信息: {error_info}")
        except:
            print(f"响应内容: {response.text}")
        return False


def test_postgresql_sql():
    """测试PostgreSQL SQL解析（确保修复没有破坏原有功能）"""
    print("\n=== 测试PostgreSQL SQL解析 ===")
    
    sql_content = """
    -- 规则名称: 测试规则
    -- 城市: 测试城市
    -- 行为认定: 超量使用
    SELECT 结算单据号, SUM(数量) as 总数量
    FROM 医保住院结算明细
    WHERE 医保项目名称 = 'CT检查'
    GROUP BY 结算单据号
    HAVING SUM(数量) > 3
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"HTTP状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ PostgreSQL SQL解析成功")
            
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"规则类型: {rule_info['rule_type']}")
            print(f"聚合函数数量: {deep_analysis['aggregations_count']}")
            
            # 检查JSON输出是否包含中文
            json_output = deep_analysis['json_output']
            print(f"JSON输出长度: {len(json_output)} 字符")
            print(f"包含中文: {'是' if any(ord(c) > 127 for c in json_output) else '否'}")
            
            return True
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


if __name__ == "__main__":
    print("开始测试修复后的SQL解析功能...\n")
    
    tests = [
        ("ClickHouse SQL解析", test_clickhouse_sql),
        ("PostgreSQL SQL解析", test_postgresql_sql)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 修复测试全部通过！")
    else:
        print("⚠️  仍有问题需要解决。")
