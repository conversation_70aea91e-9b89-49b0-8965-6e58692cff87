#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库事务提交的脚本
用于验证规则创建流程中的事务问题
"""

import oracledb
import configparser
import logging
from contextlib import contextmanager
import time

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 读取配置
config = configparser.ConfigParser()
config.read('config.ini')

@contextmanager
def get_connection(pool):
    """获取数据库连接的上下文管理器"""
    connection = None
    try:
        connection = pool.acquire()
        yield connection
    finally:
        if connection:
            pool.release(connection)

def test_transaction_commit():
    """测试事务提交是否正常工作"""
    try:
        # 创建连接池
        db_config = {
            'username': config.get('datachange', 'username'),
            'password': config.get('datachange', 'password'),
            'dsn': config.get('datachange', 'dsn')
        }
        
        pool = oracledb.create_pool(
            user=db_config['username'],
            password=db_config['password'],
            dsn=db_config['dsn'],
            min=2,
            max=5,
            increment=1,
            getmode=oracledb.POOL_GETMODE_WAIT,
            wait_timeout=10000,
            timeout=300
        )
        
        logging.info("连接池创建成功")
        
        # 测试1：插入一条测试记录
        test_rule_name = f"测试规则_{int(time.time())}"
        
        with get_connection(pool) as conn1:
            conn1.autocommit = False
            try:
                with conn1.cursor() as cursor:
                    # 获取序列值
                    cursor.execute("SELECT 飞检规则知识库ID_SEQ.NEXTVAL FROM DUAL")
                    test_id = cursor.fetchone()[0]
                    
                    # 插入测试记录
                    insert_query = """
                    INSERT INTO 飞检规则知识库 (ID, 规则名称, 规则类型, 行为认定)
                    VALUES (:id, :name, '0', '测试行为认定')
                    """
                    cursor.execute(insert_query, {'id': test_id, 'name': test_rule_name})
                    
                    # 提交事务
                    conn1.commit()
                    logging.info(f"测试记录插入成功，ID: {test_id}")
                    
                    # 验证插入
                    cursor.execute("SELECT COUNT(*) FROM 飞检规则知识库 WHERE ID = :id", {'id': test_id})
                    count = cursor.fetchone()[0]
                    logging.info(f"同一连接验证：记录数 = {count}")
                    
            except Exception as e:
                conn1.rollback()
                logging.error(f"插入失败: {str(e)}")
                raise
        
        # 测试2：使用不同连接查询刚插入的记录
        time.sleep(1)  # 稍等一下
        
        with get_connection(pool) as conn2:
            with conn2.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM 飞检规则知识库 WHERE ID = :id", {'id': test_id})
                count = cursor.fetchone()[0]
                logging.info(f"不同连接验证：记录数 = {count}")
                
                if count == 1:
                    logging.info("✅ 事务提交测试通过：不同连接可以看到已提交的数据")
                else:
                    logging.error("❌ 事务提交测试失败：不同连接无法看到已提交的数据")
                
                # 清理测试数据
                cursor.execute("DELETE FROM 飞检规则知识库 WHERE ID = :id", {'id': test_id})
                conn2.commit()
                logging.info("测试数据已清理")
        
        # 测试3：测试外键约束
        logging.info("开始测试外键约束...")
        
        with get_connection(pool) as conn3:
            conn3.autocommit = False
            try:
                with conn3.cursor() as cursor:
                    # 获取序列值
                    cursor.execute("SELECT 飞检规则知识库ID_SEQ.NEXTVAL FROM DUAL")
                    fk_test_id = cursor.fetchone()[0]
                    
                    # 插入主表记录
                    insert_main = """
                    INSERT INTO 飞检规则知识库 (ID, 规则名称, 规则类型, 行为认定)
                    VALUES (:id, :name, '0', '外键测试')
                    """
                    cursor.execute(insert_main, {'id': fk_test_id, 'name': f"外键测试_{int(time.time())}"})
                    conn3.commit()
                    logging.info(f"主表记录插入成功，ID: {fk_test_id}")
                    
            except Exception as e:
                conn3.rollback()
                logging.error(f"主表插入失败: {str(e)}")
                raise
        
        # 使用不同连接插入关联表记录
        with get_connection(pool) as conn4:
            conn4.autocommit = False
            try:
                with conn4.cursor() as cursor:
                    # 验证主表记录存在
                    cursor.execute("SELECT COUNT(*) FROM 飞检规则知识库 WHERE ID = :id", {'id': fk_test_id})
                    count = cursor.fetchone()[0]
                    logging.info(f"关联表操作前验证主表记录：记录数 = {count}")
                    
                    if count == 0:
                        logging.error("❌ 主表记录不存在，外键约束测试失败")
                        return
                    
                    # 插入关联表记录
                    insert_relation = """
                    INSERT INTO 规则医保编码对照 (
                        对照ID, 规则ID, 城市, 规则内涵, 创建时间, 更新时间
                    ) VALUES (
                        规则医保编码对照_SEQ.NEXTVAL, :rule_id, '测试城市', '测试内涵', SYSDATE, SYSDATE
                    )
                    """
                    cursor.execute(insert_relation, {'rule_id': fk_test_id})
                    conn4.commit()
                    logging.info("✅ 关联表记录插入成功，外键约束测试通过")
                    
                    # 清理测试数据
                    cursor.execute("DELETE FROM 规则医保编码对照 WHERE 规则ID = :id", {'id': fk_test_id})
                    cursor.execute("DELETE FROM 飞检规则知识库 WHERE ID = :id", {'id': fk_test_id})
                    conn4.commit()
                    logging.info("外键测试数据已清理")
                    
            except Exception as e:
                conn4.rollback()
                logging.error(f"❌ 关联表插入失败: {str(e)}")
                # 清理主表数据
                try:
                    with get_connection(pool) as cleanup_conn:
                        with cleanup_conn.cursor() as cleanup_cursor:
                            cleanup_cursor.execute("DELETE FROM 飞检规则知识库 WHERE ID = :id", {'id': fk_test_id})
                            cleanup_conn.commit()
                            logging.info("清理了主表测试数据")
                except:
                    pass
                raise
        
        logging.info("🎉 所有事务测试完成")
        
    except Exception as e:
        logging.error(f"测试失败: {str(e)}")
        raise
    finally:
        if 'pool' in locals():
            pool.close()

if __name__ == "__main__":
    test_transaction_commit()
