"""
最终集成测试 - 验证所有修复和新功能
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_clickhouse_case_extraction():
    """测试ClickHouse病案提取规则"""
    print("=== 测试ClickHouse病案提取规则 ===")
    
    sql_content = """
    -- 规则名称: 特定医保项目病案提取
    -- 城市: 上海市
    -- 行为认定: 病案提取
    SELECT
      A.`病案号` AS `病案号`,
      A.`结算单据号` AS `结算单据号`,
      A.`医疗机构编码` AS `医疗机构编码`,
      A.`医疗机构名称` AS `医疗机构名称`,
      A.`结算日期` AS `结算日期`,
      B.`医保项目编码` AS `医保项目编码`,
      B.`医保项目名称` AS `医保项目名称`,
      B.`数量` AS `数量`,
      B.`金额` AS `金额`
    FROM
      ZZS_YB_ZDYFY_9LY.`医保门诊结算主单`  A
    JOIN
      ZZS_YB_ZDYFY_9LY.`医保门诊结算明细`  B
    ON
      A.`结算单据号` = B.`结算单据号`
    WHERE
     B.`医保项目编码`  in (
     'XL03AAJ213B002010104021',
    'XL03AAJ213B002020104089',
    'XL03AAJ213B002010304021',
    'XL03AAJ213B002010204021',
    'XL03AAJ213B002010104089'
    )
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            # 验证基本信息
            assert rule_info['rule_type'] == '病案提取', f"规则类型错误: {rule_info['rule_type']}"
            assert rule_info['rule_name'] == '特定医保项目病案提取', f"规则名称错误: {rule_info['rule_name']}"
            assert rule_info['city'] == '上海市', f"城市错误: {rule_info['city']}"
            assert rule_info['behavior'] == '病案提取', f"行为认定错误: {rule_info['behavior']}"
            
            # 验证深度分析
            assert deep_analysis['data_sources_count'] == 2, f"数据源数量错误: {deep_analysis['data_sources_count']}"
            assert deep_analysis['conditions_count'] == 1, f"条件数量错误: {deep_analysis['conditions_count']}"
            assert deep_analysis['aggregations_count'] == 0, f"聚合函数数量错误: {deep_analysis['aggregations_count']}"
            
            # 验证医保项目提取
            medical_items = rule_info['medical_items']
            assert len(medical_items) == 5, f"医保项目数量错误: {len(medical_items)}"
            expected_codes = {
                'XL03AAJ213B002010104021',
                'XL03AAJ213B002020104089', 
                'XL03AAJ213B002010304021',
                'XL03AAJ213B002010204021',
                'XL03AAJ213B002010104089'
            }
            actual_codes = set(medical_items)
            assert actual_codes == expected_codes, f"医保项目编码不匹配: {actual_codes} vs {expected_codes}"
            
            print("✅ ClickHouse病案提取规则测试通过")
            return True
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


def test_postgresql_excessive_usage():
    """测试PostgreSQL超量使用规则"""
    print("\n=== 测试PostgreSQL超量使用规则 ===")
    
    sql_content = """
    -- 规则名称: CT检查超量使用
    -- 城市: 北京市
    -- 行为认定: 超量使用
    SELECT 结算单据号, SUM(数量) as 总数量
    FROM 医保住院结算明细
    WHERE 医保项目名称 = 'CT检查'
    GROUP BY 结算单据号
    HAVING SUM(数量) > 3
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            # 验证基本信息
            assert rule_info['rule_type'] == '超量使用', f"规则类型错误: {rule_info['rule_type']}"
            assert rule_info['rule_name'] == 'CT检查超量使用', f"规则名称错误: {rule_info['rule_name']}"
            assert rule_info['city'] == '北京市', f"城市错误: {rule_info['city']}"
            
            # 验证深度分析
            assert deep_analysis['aggregations_count'] >= 1, f"聚合函数数量错误: {deep_analysis['aggregations_count']}"
            
            # 验证医保项目提取
            medical_items = rule_info['medical_items']
            assert 'CT检查' in medical_items, f"医保项目未提取: {medical_items}"
            
            print("✅ PostgreSQL超量使用规则测试通过")
            return True
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


def test_age_restriction():
    """测试年龄限制规则"""
    print("\n=== 测试年龄限制规则 ===")
    
    sql_content = """
    -- 规则名称: 儿童用药年龄限制
    -- 城市: 广州市
    -- 行为认定: 年龄限制
    SELECT * FROM 医保住院结算明细 B
    JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
    WHERE A.患者年龄 < 18 AND B.医保项目名称 = '成人专用药物'
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            # 验证基本信息
            assert rule_info['rule_type'] == '年龄限制', f"规则类型错误: {rule_info['rule_type']}"
            assert rule_info['rule_name'] == '儿童用药年龄限制', f"规则名称错误: {rule_info['rule_name']}"
            assert rule_info['city'] == '广州市', f"城市错误: {rule_info['city']}"
            
            # 验证深度分析
            assert deep_analysis['conditions_count'] >= 2, f"条件数量错误: {deep_analysis['conditions_count']}"
            
            print("✅ 年龄限制规则测试通过")
            return True
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


if __name__ == "__main__":
    print("开始最终集成测试...\n")
    
    tests = [
        ("ClickHouse病案提取", test_clickhouse_case_extraction),
        ("PostgreSQL超量使用", test_postgresql_excessive_usage),
        ("年龄限制规则", test_age_restriction)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n🎯 最终测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！深度SQL解析器功能完全正常！")
        print("\n📋 功能总结:")
        print("✅ ClickHouse SQL解析（反引号语法）")
        print("✅ IN条件解析和医保项目提取")
        print("✅ 病案提取规则类型识别")
        print("✅ 超量使用规则识别")
        print("✅ 年龄限制规则识别")
        print("✅ 多方言SQL支持")
        print("✅ 前端显示功能")
        print("✅ JSON输出和复制功能")
    else:
        print("⚠️  仍有问题需要解决。")
