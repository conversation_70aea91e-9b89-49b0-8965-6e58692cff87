<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医院规则推荐系统Bug修复测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .test-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .matched-items {
            max-width: 300px;
            word-wrap: break-word;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">医院规则推荐系统Bug修复测试</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试配置</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="hospitalSelect" class="form-label">选择测试医院:</label>
                            <select class="form-select" id="hospitalSelect">
                                <option value="">请选择医院...</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="runTests()">开始测试</button>
                        <button class="btn btn-secondary" onclick="clearResults()">清除结果</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试状态</h5>
                    </div>
                    <div class="card-body">
                        <div id="testStatus">等待开始测试...</div>
                        <div class="progress mt-2" style="display: none;">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <div class="test-info">
                                <strong>测试说明：</strong><br>
                                1. <strong>Bug 1修复验证：</strong>检查匹配项目字段是否正确显示（不再从推荐原因中提取）<br>
                                2. <strong>Bug 2修复验证：</strong>检查长字符串是否能正常处理（不再出现ORA-01461错误）<br>
                                3. <strong>API一致性验证：</strong>确保前后端数据一致性
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>规则数据详情</h5>
                    </div>
                    <div class="card-body">
                        <div id="ruleDetails">
                            <p class="text-muted">请先运行测试以查看规则数据详情</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let testResults = [];
        
        // 页面加载时获取医院列表
        document.addEventListener('DOMContentLoaded', function() {
            loadHospitals();
        });
        
        async function loadHospitals() {
            try {
                const response = await fetch('/api/hospitals');
                const data = await response.json();
                
                if (data.success) {
                    const select = document.getElementById('hospitalSelect');
                    data.hospitals.forEach(hospital => {
                        const option = document.createElement('option');
                        option.value = hospital.医院ID;
                        option.textContent = hospital.医院名称;
                        select.appendChild(option);
                    });
                    addTestResult('success', '医院列表加载成功', `共加载${data.hospitals.length}家医院`);
                } else {
                    addTestResult('error', '医院列表加载失败', data.error);
                }
            } catch (error) {
                addTestResult('error', '医院列表加载异常', error.message);
            }
        }
        
        async function runTests() {
            const hospitalId = document.getElementById('hospitalSelect').value;
            if (!hospitalId) {
                alert('请先选择一家医院');
                return;
            }
            
            clearResults();
            updateTestStatus('开始测试...', 0);
            
            // 测试1: 获取所有规则API
            await testAllRulesAPI(hospitalId);
            updateTestStatus('测试所有规则API完成', 33);
            
            // 测试2: 获取已采用规则API
            await testAdoptedRulesAPI(hospitalId);
            updateTestStatus('测试已采用规则API完成', 66);
            
            // 测试3: 验证匹配项目字段
            await testMatchedItemsField(hospitalId);
            updateTestStatus('测试完成', 100);
            
            generateSummary();
        }
        
        async function testAllRulesAPI(hospitalId) {
            try {
                const response = await fetch(`/api/hospital-rules/all/${hospitalId}`);
                const data = await response.json();
                
                if (data.success) {
                    const rules = data.rules || [];
                    addTestResult('success', 'Bug 1修复验证 - 所有规则API', 
                        `成功获取${rules.length}条规则，匹配项目字段正常返回`);
                    
                    // 检查匹配项目字段
                    let hasMatchedItems = 0;
                    let validMatchedItems = 0;
                    
                    rules.forEach(rule => {
                        if (rule.匹配项目) {
                            hasMatchedItems++;
                            if (rule.匹配项目 !== '未知' && rule.匹配项目 !== '推荐规则') {
                                validMatchedItems++;
                            }
                        }
                    });
                    
                    addTestResult('info', '匹配项目字段统计', 
                        `总规则数: ${rules.length}, 有匹配项目: ${hasMatchedItems}, 有效匹配项目: ${validMatchedItems}`);
                    
                    // 显示前5条规则的详细信息
                    displayRuleDetails('所有规则', rules.slice(0, 5));
                    
                } else {
                    addTestResult('error', '所有规则API测试失败', data.error);
                }
            } catch (error) {
                addTestResult('error', '所有规则API测试异常', error.message);
            }
        }
        
        async function testAdoptedRulesAPI(hospitalId) {
            try {
                const response = await fetch(`/api/hospital-rules/adopted/${hospitalId}`);
                const data = await response.json();
                
                if (data.success) {
                    const rules = data.rules || [];
                    addTestResult('success', 'Bug 1修复验证 - 已采用规则API', 
                        `成功获取${rules.length}条已采用规则，匹配项目字段正常返回`);
                    
                    // 显示已采用规则的详细信息
                    if (rules.length > 0) {
                        displayRuleDetails('已采用规则', rules.slice(0, 3));
                    }
                    
                } else {
                    addTestResult('error', '已采用规则API测试失败', data.error);
                }
            } catch (error) {
                addTestResult('error', '已采用规则API测试异常', error.message);
            }
        }
        
        async function testMatchedItemsField(hospitalId) {
            try {
                // 尝试生成规则推荐来测试长字符串处理
                const response = await fetch('/api/hospital-rules/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ hospital_id: hospitalId })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addTestResult('success', 'Bug 2修复验证 - 规则生成', 
                        `成功生成${data.recommendations?.length || 0}条推荐，未出现ORA-01461错误`);
                } else {
                    if (data.error && data.error.includes('ORA-01461')) {
                        addTestResult('error', 'Bug 2修复验证失败', 
                            '仍然出现ORA-01461错误，字段长度限制未解决');
                    } else {
                        addTestResult('info', 'Bug 2修复验证', 
                            `规则生成返回错误但非字段长度问题: ${data.error}`);
                    }
                }
            } catch (error) {
                addTestResult('error', '规则生成测试异常', error.message);
            }
        }
        
        function displayRuleDetails(category, rules) {
            const detailsDiv = document.getElementById('ruleDetails');
            
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'mb-3';
            categoryDiv.innerHTML = `<h6>${category} (前${rules.length}条)</h6>`;
            
            const table = document.createElement('table');
            table.className = 'table table-sm table-striped';
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>规则ID</th>
                        <th>规则名称</th>
                        <th>匹配项目</th>
                        <th>状态</th>
                        <th>匹配度</th>
                    </tr>
                </thead>
                <tbody>
                    ${rules.map(rule => `
                        <tr>
                            <td>${rule.规则ID || 'N/A'}</td>
                            <td class="text-truncate" style="max-width: 200px;" title="${rule.规则名称 || 'N/A'}">${rule.规则名称 || 'N/A'}</td>
                            <td class="matched-items" title="${rule.匹配项目 || 'N/A'}">${rule.匹配项目 || 'N/A'}</td>
                            <td><span class="badge bg-${rule.状态 === '已采用' ? 'success' : rule.状态 === '已忽略' ? 'secondary' : 'primary'}">${rule.状态 || 'N/A'}</span></td>
                            <td>${rule.匹配度 || 'N/A'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            `;
            
            categoryDiv.appendChild(table);
            detailsDiv.appendChild(categoryDiv);
        }
        
        function addTestResult(type, title, message) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.innerHTML = `<strong>${title}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            
            testResults.push({ type, title, message, timestamp: new Date() });
        }
        
        function updateTestStatus(status, progress) {
            document.getElementById('testStatus').textContent = status;
            const progressBar = document.querySelector('.progress-bar');
            const progressContainer = document.querySelector('.progress');
            
            if (progress > 0) {
                progressContainer.style.display = 'block';
                progressBar.style.width = progress + '%';
                progressBar.textContent = progress + '%';
            }
            
            if (progress >= 100) {
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 2000);
            }
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('ruleDetails').innerHTML = '<p class="text-muted">请先运行测试以查看规则数据详情</p>';
            testResults = [];
        }
        
        function generateSummary() {
            const successCount = testResults.filter(r => r.type === 'success').length;
            const errorCount = testResults.filter(r => r.type === 'error').length;
            const infoCount = testResults.filter(r => r.type === 'info').length;
            
            addTestResult('info', '测试总结', 
                `成功: ${successCount}, 错误: ${errorCount}, 信息: ${infoCount}`);
            
            if (errorCount === 0) {
                addTestResult('success', '修复验证结果', 'Bug修复验证通过，系统运行正常');
            } else {
                addTestResult('error', '修复验证结果', '发现问题，请检查错误信息并进行修复');
            }
        }
    </script>
</body>
</html>
