"""
调试条件解析问题
"""

import sys
import os
import logging
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sql_deep_parser import DeepSQLParser

# 启用调试日志
logging.basicConfig(level=logging.DEBUG)

def debug_condition_parsing():
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    SELECT * FROM 医保住院结算明细 B
    JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
    WHERE A.患者年龄 > 50 AND A.患者年龄 < 75 AND A.患者性别 = '男'
    """
    
    # 手动调试解析过程
    import sqlglot
    from sqlglot import expressions as exp
    
    ast = sqlglot.parse_one(sql)
    print("完整AST:")
    print(ast)
    print("\n" + "="*50)
    
    # 找到WHERE子句
    where_clause = ast.find(exp.Where)
    print("WHERE子句:")
    print(where_clause)
    print(f"WHERE表达式类型: {type(where_clause.this)}")
    print("\n" + "="*50)
    
    # 手动调用条件提取方法
    print("调用条件提取方法:")
    conditions = parser._extract_conditions_from_expression(where_clause.this)
    print(f"提取到的条件数量: {len(conditions)}")
    
    for i, cond in enumerate(conditions):
        print(f"条件 {i+1}:")
        print(f"  字段: {cond.field.table_alias}.{cond.field.field_name}")
        print(f"  操作符: {cond.operator.value}")
        print(f"  值: {cond.value}")
        print(f"  逻辑类型: {cond.logic_type.value}")

if __name__ == "__main__":
    debug_condition_parsing()
