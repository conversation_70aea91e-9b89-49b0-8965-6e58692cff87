# SQL规则解析器

用于分析医保飞检规则的SQL语句并提取其业务逻辑的Python工具。

## 功能特性

### 1. 规则类型自动识别
- **重复收费检测规则**：识别使用INTERSECT查找同时存在项目组合的规则
- **超量使用检测规则**：识别使用HAVING SUM(数量) > 阈值的规则  
- **病例提取规则**：识别基于诊断条件的简单查询规则
- **超标准收费规则**：识别计价标准相关的规则
- **不当使用规则**：识别年龄、性别等限制条件的规则

### 2. 条件参数自动提取
- **年龄限制条件**：提取年龄范围（如：≥50岁、6-75岁）
- **性别限制条件**：提取性别要求（男/女）
- **诊断条件**：提取包含/排除的诊断名称列表
- **科室条件**：提取相关科室限制
- **数量阈值**：提取数量限制条件
- **时间条件**：提取时间格式和时间范围要求

### 3. 元数据信息解析
- **规则名称**：从注释中提取规则标题
- **城市信息**：提取适用城市
- **规则来源**：提取规则来源文档
- **行为认定**：提取违规行为类型
- **医保项目**：提取相关医保项目名称

## 快速开始

```python
from sql_rule_parser import SQLRuleParser

# 创建解析器
parser = SQLRuleParser()

# 解析单个SQL文件
rule_info = parser.parse_file("rule.sql")

# 解析SQL内容
sql_content = """
-- 规则名称: 重复收费检测
-- 行为认定: 重复收费
WITH tab1 AS (
  SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = 'A'
  INTERSECT  
  SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = 'B'
)
SELECT * FROM 医保住院结算明细 JOIN tab1 USING(结算单据号)
"""

rule_info = parser.parse_content(sql_content)

# 查看解析结果
print(f"规则类型: {rule_info.rule_type.value}")
print(f"医保项目: {rule_info.medical_items}")
print(f"年龄限制: {rule_info.conditions.age_range}")
```

## 运行测试

```bash
# 运行单元测试
python -m pytest tests/

# 运行示例脚本
python example_usage.py
```
