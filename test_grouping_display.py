#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复规则分组展示测试脚本

测试新的分组展示逻辑：
1. 医保名称1重复规则组
2. 医保名称2重复规则组
3. 按对照ID集合进行分组
4. 分类统计和展示

使用方法：
python test_grouping_display.py [hospital_id]

作者: Augment Agent
日期: 2025-07-21
"""

import sys
import requests
import json
from datetime import datetime
from collections import defaultdict

def test_grouping_display(hospital_id=1):
    """测试新的分组展示逻辑"""
    url = f"http://localhost:5000/api/hospital-rules/duplicate-analysis/{hospital_id}"
    
    print(f"测试重复规则分组展示逻辑")
    print(f"URL: {url}")
    print("=" * 80)
    
    try:
        response = requests.get(url, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✓ API调用成功")
                print(f"总规则数: {data.get('total_rules', 0)}")
                print(f"重复规则数: {data.get('duplicate_rules', 0)}")
                print(f"重复组数: {data.get('duplicate_groups_count', 0)}")
                
                # 分析分组结果
                groups = data.get('duplicate_groups', [])
                if groups:
                    analyze_grouping_results(groups)
                else:
                    print("\n没有发现重复规则")
                
                return True
            else:
                print(f"✗ API返回错误: {data.get('error')}")
                if data.get('details'):
                    print(f"详细错误信息:")
                    print(data.get('details'))
                return False
        else:
            print(f"✗ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return False

def analyze_grouping_results(groups):
    """分析分组结果"""
    print(f"\n分组结果分析:")
    print("=" * 80)
    
    # 按类别统计
    category_stats = defaultdict(list)
    for group in groups:
        category = group.get('category', '未知类别')
        category_stats[category].append(group)
    
    print(f"分类统计:")
    for category, category_groups in category_stats.items():
        print(f"  {category}: {len(category_groups)} 组")
    
    print(f"\n详细分组信息:")
    print("-" * 80)
    
    for i, group in enumerate(groups):
        print(f"\n【分组 {i+1}】")
        print(f"类别: {group.get('category', '未知')}")
        print(f"组ID: {group.get('group_id')}")
        print(f"规则数量: {group.get('rule_count', 0)}")
        print(f"相似度: {group.get('similarity', 0):.2f}")
        print(f"共享对照ID: {group.get('compare_ids', [])}")
        
        # 分析共同医保项目
        common_names = group.get('common_medical_names', [])
        print(f"共同医保项目 ({len(common_names)} 个):")
        for name in common_names:
            print(f"  - {name}")
        
        # 分析重复项目
        duplicate_items = group.get('duplicate_items', [])
        if duplicate_items:
            print(f"重复项目 ({len(duplicate_items)} 个): {duplicate_items}")
        
        # 验证分组逻辑
        verify_grouping_logic(group)
        
        print("-" * 40)

def verify_grouping_logic(group):
    """验证分组逻辑是否正确"""
    print("分组逻辑验证:")
    
    category = group.get('category', '')
    compare_ids = group.get('compare_ids', [])
    rules = group.get('rules', [])
    duplicate_items = group.get('duplicate_items', [])
    
    # 验证对照ID集合
    if len(compare_ids) < 2:
        print("  ⚠️  警告: 对照ID数量少于2个，可能不应该被归为重复组")
    else:
        print(f"  ✓ 对照ID集合合理: {len(compare_ids)} 个对照ID")
    
    # 验证规则数量
    if len(rules) < 2:
        print("  ⚠️  警告: 规则数量少于2条，可能不应该被归为重复组")
    else:
        print(f"  ✓ 规则数量合理: {len(rules)} 条规则")
    
    # 验证类别一致性
    if category in ['医保名称1重复', '医保名称2重复']:
        print(f"  ✓ 类别标识正确: {category}")
    else:
        print(f"  ⚠️  警告: 未知类别: {category}")
    
    # 验证重复项目
    if duplicate_items:
        print(f"  ✓ 重复项目识别: {len(duplicate_items)} 个重复项目")
    else:
        print("  ⚠️  警告: 没有识别到重复项目")
    
    # 验证规则的对照ID是否在共享对照ID集合中
    rule_compare_ids = set()
    for rule in rules:
        rule_compare_id = rule.get('对照ID')
        if rule_compare_id:
            rule_compare_ids.add(rule_compare_id)
    
    expected_compare_ids = set(compare_ids)
    if rule_compare_ids == expected_compare_ids:
        print("  ✓ 规则对照ID与共享对照ID集合一致")
    else:
        print(f"  ⚠️  警告: 规则对照ID {rule_compare_ids} 与共享对照ID {expected_compare_ids} 不一致")

def test_display_logic():
    """测试前端显示逻辑"""
    print("\n前端显示逻辑验证:")
    print("=" * 80)
    
    print("新的显示特性:")
    print("1. ✓ 分为两大类别：医保名称1重复 和 医保名称2重复")
    print("2. ✓ 每个分组显示类别图标和颜色标识")
    print("3. ✓ 显示共享对照ID集合信息")
    print("4. ✓ 在顶部显示分类统计信息")
    print("5. ✓ 保持现有的折叠/展开功能")
    
    print("\n分组逻辑改进:")
    print("- 按对照ID集合进行分组，而不是简单的项目名称匹配")
    print("- 相同对照ID集合的规则归为一组，即使医保名称内容不同")
    print("- 分别处理医保名称1和医保名称2，避免交叉比较")
    print("- 提供更清晰的重复关系展示")

def test_algorithm_improvements():
    """测试算法改进效果"""
    print("\n算法改进验证:")
    print("=" * 80)
    
    print("改进前的问题:")
    print("- 重复规则混合展示，难以区分来源字段")
    print("- 分组逻辑不够清晰，可能产生误解")
    print("- 缺少对照ID集合信息，难以理解重复关系")
    
    print("\n改进后的优势:")
    print("- 明确区分医保名称1和医保名称2的重复规则")
    print("- 按对照ID集合分组，逻辑更加清晰")
    print("- 显示共享对照ID信息，便于理解重复关系")
    print("- 提供分类统计，便于快速了解重复情况")

def main():
    """主测试函数"""
    hospital_id = 1
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("重复规则分组展示测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试医院ID: {hospital_id}")
    
    try:
        # 1. 测试分组展示逻辑
        api_success = test_grouping_display(hospital_id)
        
        # 2. 验证显示逻辑
        test_display_logic()
        
        # 3. 测试算法改进
        test_algorithm_improvements()
        
        print("\n" + "=" * 80)
        if api_success:
            print("✓ 分组展示测试完成")
            print("请在前端界面验证以下改进效果：")
            print("1. 重复规则分为两大类别展示")
            print("2. 每个分组显示共享对照ID信息")
            print("3. 顶部显示分类统计信息")
            print("4. 分组逻辑更加合理和清晰")
        else:
            print("✗ 分组展示测试发现问题，请检查API实现")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
