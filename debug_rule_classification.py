"""
调试规则分类问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sql_deep_parser import DeepSQLParser

def debug_age_restriction():
    """调试年龄限制规则分类"""
    print("=== 调试年龄限制规则分类 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    -- 规则名称: 儿童用药年龄限制
    -- 城市: 上海市
    -- 行为认定: 年龄限制
    SELECT * FROM 医保住院结算明细 B
    JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
    WHERE A.患者年龄 < 18 AND B.医保项目名称 = '成人专用药物'
    """
    
    result = parser.parse(sql)
    
    if result.success:
        rule_ir = result.rule_ir
        print(f"原始规则类型: {rule_ir.rule_type.value}")
        print(f"数据源数量: {len(rule_ir.data_sources)}")
        print(f"条件数量: {len(rule_ir.conditions)}")
        print(f"聚合函数数量: {len(rule_ir.aggregations)}")
        print(f"重复收费模式: {rule_ir.duplicate_pattern is not None}")
        
        print("\n条件详情:")
        for i, cond in enumerate(rule_ir.conditions):
            print(f"  {i+1}. {cond.field.field_name} ({cond.field.field_type.value}) {cond.operator.value} {cond.value}")
        
        print("\n数据源详情:")
        for i, ds in enumerate(rule_ir.data_sources):
            print(f"  {i+1}. {ds.table_name} (别名: {ds.alias}, JOIN类型: {ds.join_type})")
        
        # 手动调用推断方法
        inferred_type = parser._infer_rule_type(rule_ir)
        print(f"\n推断的规则类型: {inferred_type.value}")
        
        # 分析推断逻辑
        print("\n推断逻辑分析:")
        
        # 检查聚合函数
        if rule_ir.aggregations:
            print("  - 检测到聚合函数")
            for condition in rule_ir.conditions:
                if 'SUM(' in condition.field.field_name:
                    print("  - 检测到SUM聚合条件，可能是超量使用规则")
        
        # 检查重复收费模式
        if rule_ir.duplicate_pattern:
            print("  - 检测到重复收费模式")
        
        # 检查字段类型条件
        age_conditions = 0
        gender_conditions = 0
        
        from sql_deep_parser import FieldType

        for condition in rule_ir.conditions:
            if condition.field.field_type == FieldType.AGE:
                age_conditions += 1
                print(f"  - 检测到年龄条件: {condition.field.field_name}")
            elif condition.field.field_type == FieldType.GENDER:
                gender_conditions += 1
                print(f"  - 检测到性别条件: {condition.field.field_name}")
        
        print(f"  - 年龄条件数量: {age_conditions}")
        print(f"  - 性别条件数量: {gender_conditions}")
        
        # 检查数据源数量
        print(f"  - 数据源数量: {len(rule_ir.data_sources)} (阈值: 3)")
        
    else:
        print(f"解析失败: {result.error_message}")


def debug_excessive_usage():
    """调试超量使用规则分类"""
    print("\n=== 调试超量使用规则分类 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    -- 规则名称: 检查项目超量使用检测
    -- 城市: 广州市
    -- 行为认定: 超量使用
    SELECT 结算单据号, SUM(数量) as 总数量
    FROM 医保住院结算明细
    WHERE 医保项目名称 = 'CT检查'
    GROUP BY 结算单据号
    HAVING SUM(数量) > 3
    """
    
    result = parser.parse(sql)
    
    if result.success:
        rule_ir = result.rule_ir
        print(f"原始规则类型: {rule_ir.rule_type.value}")
        print(f"数据源数量: {len(rule_ir.data_sources)}")
        print(f"条件数量: {len(rule_ir.conditions)}")
        print(f"聚合函数数量: {len(rule_ir.aggregations)}")
        
        print("\n聚合函数详情:")
        for i, agg in enumerate(rule_ir.aggregations):
            print(f"  {i+1}. {agg.function.value}({agg.field.field_name})")
        
        print("\n条件详情:")
        for i, cond in enumerate(rule_ir.conditions):
            print(f"  {i+1}. {cond.field.field_name} ({cond.field.field_type.value}) {cond.operator.value} {cond.value}")
        
        # 手动调用推断方法
        inferred_type = parser._infer_rule_type(rule_ir)
        print(f"\n推断的规则类型: {inferred_type.value}")
        
    else:
        print(f"解析失败: {result.error_message}")


if __name__ == "__main__":
    debug_age_restriction()
    debug_excessive_usage()
