#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Gemini多密钥轮询功能
"""

import requests
import json
import time
import os

# 测试配置
base_url = 'http://localhost:5001'

def test_key_status():
    """测试获取密钥状态"""
    print('=== 测试获取密钥状态 ===')
    
    try:
        response = requests.get(f'{base_url}/api/gemini/keys/status', timeout=10)
        print(f'状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                status = data['data']
                print(f"✅ 密钥状态获取成功")
                print(f"总密钥数: {status['total_keys']}")
                print(f"可用密钥数: {status['available_keys']}")
                print(f"不可用密钥数: {status['unavailable_keys']}")
                print(f"总使用次数: {status['total_usage']}")
                print(f"总错误次数: {status['total_errors']}")
                print(f"当前索引: {status['current_index']}")
                
                print("\n密钥详情:")
                for key_detail in status['key_details']:
                    print(f"  密钥 {key_detail['index']}: {key_detail['key_preview']}")
                    print(f"    可用: {key_detail['is_available']}")
                    print(f"    使用次数: {key_detail['usage_count']}")
                    print(f"    错误次数: {key_detail['error_count']}")
                    if key_detail['last_error']:
                        print(f"    最后错误: {key_detail['last_error']}")
                    print()
                
                return status
            else:
                print(f"❌ 获取失败: {data.get('error')}")
        else:
            print(f"❌ API错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return None

def test_gemini_api_call():
    """测试Gemini API调用"""
    print('\n=== 测试Gemini API调用 ===')
    
    test_data = {
        'rule_content': '测试规则内容',
        'rule_name': '测试规则',
        'behavior_type': '测试行为',
        'city': '北京'
    }
    
    try:
        response = requests.post(
            f'{base_url}/api/rules/intelligent-get-medical-names',
            json=test_data,
            timeout=60
        )
        
        print(f'状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Gemini API调用成功")
                print(f"医保名称1: {data.get('medical_name1', '')}")
                print(f"医保名称2: {data.get('medical_name2', '')}")
                return True
            else:
                print(f"❌ API调用失败: {data.get('error')}")
                print(f"详情: {data.get('details', '')}")
        else:
            print(f"❌ HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return False

def test_multiple_calls():
    """测试多次调用，验证轮询功能"""
    print('\n=== 测试多次调用轮询功能 ===')
    
    test_data = {
        'rule_content': '呼吸机辅助呼吸、遥测心电监护、连续性血液净化等计价单位为小时，医院计费每日总时长大于24小时。',
        'rule_name': '呼吸机辅助呼吸超日限制',
        'behavior_type': '超标准收费',
        'city': '北京'
    }
    
    success_count = 0
    total_calls = 5
    
    for i in range(total_calls):
        print(f"\n第 {i+1} 次调用:")
        
        try:
            response = requests.post(
                f'{base_url}/api/rules/intelligent-get-medical-names',
                json=test_data,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    success_count += 1
                    print(f"✅ 调用成功")
                else:
                    print(f"❌ 调用失败: {data.get('error')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 短暂延迟
        time.sleep(1)
    
    print(f"\n轮询测试完成: {success_count}/{total_calls} 次成功")
    return success_count

def test_key_management():
    """测试密钥管理功能"""
    print('\n=== 测试密钥管理功能 ===')
    
    # 获取初始状态
    initial_status = test_key_status()
    if not initial_status:
        print("无法获取初始状态，跳过管理测试")
        return
    
    if initial_status['total_keys'] == 0:
        print("没有配置密钥，跳过管理测试")
        return
    
    # 测试禁用第一个密钥
    print("\n测试禁用密钥 0:")
    try:
        response = requests.post(
            f'{base_url}/api/gemini/keys/0/disable',
            json={'reason': '测试禁用'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 密钥禁用成功")
            else:
                print(f"❌ 禁用失败: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 禁用请求失败: {e}")
    
    # 检查状态变化
    print("\n检查禁用后的状态:")
    test_key_status()
    
    # 测试启用密钥
    print("\n测试启用密钥 0:")
    try:
        response = requests.post(f'{base_url}/api/gemini/keys/0/enable', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 密钥启用成功")
            else:
                print(f"❌ 启用失败: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 启用请求失败: {e}")
    
    # 测试重置所有密钥
    print("\n测试重置所有密钥:")
    try:
        response = requests.post(f'{base_url}/api/gemini/keys/reset', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 密钥重置成功")
            else:
                print(f"❌ 重置失败: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 重置请求失败: {e}")

def test_configuration():
    """测试配置解析"""
    print('\n=== 测试配置解析 ===')
    
    # 显示当前环境变量配置
    print("当前环境变量:")
    print(f"GEMINI_API_KEY: {os.getenv('GEMINI_API_KEY', '未设置')[:20]}...")
    print(f"GEMINI_API_KEYS: {os.getenv('GEMINI_API_KEYS', '未设置')}")
    
    # 测试配置解析
    try:
        from gemini_config import GEMINI_API_KEYS, GEMINI_API_KEY
        print(f"\n解析结果:")
        print(f"密钥数量: {len(GEMINI_API_KEYS)}")
        print(f"主密钥: {GEMINI_API_KEY[:20]}..." if GEMINI_API_KEY else "未设置")
        
        for i, key in enumerate(GEMINI_API_KEYS):
            print(f"密钥 {i}: {key[:20]}...")
            
    except Exception as e:
        print(f"❌ 配置解析失败: {e}")

def main():
    """主测试函数"""
    print('=' * 60)
    print('Gemini多密钥轮询功能测试')
    print('=' * 60)
    
    # 测试配置
    test_configuration()
    
    # 测试密钥状态
    test_key_status()
    
    # 测试API调用
    test_gemini_api_call()
    
    # 测试多次调用
    test_multiple_calls()
    
    # 测试密钥管理
    test_key_management()
    
    # 最终状态
    print('\n=== 最终密钥状态 ===')
    test_key_status()
    
    print('\n' + '=' * 60)
    print('测试完成')
    print('=' * 60)

if __name__ == "__main__":
    main()
