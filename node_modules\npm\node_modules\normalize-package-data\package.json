{"name": "normalize-package-data", "version": "7.0.0", "author": "GitHub Inc.", "description": "Normalizes data that can be found in package.json files.", "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/npm/normalize-package-data.git"}, "main": "lib/normalize.js", "scripts": {"test": "tap", "npmclilint": "npmcli-lint", "lint": "npm run eslint", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "snap": "tap", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "dependencies": {"hosted-git-info": "^8.0.0", "semver": "^7.3.5", "validate-npm-package-license": "^3.0.4"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": "true"}, "tap": {"branches": 86, "functions": 92, "lines": 86, "statements": 86, "nyc-arg": ["--exclude", "tap-snapshots/**"]}}