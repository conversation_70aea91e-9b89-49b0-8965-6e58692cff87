#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增功能：删除城市关联和优化批量复制
"""

import requests
import json

# 测试配置
base_url = 'http://localhost:5001'

def test_delete_city_association():
    """测试删除城市关联功能"""
    print('\n=== 测试删除城市关联功能 ===')
    
    # 使用一个测试规则ID和城市
    test_rule_id = 3338
    test_city = '测试城市'
    
    try:
        # 测试删除城市关联API
        response = requests.delete(
            f'{base_url}/api/rules/city-association/{test_rule_id}/{test_city}',
            timeout=10
        )
        
        print(f'删除城市关联API - 状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'响应: {data}')
            if data.get('success'):
                print('✅ 删除城市关联功能正常')
            else:
                print('❌ 删除失败:', data.get('error'))
        elif response.status_code == 404:
            data = response.json()
            print(f'⚠️ 关联关系不存在（这是正常的）: {data.get("error")}')
        else:
            print(f'❌ API错误: {response.text}')
            
    except Exception as e:
        print(f'❌ 请求失败: {e}')

def test_batch_copy_with_new_city():
    """测试批量复制到新城市功能"""
    print('\n=== 测试批量复制到新城市功能 ===')
    
    try:
        # 测试批量复制到新城市
        test_data = {
            'rule_ids': [3338],
            'target_city': '新测试城市_' + str(int(__import__('time').time()))  # 使用时间戳确保唯一性
        }
        
        response = requests.post(
            f'{base_url}/api/rules/batch-copy',
            json=test_data,
            timeout=30
        )
        
        print(f'批量复制到新城市API - 状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'响应: {json.dumps(data, ensure_ascii=False, indent=2)}')
            
            if data.get('success'):
                print('✅ 批量复制到新城市功能正常')
                if data.get('is_new_city'):
                    print(f'✅ 成功创建新城市: {data.get("target_city")}')
                else:
                    print('ℹ️ 目标城市已存在')
            else:
                print('❌ 批量复制失败:', data.get('error'))
        else:
            print(f'❌ API错误: {response.text}')
            
    except Exception as e:
        print(f'❌ 请求失败: {e}')

def test_city_validation():
    """测试城市名称验证"""
    print('\n=== 测试城市名称验证 ===')
    
    # 测试各种城市名称格式
    test_cases = [
        ('', '空城市名称'),
        ('a', '太短的城市名称'),
        ('北京', '正常中文城市名称'),
        ('New York', '英文城市名称'),
        ('北京-朝阳', '包含连字符的城市名称'),
        ('测试城市123', '包含数字的城市名称'),
        ('a' * 25, '太长的城市名称'),
        ('城市@#$', '包含特殊字符的城市名称')
    ]
    
    for city_name, description in test_cases:
        try:
            response = requests.post(
                f'{base_url}/api/rules/batch-copy',
                json={
                    'rule_ids': [3338],
                    'target_city': city_name
                },
                timeout=10
            )
            
            data = response.json()
            
            if city_name == '' or len(city_name) < 2 or len(city_name) > 20:
                # 这些应该失败
                if not data.get('success'):
                    print(f'✅ {description}: 正确拒绝')
                else:
                    print(f'❌ {description}: 应该被拒绝但成功了')
            else:
                # 这些应该成功（除非有其他错误）
                if data.get('success') or 'already exists' in str(data.get('error', '')):
                    print(f'✅ {description}: 正确接受')
                else:
                    print(f'⚠️ {description}: {data.get("error")}')
                    
        except Exception as e:
            print(f'❌ {description}: 请求失败 - {e}')

def test_api_availability():
    """测试API可用性"""
    print('\n=== 测试API可用性 ===')
    
    apis = [
        ('删除城市关联', '/api/rules/city-association/1/test'),
        ('批量复制规则', '/api/rules/batch-copy'),
        ('城市类型列表', '/api/city_types')
    ]
    
    for name, endpoint in apis:
        try:
            if 'city-association' in endpoint:
                # DELETE请求
                response = requests.delete(f'{base_url}{endpoint}', timeout=5)
            elif 'batch-copy' in endpoint:
                # POST请求
                response = requests.post(f'{base_url}{endpoint}', json={}, timeout=5)
            else:
                # GET请求
                response = requests.get(f'{base_url}{endpoint}', timeout=5)
                
            if response.status_code in [200, 400, 404, 405]:  # 这些状态码说明端点存在
                print(f'✅ {name} API 可用')
            else:
                print(f'❌ {name} API 不可用 (状态码: {response.status_code})')
                
        except Exception as e:
            print(f'❌ {name} API 测试失败: {e}')

def main():
    """主测试函数"""
    print('=' * 60)
    print('规则知识库新功能测试')
    print('=' * 60)
    
    # 运行所有测试
    test_api_availability()
    test_delete_city_association()
    test_batch_copy_with_new_city()
    test_city_validation()
    
    print('\n' + '=' * 60)
    print('测试完成')
    print('=' * 60)
    print('注意：')
    print('1. 删除城市关联功能需要在前端编辑规则时测试')
    print('2. 批量复制的城市选择模式切换需要在前端测试')
    print('3. 以上测试主要验证后端API的功能')

if __name__ == "__main__":
    main()
