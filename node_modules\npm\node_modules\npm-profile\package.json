{"name": "npm-profile", "version": "11.0.1", "description": "Library for updating an npmjs.com profile", "keywords": [], "author": "GitHub Inc.", "license": "ISC", "dependencies": {"npm-registry-fetch": "^18.0.0", "proc-log": "^5.0.0"}, "main": "./lib/index.js", "repository": {"type": "git", "url": "git+https://github.com/npm/npm-profile.git"}, "files": ["bin/", "lib/"], "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "nock": "^13.2.4", "tap": "^16.0.1"}, "scripts": {"posttest": "npm run lint", "test": "tap", "snap": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "lintfix": "npm run eslint -- --fix", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": true}}