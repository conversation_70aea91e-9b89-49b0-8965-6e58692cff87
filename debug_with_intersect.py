"""
调试WITH子句中INTERSECT处理问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sql_deep_parser import DeepSQLParser

def debug_with_intersect():
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    WITH tab1 AS (
        SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
        FROM 医保住院结算明细 WHERE 医保项目名称 = 'A'
        INTERSECT
        SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
        FROM 医保住院结算明细 WHERE 医保项目名称 = 'B'
    )
    SELECT * FROM tab1
    """
    
    # 手动调试解析过程
    import sqlglot
    from sqlglot import expressions as exp
    
    ast = sqlglot.parse_one(sql)
    print("完整AST:")
    print(f"AST类型: {type(ast)}")
    
    # 查找WITH节点
    with_node = ast.find(exp.With)
    if with_node:
        print(f"\nWITH节点找到")
        for i, cte in enumerate(with_node.expressions):
            print(f"CTE {i+1}:")
            print(f"  别名: {cte.alias}")
            print(f"  查询类型: {type(cte.this)}")
            print(f"  是否为INTERSECT: {isinstance(cte.this, exp.Intersect)}")
            
            if isinstance(cte.this, exp.Intersect):
                print("  ✅ 检测到INTERSECT")
                
                # 手动调用分析方法
                from sql_deep_parser.models import RuleLogicIR, RuleType
                rule_ir = RuleLogicIR(rule_type=RuleType.UNKNOWN, original_sql=sql)
                
                print("  调用_analyze_intersect_pattern...")
                parser._analyze_intersect_pattern(cte.this, rule_ir)
                
                print(f"  分析后的规则类型: {rule_ir.rule_type.value}")
                if rule_ir.duplicate_pattern:
                    print(f"  重复收费模式: {rule_ir.duplicate_pattern.to_dict()}")
                else:
                    print("  未生成重复收费模式")

if __name__ == "__main__":
    debug_with_intersect()
