<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正则表达式转义测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .highlight { background-color: yellow; }
    </style>
</head>
<body>
    <h1>正则表达式转义测试</h1>
    <div id="testResults"></div>

    <script>
        // 修复后的高亮函数
        function highlightCommonNames(text, commonNames) {
            if (!text || !commonNames || commonNames.length === 0) {
                return text || '';
            }

            let highlightedText = text;
            commonNames.forEach(name => {
                try {
                    // 转义正则表达式特殊字符
                    const escapedName = name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    const regex = new RegExp(`(${escapedName})`, 'gi');
                    highlightedText = highlightedText.replace(regex, '<mark class="highlight">$1</mark>');
                } catch (error) {
                    console.warn('高亮显示失败，项目名称:', name, '错误:', error);
                    // 如果正则表达式仍然失败，使用简单的字符串替换
                    const index = highlightedText.toLowerCase().indexOf(name.toLowerCase());
                    if (index !== -1) {
                        const originalText = highlightedText.substring(index, index + name.length);
                        highlightedText = highlightedText.replace(originalText, `<mark class="highlight">${originalText}</mark>`);
                    }
                }
            });
            return highlightedText;
        }

        // 测试用例
        const testCases = [
            {
                name: '正常文本',
                text: '这是一个普通的医保项目名称',
                commonNames: ['医保项目'],
                expected: '这是一个普通的<mark class="highlight">医保项目</mark>名称'
            },
            {
                name: '包含括号的项目名称',
                text: '经电子内镜食管胃十二指肠黏膜剥离术（ESD）治疗费用',
                commonNames: ['经电子内镜食管胃十二指肠黏膜剥离术（ESD）'],
                expected: '<mark class="highlight">经电子内镜食管胃十二指肠黏膜剥离术（ESD）</mark>治疗费用'
            },
            {
                name: '包含特殊字符',
                text: '项目A+项目B*项目C?项目D',
                commonNames: ['项目A+', '项目B*', '项目C?'],
                expected: '<mark class="highlight">项目A+</mark><mark class="highlight">项目B*</mark><mark class="highlight">项目C?</mark>项目D'
            },
            {
                name: '包含方括号',
                text: '检查项目[血常规]和检查项目[尿常规]',
                commonNames: ['检查项目[血常规]'],
                expected: '<mark class="highlight">检查项目[血常规]</mark>和检查项目[尿常规]'
            },
            {
                name: '包含花括号',
                text: '治疗方案{方案A}和治疗方案{方案B}',
                commonNames: ['治疗方案{方案A}'],
                expected: '<mark class="highlight">治疗方案{方案A}</mark>和治疗方案{方案B}'
            }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('testResults');
            let allPassed = true;

            testCases.forEach((testCase, index) => {
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';

                try {
                    const result = highlightCommonNames(testCase.text, testCase.commonNames);
                    const passed = result === testCase.expected;
                    
                    if (passed) {
                        testDiv.classList.add('success');
                    } else {
                        testDiv.classList.add('error');
                        allPassed = false;
                    }

                    testDiv.innerHTML = `
                        <h3>测试 ${index + 1}: ${testCase.name} ${passed ? '✅' : '❌'}</h3>
                        <p><strong>输入文本:</strong> ${testCase.text}</p>
                        <p><strong>匹配项目:</strong> ${testCase.commonNames.join(', ')}</p>
                        <p><strong>预期结果:</strong> ${testCase.expected}</p>
                        <p><strong>实际结果:</strong> ${result}</p>
                        <p><strong>渲染效果:</strong> <span>${result}</span></p>
                    `;
                } catch (error) {
                    testDiv.classList.add('error');
                    testDiv.innerHTML = `
                        <h3>测试 ${index + 1}: ${testCase.name} ❌</h3>
                        <p><strong>错误:</strong> ${error.message}</p>
                    `;
                    allPassed = false;
                }

                resultsDiv.appendChild(testDiv);
            });

            // 总结
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `test-case ${allPassed ? 'success' : 'error'}`;
            summaryDiv.innerHTML = `
                <h2>测试总结 ${allPassed ? '✅' : '❌'}</h2>
                <p>共 ${testCases.length} 个测试用例，${allPassed ? '全部通过' : '存在失败'}</p>
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // 页面加载后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
