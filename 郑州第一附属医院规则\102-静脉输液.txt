-----  14元
SELECT /*+PARALLEL(12)*/
A.BRIDGE_ID 病案关联字段,
A.HISID 结算单据号,
A.DISCHARGE_DEPT_NAME 出院科室名称,
A.DISCHARGE_DATE  出院日期,
A<PERSON>ZYTS  住院天数,
D.EXCUTE_DEPT_NAME 病区,
D.DOCTOR_NAME 医生姓名,
D.EXCUTE_DEPT_NAME 科室,
D.ITEM_ID 医保项目编码,
D.ITEM_NAME 医保项目名称,
D.NUM1 静脉输液总数量,
D.COST1 静脉输液总金额,
D.BMI_CONVERED_AMOUNT 静脉输液总医保范围内金额,
E.NUM1 输液器总数量,
E.COST1 输液器总金额,
E.BMI_CONVERED_AMOUNT 输液器总医保范围内金额,
14 *(D.NUM1 - E.NUM1) 差额
  
FROM
    (  
 
  SELECT
  B.HISID  ,
  B.ITEM_ID ,
  B.BILLING_DEPT_NAME ,
  B.EXCUTE_DEPT_NAME,
  B.DOCTOR_NAME ,
  B.ITEM_NAME ,
  sum(B.NUM)  AS NUM1 ,
  sum(B.COST) AS COST1,
  sum(B.BMI_CONVERED_AMOUNT) AS BMI_CONVERED_AMOUNT
  FROM
  住院结算明细_全 B
WHERE B. ITEM_NAME = '静脉输液'
and B.UNIT_PRICE = '14'
group  by 
  B.HISID  ,
  B.ITEM_ID ,
  B.BILLING_DEPT_NAME ,
  B.EXCUTE_DEPT_NAME,
  B.DOCTOR_NAME ,
  B.ITEM_NAME 
  
  ) D  
  
   JOIN  (  
 
   SELECT
  B.HISID  ,
  B.ITEM_ID ,
  B.BILLING_DEPT_NAME ,
  B.EXCUTE_DEPT_NAME,
  B.DOCTOR_NAME ,
  B.ITEM_NAME ,
  sum(B.NUM)  AS NUM1 ,
  sum(B.COST) AS COST1,
  sum(B.BMI_CONVERED_AMOUNT) AS BMI_CONVERED_AMOUNT
  FROM
  住院结算明细_全 B
WHERE B. ITEM_NAME like '%输液器%'
group  by 
  B.HISID  ,
  B.ITEM_ID ,
  B.BILLING_DEPT_NAME ,
  B.EXCUTE_DEPT_NAME,
  B.DOCTOR_NAME ,
  B.ITEM_NAME 
  )E  ON   E.HISID  = D.HISID
  JOIN  住院结算主单_全  A  ON   E.HISID = A.HISID
 where  D.NUM1 > E.NUM1 ;


-----  15.6元
SELECT /*+PARALLEL(12)*/
A.BRIDGE_ID 病案关联字段,
A.HISID 结算单据号,
A.DISCHARGE_DEPT_NAME 出院科室名称,
A.DISCHARGE_DATE  出院日期,
A.ZYTS  住院天数,
D.EXCUTE_DEPT_NAME 病区,
D.DOCTOR_NAME 医生姓名,
D.EXCUTE_DEPT_NAME 科室,
D.ITEM_ID 医保项目编码,
D.ITEM_NAME 医保项目名称,
D.NUM1 静脉输液总数量,
D.COST1 静脉输液总金额,
D.BMI_CONVERED_AMOUNT 静脉输液总医保范围内金额,
E.NUM1 输液器总数量,
E.COST1 输液器总金额,
E.BMI_CONVERED_AMOUNT 输液器总医保范围内金额,
15.6 *(D.NUM1 - E.NUM1) 差额
  
FROM
    (  
 
  SELECT
  B.HISID  ,
  B.ITEM_ID ,
  B.BILLING_DEPT_NAME ,
  B.EXCUTE_DEPT_NAME,
  B.DOCTOR_NAME ,
  B.ITEM_NAME ,
  sum(B.NUM)  AS NUM1 ,
  sum(B.COST) AS COST1,
  sum(B.BMI_CONVERED_AMOUNT) AS BMI_CONVERED_AMOUNT
  FROM
  住院结算明细_全 B
WHERE B. ITEM_NAME = '静脉输液'
and B.UNIT_PRICE = '15.6'
group  by 
  B.HISID  ,
  B.ITEM_ID ,
  B.BILLING_DEPT_NAME ,
  B.EXCUTE_DEPT_NAME,
  B.DOCTOR_NAME ,
  B.ITEM_NAME 
  
  ) D  
  
   JOIN  (  
 
   SELECT
  B.HISID  ,
  B.ITEM_ID ,
  B.BILLING_DEPT_NAME ,
  B.EXCUTE_DEPT_NAME,
  B.DOCTOR_NAME ,
  B.ITEM_NAME ,
  sum(B.NUM)  AS NUM1 ,
  sum(B.COST) AS COST1,
  sum(B.BMI_CONVERED_AMOUNT) AS BMI_CONVERED_AMOUNT
  FROM
  住院结算明细_全 B
WHERE B. ITEM_NAME like '%输液器%'
group  by 
  B.HISID  ,
  B.ITEM_ID ,
  B.BILLING_DEPT_NAME ,
  B.EXCUTE_DEPT_NAME,
  B.DOCTOR_NAME ,
  B.ITEM_NAME 
  )E  ON   E.HISID  = D.HISID
  JOIN  住院结算主单_全  A  ON   E.HISID = A.HISID
 where  D.NUM1 > E.NUM1 ;