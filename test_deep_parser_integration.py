"""
测试深度SQL解析器集成
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_deep_parser_integration():
    """测试深度SQL解析器集成"""
    print("=== 测试深度SQL解析器集成 ===")
    
    # 测试复杂的医保规则SQL
    sql_content = """
    -- 规则名称: 血液透析与血液灌流同日重复收费检测
    -- 城市: 北京市
    -- 行为认定: 重复收费
    WITH duplicate_check AS (
        SELECT A.结算单据号, to_char(B.项目使用日期,'yyyy-MM-dd hh24') 使用日期,
               SUM(B.数量) AS 总数量,
               COUNT(DISTINCT B.医保项目名称) AS 项目种类数
        FROM 医保住院结算主单 A
        JOIN 医保住院结算明细 B ON A.结算单据号 = B.结算单据号
        WHERE A.患者年龄 BETWEEN 18 AND 80
          AND A.患者性别 = '男'
          AND B.医保项目名称 IN ('血液透析', '血液灌流')
        GROUP BY A.结算单据号, to_char(B.项目使用日期,'yyyy-MM-dd hh24')
        HAVING SUM(B.数量) > 1 AND COUNT(DISTINCT B.医保项目名称) > 1
    ),
    intersect_pattern AS (
        SELECT 结算单据号, 使用日期 FROM 医保住院结算明细 
        WHERE 医保项目名称 = '血液透析'
        INTERSECT
        SELECT 结算单据号, 使用日期 FROM 医保住院结算明细 
        WHERE 医保项目名称 = '血液灌流'
    )
    SELECT D.结算单据号, D.使用日期, D.总数量, D.项目种类数
    FROM duplicate_check D
    JOIN intersect_pattern I ON D.结算单据号 = I.结算单据号 AND D.使用日期 = I.使用日期
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ 深度解析成功")
            
            rule_info = result['rule_info']
            explanation = result['explanation']
            deep_analysis = result['deep_analysis']
            
            print(f"\n📋 基本信息:")
            print(f"  规则名称: {rule_info['rule_name']}")
            print(f"  规则类型: {rule_info['rule_type']}")
            print(f"  城市: {rule_info['city']}")
            print(f"  行为认定: {rule_info['behavior']}")
            print(f"  置信度分数: {rule_info['confidence_score']:.2f}")
            
            print(f"\n🔍 深度分析:")
            print(f"  数据源数量: {deep_analysis['data_sources_count']}")
            print(f"  条件数量: {deep_analysis['conditions_count']}")
            print(f"  聚合函数数量: {deep_analysis['aggregations_count']}")
            print(f"  重复收费模式: {'检测到' if deep_analysis['has_duplicate_pattern'] else '未检测到'}")
            
            print(f"\n💡 分类解释:")
            print(f"  置信度: {explanation['confidence']:.2f}")
            for exp in explanation['explanations']:
                print(f"  - {exp}")
            
            print(f"\n🏥 医保项目:")
            for item in rule_info['medical_items']:
                print(f"  - {item}")
            
            print(f"\n📊 数据源:")
            for ds in rule_info['data_sources']:
                print(f"  - {ds['table_name']} ({ds['alias']})")
            
            print(f"\n🧮 聚合函数:")
            for agg in rule_info['aggregations']:
                print(f"  - {agg['function']}({agg['field']['field_name']})")
            
            if rule_info['duplicate_pattern']:
                print(f"\n🔄 重复收费模式:")
                dp = rule_info['duplicate_pattern']
                print(f"  主要项目: {dp['primary_items']}")
                print(f"  冲突项目: {dp['conflict_items']}")
                print(f"  时间精度: {dp['time_precision']}")
                print(f"  检测方法: {dp['detection_method']}")
            
            return True
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        print(response.text)
        return False


def test_simple_age_restriction():
    """测试简单年龄限制规则"""
    print("\n=== 测试年龄限制规则 ===")
    
    sql_content = """
    -- 规则名称: 儿童用药年龄限制
    -- 城市: 上海市
    -- 行为认定: 年龄限制
    SELECT * FROM 医保住院结算明细 B
    JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
    WHERE A.患者年龄 < 18 AND B.医保项目名称 = '成人专用药物'
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ 年龄限制规则解析成功")
            print(f"  规则类型: {result['rule_info']['rule_type']}")
            print(f"  条件数量: {result['deep_analysis']['conditions_count']}")

            rule_type_ok = result['rule_info']['rule_type'] == '年龄限制'
            conditions_ok = result['deep_analysis']['conditions_count'] >= 2
            print(f"  规则类型正确: {rule_type_ok}")
            print(f"  条件数量足够: {conditions_ok}")

            return rule_type_ok and conditions_ok
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


def test_excessive_usage_rule():
    """测试超量使用规则"""
    print("\n=== 测试超量使用规则 ===")
    
    sql_content = """
    -- 规则名称: 检查项目超量使用检测
    -- 城市: 广州市
    -- 行为认定: 超量使用
    SELECT 结算单据号, SUM(数量) as 总数量
    FROM 医保住院结算明细
    WHERE 医保项目名称 = 'CT检查'
    GROUP BY 结算单据号
    HAVING SUM(数量) > 3
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ 超量使用规则解析成功")
            print(f"  规则类型: {result['rule_info']['rule_type']}")
            print(f"  聚合函数数量: {result['deep_analysis']['aggregations_count']}")

            rule_type_ok = result['rule_info']['rule_type'] == '超量使用'
            agg_count_ok = result['deep_analysis']['aggregations_count'] >= 1
            print(f"  规则类型正确: {rule_type_ok}")
            print(f"  聚合函数足够: {agg_count_ok}")

            return rule_type_ok and agg_count_ok
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


def main():
    """主测试函数"""
    print("开始测试深度SQL解析器集成...\n")
    
    tests = [
        ("复杂重复收费规则", test_deep_parser_integration),
        ("年龄限制规则", test_simple_age_restriction),
        ("超量使用规则", test_excessive_usage_rule)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 深度SQL解析器集成测试全部通过！")
        print("✨ 页面功能已成功升级，可以开始使用深度解析功能。")
    else:
        print("⚠️  存在失败的测试，需要进一步调试。")


if __name__ == "__main__":
    main()
