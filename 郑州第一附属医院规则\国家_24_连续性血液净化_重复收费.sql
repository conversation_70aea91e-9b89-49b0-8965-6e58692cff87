WITH tab1 AS(
SELECT
	结算单据号,
	TRUNC(项目使用日期) 项目使用日期
FROM
	医保住院结算明细 a
WHERE
	医保项目名称 LIKE '%连续性血液净化%'
GROUP BY
	结算单据号,
	TRUNC(项目使用日期)
HAVING
	sum(数量)>24)
SELECT
	b.医疗机构编码,
	b.医疗机构名称,
	b.结算单据号,
	b.住院号,
	b.险种类型,
	b.个人编码,
	b.患者社会保障号码,
	b.患者姓名,
	b.患者性别,
	b.患者年龄,
	b.出院科室,
	b.入院日期,
	b.出院日期,
	b.结算日期,
	a.项目使用日期,
	b.入院诊断名称,
	b.出院诊断名称,
	a.医保项目编码,
	a.医保项目名称,
	a.单价,
	a.数量,
	a.金额,
	a.医保范围内金额,
	b.医保支付方式
FROM
	医保住院结算明细 a JOIN 医保住院结算主单 b ON a.结算单据号 = b.结算单据号
	JOIN tab1 ON a.结算单据号 = tab1.结算单据号 AND tab1.项目使用日期 = TRUNC(a.项目使用日期) 
WHERE
	a.医保项目名称 LIKE '%连续性血液净化%'
ORDER BY
	a.结算单据号,
	a.项目使用日期	
	