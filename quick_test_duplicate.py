#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试重复规则分析功能

这个脚本会测试：
1. 测试API是否可以访问
2. 真实API是否工作正常
3. 显示详细的错误信息

使用方法：
python quick_test_duplicate.py
"""

import requests
import json
import sys

def test_apis():
    base_url = "http://localhost:5000"
    hospital_id = 1  # 测试医院ID
    
    print("=" * 60)
    print("重复规则分析API快速测试")
    print("=" * 60)
    
    # 测试1: 基本连接
    print("\n1. 测试基本连接...")
    try:
        response = requests.get(f"{base_url}/api/hospitals", timeout=5)
        if response.status_code == 200:
            print("✓ 服务器连接正常")
        else:
            print(f"✗ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 无法连接到服务器: {e}")
        print("请确保后端服务正在运行在 http://localhost:5000")
        return False
    
    # 测试2: 测试API
    print("\n2. 测试简单API...")
    try:
        response = requests.get(f"{base_url}/api/hospital-rules/duplicate-analysis-test/{hospital_id}", timeout=10)
        print(f"   HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✓ 测试API工作正常")
            else:
                print(f"✗ 测试API返回错误: {data.get('error')}")
        else:
            print(f"✗ 测试API HTTP错误: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
    except Exception as e:
        print(f"✗ 测试API异常: {e}")
    
    # 测试3: 真实API
    print("\n3. 测试真实重复规则分析API...")
    try:
        response = requests.get(f"{base_url}/api/hospital-rules/duplicate-analysis/{hospital_id}", timeout=30)
        print(f"   HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   响应数据结构:")
            print(f"   - success: {data.get('success')}")
            print(f"   - total_rules: {data.get('total_rules')}")
            print(f"   - duplicate_rules: {data.get('duplicate_rules')}")
            print(f"   - duplicate_groups_count: {data.get('duplicate_groups_count')}")
            
            if data.get('success'):
                print("✓ 真实API工作正常")
                
                # 显示详细结果
                if data.get('duplicate_groups'):
                    print(f"\n   发现 {len(data['duplicate_groups'])} 个重复组:")
                    for i, group in enumerate(data['duplicate_groups'][:3]):  # 只显示前3组
                        print(f"   组 {i+1}: {group.get('rule_count', 0)} 条规则, 相似度 {group.get('similarity', 0)}")
                        print(f"        共同项目: {', '.join(group.get('common_medical_names', []))}")
                else:
                    print("   没有发现重复规则")
                    
            else:
                print(f"✗ 真实API返回错误: {data.get('error')}")
                if data.get('details'):
                    print(f"   详细错误信息:")
                    print(f"   {data.get('details')}")
        else:
            print(f"✗ 真实API HTTP错误: {response.status_code}")
            print(f"   响应内容: {response.text[:500]}...")
            
    except Exception as e:
        print(f"✗ 真实API异常: {e}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    return True

def main():
    try:
        test_apis()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
