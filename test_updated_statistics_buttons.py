#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的统计按钮功能

验证统计按钮功能定义的修改：
- 重复筛查警告按钮：显示有重复的推荐规则
- 无重复推荐按钮：显示没有重复的推荐规则  
- 显示全部按钮：显示所有规则

使用方法：
python test_updated_statistics_buttons.py [hospital_id]

作者: Augment Agent
日期: 2025-07-24
"""

import sys
import requests
import json
from datetime import datetime

def test_updated_statistics_logic(hospital_id=9):
    """测试修改后的统计逻辑"""
    print("=" * 80)
    print("测试: 修改后的统计按钮逻辑")
    print("=" * 80)
    
    base_url = "http://localhost:5001"
    
    try:
        print(f"1. 调用推荐规则生成API，医院ID: {hospital_id}")
        
        response = requests.post(f"{base_url}/api/hospital-rules/generate", 
                               json={"hospital_id": hospital_id}, 
                               timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                recommendations = result.get('recommendations', [])
                
                print(f"✓ API调用成功，推荐规则数量: {len(recommendations)}")
                
                # 按照新的统计逻辑计算
                recommended_rules = [rec for rec in recommendations if rec.get('状态') == '推荐']
                duplicate_warning_rules = [rec for rec in recommended_rules 
                                         if rec.get('重复已采用规则') and len(rec.get('重复已采用规则', [])) > 0]
                clean_recommended_rules = [rec for rec in recommended_rules 
                                         if not rec.get('重复已采用规则') or len(rec.get('重复已采用规则', [])) == 0]
                
                print(f"\n2. 修改后的统计逻辑:")
                print(f"   总推荐规则数: {len(recommended_rules)}")
                print(f"   有重复筛查警告的推荐规则: {len(duplicate_warning_rules)}")
                print(f"   无重复的推荐规则: {len(clean_recommended_rules)}")
                print(f"   验证: {len(duplicate_warning_rules)} + {len(clean_recommended_rules)} = {len(duplicate_warning_rules) + len(clean_recommended_rules)} (应等于 {len(recommended_rules)})")
                
                # 验证逻辑正确性
                if len(duplicate_warning_rules) + len(clean_recommended_rules) == len(recommended_rules):
                    print(f"   ✓ 统计逻辑正确：有重复 + 无重复 = 总推荐规则")
                else:
                    print(f"   ❌ 统计逻辑错误：数字不匹配")
                
                print(f"\n3. 预期的按钮显示:")
                print(f"   重复筛查警告按钮: 黄色，数字 {len(duplicate_warning_rules)}")
                print(f"   无重复推荐按钮: 蓝色，数字 {len(clean_recommended_rules)}")
                print(f"   显示全部按钮: 灰色，无数字")
                
                print(f"\n4. 功能对比:")
                print(f"   修改前 - 推荐未采用按钮: 显示所有推荐规则 ({len(recommended_rules)}条)")
                print(f"   修改后 - 无重复推荐按钮: 显示无重复的推荐规则 ({len(clean_recommended_rules)}条)")
                print(f"   改进效果: 用户可以清楚区分有重复问题和无重复问题的推荐规则")
                
                # 显示一些示例
                if duplicate_warning_rules:
                    print(f"\n5. 有重复筛查警告的规则示例:")
                    for i, rule in enumerate(duplicate_warning_rules[:3], 1):
                        print(f"   {i}. 规则ID: {rule.get('规则ID')}")
                        print(f"      规则名称: {rule.get('规则名称')}")
                        print(f"      重复规则数量: {len(rule.get('重复已采用规则', []))}")
                
                if clean_recommended_rules:
                    print(f"\n6. 无重复的推荐规则示例:")
                    for i, rule in enumerate(clean_recommended_rules[:3], 1):
                        print(f"   {i}. 规则ID: {rule.get('规则ID')}")
                        print(f"      规则名称: {rule.get('规则名称')}")
                        print(f"      重复规则: 无")
                
                return True, {
                    'total_recommended': len(recommended_rules),
                    'duplicate_warning': len(duplicate_warning_rules),
                    'clean_recommended': len(clean_recommended_rules)
                }
            else:
                print(f"❌ API返回错误: {result.get('error')}")
                return False, None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_button_functionality():
    """测试按钮功能"""
    print("\n" + "=" * 80)
    print("测试: 修改后的按钮功能")
    print("=" * 80)
    
    print("验证修改后的按钮功能:")
    
    button_functions = [
        {
            'button': '重复筛查警告按钮',
            'text': '重复筛查警告',
            'color': 'btn-warning (黄色)',
            'icon': 'bi-exclamation-triangle',
            'function': 'filterDuplicateWarningRules()',
            'logic': '过滤显示状态为推荐且有重复已采用规则的规则',
            'filter_type': 'duplicate'
        },
        {
            'button': '无重复推荐按钮',
            'text': '无重复推荐',
            'color': 'btn-info (蓝色)',
            'icon': 'bi-lightbulb',
            'function': 'filterCleanRecommendedRules()',
            'logic': '过滤显示状态为推荐且没有重复已采用规则的规则',
            'filter_type': 'clean'
        },
        {
            'button': '显示全部按钮',
            'text': '显示全部',
            'color': 'btn-secondary (灰色)',
            'icon': 'bi-list',
            'function': 'showAllRules()',
            'logic': '取消过滤，显示所有规则',
            'filter_type': 'all'
        }
    ]
    
    for button in button_functions:
        print(f"\n✓ {button['button']}:")
        print(f"  按钮文本: {button['text']}")
        print(f"  样式: {button['color']}")
        print(f"  图标: {button['icon']}")
        print(f"  函数: {button['function']}")
        print(f"  过滤逻辑: {button['logic']}")
        print(f"  过滤类型: {button['filter_type']}")
    
    print(f"\n✓ 修改要点:")
    print(f"  1. 按钮文本: '推荐未采用' → '无重复推荐'")
    print(f"  2. 函数名称: filterRecommendedRules() → filterCleanRecommendedRules()")
    print(f"  3. 统计逻辑: 所有推荐规则 → 无重复的推荐规则")
    print(f"  4. 过滤类型: 'recommended' → 'clean'")
    print(f"  5. 提示文本: 更准确的描述")
    
    return True

def test_user_experience():
    """测试用户体验改进"""
    print("\n" + "=" * 80)
    print("测试: 用户体验改进")
    print("=" * 80)
    
    print("验证用户体验改进:")
    
    improvements = [
        {
            'aspect': '功能区分度',
            'before': '重复筛查警告 + 推荐未采用（包含有重复的）',
            'after': '重复筛查警告 + 无重复推荐（不包含有重复的）',
            'benefit': '用户可以清楚区分有重复问题和无重复问题的规则'
        },
        {
            'aspect': '按钮语义',
            'before': '推荐未采用（语义模糊）',
            'after': '无重复推荐（语义明确）',
            'benefit': '按钮名称更准确地反映其功能'
        },
        {
            'aspect': '工作流程',
            'before': '需要在所有推荐规则中手动识别重复问题',
            'after': '可以直接查看没有重复问题的推荐规则',
            'benefit': '提高工作效率，减少人工筛选'
        },
        {
            'aspect': '数据完整性',
            'before': '重复筛查警告 + 推荐未采用 ≠ 互补关系',
            'after': '重复筛查警告 + 无重复推荐 = 所有推荐规则',
            'benefit': '统计逻辑更清晰，数据关系更明确'
        }
    ]
    
    for improvement in improvements:
        print(f"\n✓ {improvement['aspect']}:")
        print(f"  修改前: {improvement['before']}")
        print(f"  修改后: {improvement['after']}")
        print(f"  用户收益: {improvement['benefit']}")
    
    print(f"\n✓ 预期的用户操作流程:")
    print(f"  1. 查看统计按钮了解规则分布")
    print(f"  2. 点击'重复筛查警告'查看需要处理的重复问题")
    print(f"  3. 点击'无重复推荐'查看可以直接采用的规则")
    print(f"  4. 点击'显示全部'查看完整的规则列表")
    
    return True

def main():
    """主测试函数"""
    hospital_id = 9
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("修改后的统计按钮功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试医院ID: {hospital_id}")
    
    try:
        # 测试1: 统计逻辑
        success1, stats = test_updated_statistics_logic(hospital_id)
        
        # 测试2: 按钮功能
        success2 = test_button_functionality()
        
        # 测试3: 用户体验
        success3 = test_user_experience()
        
        # 输出测试结果
        print("\n" + "=" * 80)
        print("测试结果汇总")
        print("=" * 80)
        
        print(f"统计逻辑测试: {'✓ 通过' if success1 else '❌ 失败'}")
        print(f"按钮功能测试: {'✓ 通过' if success2 else '❌ 失败'}")
        print(f"用户体验测试: {'✓ 通过' if success3 else '❌ 失败'}")
        
        if success1 and success2 and success3:
            print("\n🎉 修改后的统计按钮功能测试通过！")
            
            if stats:
                print(f"\n📊 统计数据验证:")
                print(f"  总推荐规则: {stats['total_recommended']}")
                print(f"  有重复筛查警告: {stats['duplicate_warning']}")
                print(f"  无重复推荐: {stats['clean_recommended']}")
                print(f"  数据验证: {stats['duplicate_warning']} + {stats['clean_recommended']} = {stats['duplicate_warning'] + stats['clean_recommended']} ✓")
            
            print(f"\n📋 用户验证步骤:")
            print(f"1. 打开浏览器访问 http://localhost:5001/hospital_rules")
            print(f"2. 选择医院ID {hospital_id}")
            print(f"3. 点击'生成推荐'按钮")
            print(f"4. 查看医院信息区域的统计按钮:")
            print(f"   - 重复筛查警告按钮（黄色）")
            print(f"   - 无重复推荐按钮（蓝色）")
            print(f"   - 显示全部按钮（灰色）")
            print(f"5. 点击各个按钮测试过滤功能")
            print(f"6. 验证按钮文本和统计数字是否正确")
            
            print(f"\n🎯 修改完成确认:")
            print(f"✅ 按钮文本已修改: '推荐未采用' → '无重复推荐'")
            print(f"✅ 统计逻辑已修改: 所有推荐规则 → 无重复的推荐规则")
            print(f"✅ 过滤功能已修改: filterRecommendedRules() → filterCleanRecommendedRules()")
            print(f"✅ 提示文本已修改: 更准确的功能描述")
            print(f"✅ 两个位置都已修改: displayRecommendations + loadAllRulesDirectly")
            
            return True
        else:
            print("\n❌ 部分测试失败，请检查修改。")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
