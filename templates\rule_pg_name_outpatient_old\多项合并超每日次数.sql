with tab1 as(
  select 
    结算单据号, 
  (b.项目使用日期 :: DATE) 项目使用日期,
    sum(数量) as 使用数量
   FROM 医保门诊结算明细 b
   WHERE 医保项目名称 in ({医保名称1})
   group by 
    结算单据号, (b.项目使用日期 :: DATE)
)
select 
 a.结算单据号,
 a.住院号,
 --a.病案号,
 a.个人编码,
 a.患者姓名,
 a.患者性别,
 a.患者社会保障号码,
 a.患者年龄,
 a.险种类型,
 --a.入院科室,
 a.入院诊断名称,
 a.入院日期,
 a.出院科室,
 a.出院日期,
 a.出院诊断名称,
 (a.出院日期 :: DATE) - (a.入院日期 :: DATE) as 住院天数,
 a.结算日期,
 c.医保项目名称,
 b.项目使用日期,
 c.单价,
 sum(c.数量) as 数量,
 (sum(c.数量) - {违规数量}) as 超出数量
from 医保门诊结算主单 a
join tab1 B on a.结算单据号 = b.结算单据号 
join 医保门诊结算明细 c on b.结算单据号 = c.结算单据号 and b.项目使用日期=(c.项目使用日期 :: DATE)
and c.医保项目名称 in ({医保名称1})
and not A.诊断名称 ~* '({排除诊断})'
and not B.开单科室名称 ~* '({排除科室})'
and b.使用数量>  {违规数量}  
group by  
a.结算单据号,
 a.住院号,
 --a.病案号,
 a.个人编码,
 a.患者姓名,
 a.患者性别,
 a.患者社会保障号码,
 a.患者年龄,
 a.险种类型,
 --a.入院科室,
 a.入院诊断名称,
 a.入院日期,
 a.出院科室,
 a.出院日期,
 a.出院诊断名称,
 (a.出院日期 :: DATE) - (a.入院日期 :: DATE) ,
 a.结算日期,
 c.医保项目名称,
 b.项目使用日期,
 c.单价
