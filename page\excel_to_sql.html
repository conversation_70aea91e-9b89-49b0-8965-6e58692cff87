<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel转SQL工具</title>
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #f5f5f5;
            --text-color: #333;
            --border-color: #ddd;
            --success-color: #4caf50;
            --error-color: #f44336;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 2rem;
            background-color: #f0f2f5;
            color: var(--text-color);
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .back-button {
            background-color: var(--secondary-color);
            color: var(--text-color);
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        h1 {
            color: var(--primary-color);
            margin: 0;
        }

        .form-section {
            background-color: var(--secondary-color);
            padding: 1.5rem;
            border-radius: 6px;
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        input[type="file"],
        input[type="text"],
        select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 1rem;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn {
            background-color: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.2s;
        }

        .btn:hover {
            opacity: 0.9;
        }

        .preview-section {
            margin-top: 1.5rem;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            display: none;
        }

        .flash-message {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .flash-error {
            background-color: #fee2e2;
            color: var(--error-color);
        }

        .flash-success {
            background-color: #dcfce7;
            color: var(--success-color);
        }
    </style>
</head>
<body>


    <div class="container">

        <div class="header">
            <a href="/" class="back-button">← 返回首页</a>
            <h1>Excel转SQL工具</h1>
            <div style="width: 80px;"></div>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message flash-{{ category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form action="{{ url_for('process_excel_to_sql') }}" method="post" enctype="multipart/form-data">
            <div class="form-section">
                <div class="form-group">
                    <label for="file">选择Excel文件：</label>
                    <input type="file" id="file" name="file" accept=".xlsx,.xls" required>
                    <small style="color: #666;">支持的格式: .xlsx, .xls</small>
                </div>

                <div class="form-group">
                    <label>Excel文件必须包含以下列：</label>
                    <ul style="color: #666;">
                        <li>规则名称（必需）</li>
                        <li>SQL 或 sql（必需）</li>
                        <li>规则内涵（可选）</li>
                        <li>政策依据（可选）</li>
                    </ul>
                </div>
            </div>

            <div class="form-section">
                <h3>生成选项</h3>
                <div class="options-grid">
                    <div class="checkbox-group">
                        <input type="checkbox" id="include_description" name="include_description" checked>
                        <label for="include_description">包含规则内涵注释</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="include_policy" name="include_policy" checked>
                        <label for="include_policy">包含政策依据注释</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="create_separate_files" name="create_separate_files" checked>
                        <label for="create_separate_files">生成独立SQL文件</label>
                    </div>
                    <div class="checkbox-group">
                        <input type="checkbox" id="zip_output" name="zip_output" checked>
                        <label for="zip_output">打包为ZIP文件</label>
                    </div>
                </div>
            </div>

            <button type="submit" class="btn">生成SQL文件</button>
        </form>

        <div id="preview-section" class="preview-section">
            <h3>处理进度</h3>
            <div id="progress-bar" style="display: none;">
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 文件类型验证
        document.getElementById('file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const extension = file.name.split('.').pop().toLowerCase();
                if (!['xlsx', 'xls'].includes(extension)) {
                    alert('请选择正确的Excel文件格式（.xlsx 或 .xls）');
                    this.value = '';
                }
            }
        });
    </script>
</body>
</html>