#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试医院筛选功能的API
"""

import requests
import json
import sys

def test_hospital_filter_api():
    """测试医院筛选功能的完整流程"""
    base_url = 'http://localhost:5001'
    
    print("=== 测试医院筛选功能API ===")
    
    # 1. 获取医院列表
    try:
        response = requests.get(f'{base_url}/api/hospitals', timeout=10)
        print(f'获取医院列表 - 状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('hospitals'):
                hospitals = data['hospitals']
                print(f'找到 {len(hospitals)} 家医院')
                
                # 选择第一家医院进行测试
                if hospitals:
                    hospital = hospitals[0]
                    hospital_id = hospital['医院ID']
                    hospital_name = hospital['医院名称']
                    print(f'测试医院: {hospital_name} (ID: {hospital_id})')
                    
                    # 2. 获取该医院的已采用规则
                    try:
                        response = requests.get(f'{base_url}/api/hospital-rules/adopted/{hospital_id}', timeout=10)
                        print(f'获取已采用规则 - 状态码: {response.status_code}')
                        
                        if response.status_code == 200:
                            data = response.json()
                            if data.get('success') and data.get('rules'):
                                rules = data['rules']
                                print(f'该医院有 {len(rules)} 条已采用规则')
                                
                                if rules:
                                    # 提取规则ID
                                    rule_ids = [rule['规则ID'] for rule in rules]
                                    print(f'规则ID列表: {rule_ids[:5]}...' if len(rule_ids) > 5 else f'规则ID列表: {rule_ids}')
                                    
                                    # 3. 测试根据规则ID获取对照ID的API
                                    try:
                                        response = requests.post(f'{base_url}/api/get_compare_ids_by_rule_ids', 
                                                               json={'rule_ids': rule_ids}, timeout=10)
                                        print(f'获取对照ID - 状态码: {response.status_code}')
                                        
                                        if response.status_code == 200:
                                            data = response.json()
                                            if data.get('success'):
                                                compare_ids = data.get('compare_ids', [])
                                                print(f'获取到 {len(compare_ids)} 个对照ID')
                                                print(f'对照ID示例: {compare_ids[:10]}' if len(compare_ids) > 10 else f'对照ID列表: {compare_ids}')
                                                
                                                # 4. 测试系统规则筛选
                                                try:
                                                    response = requests.get(f'{base_url}/api/sql_history', timeout=10)
                                                    print(f'获取系统规则 - 状态码: {response.status_code}')
                                                    
                                                    if response.status_code == 200:
                                                        system_rules = response.json()
                                                        print(f'系统中共有 {len(system_rules)} 条规则')
                                                        
                                                        # 筛选匹配的规则
                                                        matched_rules = [rule for rule in system_rules 
                                                                       if str(rule.get('compare_id')) in compare_ids]
                                                        
                                                        print(f'匹配的规则数量: {len(matched_rules)}')
                                                        
                                                        if matched_rules:
                                                            print("匹配规则示例:")
                                                            for i, rule in enumerate(matched_rules[:3]):
                                                                print(f"  {i+1}. 对照ID: {rule.get('compare_id')}, "
                                                                     f"规则名称: {rule.get('rule_name')}, "
                                                                     f"城市: {rule.get('city')}")
                                                        
                                                        print("\n=== 测试结果总结 ===")
                                                        print(f"医院: {hospital_name}")
                                                        print(f"已采用规则数: {len(rules)}")
                                                        print(f"对照ID数: {len(compare_ids)}")
                                                        print(f"匹配的系统规则数: {len(matched_rules)}")
                                                        print("✅ 医院筛选功能API测试成功！")
                                                        
                                                    else:
                                                        print(f'获取系统规则失败: {response.status_code}')
                                                        
                                                except Exception as e:
                                                    print(f'测试系统规则筛选失败: {e}')
                                                    
                                            else:
                                                print(f'获取对照ID失败: {data.get("error")}')
                                        else:
                                            print(f'获取对照ID HTTP错误: {response.status_code}')
                                            
                                    except Exception as e:
                                        print(f'测试获取对照ID API失败: {e}')
                                        
                                else:
                                    print("该医院没有已采用规则，无法进行筛选测试")
                            else:
                                print(f'获取已采用规则失败: {data.get("error")}')
                        else:
                            print(f'获取已采用规则HTTP错误: {response.status_code}')
                            
                    except Exception as e:
                        print(f'获取已采用规则失败: {e}')
                        
                else:
                    print("没有找到医院数据")
            else:
                print(f'获取医院列表失败: {data.get("error") if data else "无响应数据"}')
        else:
            print(f'获取医院列表HTTP错误: {response.status_code}')
            
    except Exception as e:
        print(f'请求医院列表失败: {e}')

def test_api_only():
    """仅测试新增的API接口"""
    base_url = 'http://localhost:5001'
    
    print("=== 测试新增API接口 ===")
    
    # 测试用的规则ID（假设这些ID存在）
    test_rule_ids = [16, 30, 32]  # 从之前的测试中看到的规则ID
    
    try:
        response = requests.post(f'{base_url}/api/get_compare_ids_by_rule_ids', 
                               json={'rule_ids': test_rule_ids}, timeout=10)
        print(f'测试API - 状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'API响应: {json.dumps(data, ensure_ascii=False, indent=2)}')
            
            if data.get('success'):
                compare_ids = data.get('compare_ids', [])
                print(f'✅ API测试成功！获取到 {len(compare_ids)} 个对照ID')
            else:
                print(f'❌ API返回失败: {data.get("error")}')
        else:
            print(f'❌ HTTP错误: {response.status_code}')
            print(f'响应内容: {response.text}')
            
    except Exception as e:
        print(f'❌ API测试失败: {e}')

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'api':
        test_api_only()
    else:
        test_hospital_filter_api()
