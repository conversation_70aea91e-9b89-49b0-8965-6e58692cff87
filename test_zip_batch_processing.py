#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量处理压缩包功能
"""

import requests
import os
import zipfile
import tempfile
import pandas as pd
from datetime import datetime

# 服务器地址
base_url = 'http://127.0.0.1:5001'

def create_test_excel_file(file_path, rule_name, violation_count=10, violation_amount=1000.0):
    """创建测试用的Excel文件"""
    # 创建规则详情工作表数据
    rule_data = {
        '规则名称': [rule_name],
        '规则类型': ['病案提取'],
        '城市': ['测试城市'],
        '行为认定': ['测试行为'],
        '违规人次': [0],  # 初始为0，后续会被统计
        '违规数量': [0],  # 初始为0，后续会被统计
        '违规金额(元)': [0.0]  # 初始为0，后续会被统计
    }
    
    # 创建违规数据（这是主要的数据表，包含违规数量和违规金额列）
    violation_data = {
        '结算单据号': [f'TEST{i:06d}' for i in range(1, violation_count + 1)],
        '患者姓名': [f'测试患者{i}' for i in range(1, violation_count + 1)],
        '医保项目名称': [f'测试项目{i}' for i in range(1, violation_count + 1)],
        '违规数量': [1] * violation_count,
        '违规金额': [violation_amount / violation_count] * violation_count
    }

    # 创建Excel文件
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        # 规则详情工作表
        pd.DataFrame(rule_data).to_excel(writer, sheet_name='规则详情', index=False)
        # 主数据表（默认工作表，包含违规数量和违规金额）
        pd.DataFrame(violation_data).to_excel(writer, sheet_name='Sheet1', index=False)
    
    print(f"创建测试Excel文件: {file_path}")

def create_test_zip_files():
    """创建测试用的压缩包文件"""
    test_files = []
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix='zip_test_')
    
    # 创建第一个压缩包
    zip1_path = os.path.join(temp_dir, 'test_batch_1.zip')
    with zipfile.ZipFile(zip1_path, 'w') as zf:
        # 创建几个测试Excel文件
        for i in range(1, 4):
            excel_file = os.path.join(temp_dir, f'test_file_{i}.xlsx')
            create_test_excel_file(excel_file, f'测试规则{i}', violation_count=5+i, violation_amount=500.0*(i+1))
            zf.write(excel_file, f'test_file_{i}.xlsx')
            os.remove(excel_file)  # 删除临时文件
    
    test_files.append(zip1_path)
    
    # 创建第二个压缩包
    zip2_path = os.path.join(temp_dir, 'test_batch_2.zip')
    with zipfile.ZipFile(zip2_path, 'w') as zf:
        # 创建几个测试Excel文件
        for i in range(4, 6):
            excel_file = os.path.join(temp_dir, f'test_file_{i}.xlsx')
            create_test_excel_file(excel_file, f'测试规则{i}', violation_count=3+i, violation_amount=800.0*(i-2))
            zf.write(excel_file, f'test_file_{i}.xlsx')
            os.remove(excel_file)  # 删除临时文件
    
    test_files.append(zip2_path)
    
    return test_files, temp_dir

def test_zip_batch_processing():
    """测试批量处理压缩包功能"""
    print("开始测试批量处理压缩包功能...")
    
    try:
        # 创建测试文件
        test_files, temp_dir = create_test_zip_files()
        output_dir = os.path.join(temp_dir, 'output')
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"创建了 {len(test_files)} 个测试压缩包:")
        for file_path in test_files:
            print(f"  - {os.path.basename(file_path)} ({os.path.getsize(file_path)} bytes)")
        
        # 准备请求数据
        files = []
        for file_path in test_files:
            files.append(('zip_files', (os.path.basename(file_path), open(file_path, 'rb'), 'application/zip')))
        
        data = {
            'output_path': output_dir,
            'optimize_performance': 'true'
        }
        
        print(f"\n发送批量处理请求到: {base_url}/api/zip_batch_processing")
        print(f"输出目录: {output_dir}")
        
        # 发送请求
        response = requests.post(f'{base_url}/api/zip_batch_processing', files=files, data=data, timeout=60)
        
        # 关闭文件句柄
        for _, file_tuple in files:
            file_tuple[1].close()
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 批量处理成功!")
            print(f"处理结果:")
            print(f"  - 处理压缩包数量: {result.get('processed_zips', 0)}")
            print(f"  - 总文件数量: {result.get('total_files', 0)}")
            print(f"  - 成功处理: {result.get('success_files', 0)}")
            print(f"  - 处理失败: {result.get('failed_files', 0)}")
            print(f"  - 总违规数量: {result.get('total_violations', 0):,}")
            print(f"  - 总违规金额: {result.get('total_amount', 0):,.2f} 元")
            print(f"  - 处理耗时: {result.get('processing_time', 0)} 秒")
            print(f"  - 结果压缩包: {result.get('result_zip', 'N/A')}")
            
            # 显示各压缩包详情
            if 'zip_results' in result:
                print(f"\n各压缩包处理详情:")
                for i, zip_result in enumerate(result['zip_results'], 1):
                    print(f"  {i}. {zip_result['zip_name']}")
                    print(f"     成功: {zip_result['success_count']}, 失败: {zip_result['failed_count']}")
                    print(f"     违规数量: {zip_result.get('violations', 0):,}")
                    print(f"     违规金额: {zip_result.get('amount', 0):,.2f} 元")
            
            # 测试下载结果
            if result.get('result_zip'):
                test_download_result(result['result_zip'])
            
        else:
            print("❌ 批量处理失败!")
            print(f"错误信息: {response.text}")
        
        # 清理测试文件
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        print(f"\n清理临时目录: {temp_dir}")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {str(e)}")

def test_download_result(result_zip_path):
    """测试下载结果文件"""
    try:
        print(f"\n测试下载结果文件: {result_zip_path}")
        
        download_url = f"{base_url}/api/download_zip_result"
        params = {'file': result_zip_path}
        
        response = requests.get(download_url, params=params, timeout=30)
        
        if response.status_code == 200:
            print("✅ 结果文件下载成功!")
            print(f"文件大小: {len(response.content)} bytes")
            print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        else:
            print("❌ 结果文件下载失败!")
            print(f"状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 下载测试失败: {str(e)}")

def test_api_availability():
    """测试API可用性"""
    print("测试API可用性...")
    
    apis = [
        ('批量处理压缩包', '/api/zip_batch_processing'),
        ('下载结果文件', '/api/download_zip_result'),
    ]
    
    for name, endpoint in apis:
        try:
            response = requests.get(f'{base_url}{endpoint}', timeout=5)
            if response.status_code in [200, 405, 400]:  # 405=方法不允许，400=请求错误，都说明端点存在
                print(f'✅ {name} API 可用')
            else:
                print(f'❌ {name} API 不可用 (状态码: {response.status_code})')
        except Exception as e:
            print(f'❌ {name} API 测试失败: {e}')

if __name__ == '__main__':
    print("=" * 60)
    print("批量处理压缩包功能测试")
    print("=" * 60)
    
    # 1. 测试API可用性
    test_api_availability()
    print()
    
    # 2. 测试批量处理功能
    test_zip_batch_processing()
    
    print("\n测试完成!")
