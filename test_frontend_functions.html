<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则知识库功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">规则知识库功能测试</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>功能测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button class="btn btn-primary" onclick="testNewRule()">测试新增规则</button>
                            <div id="newRuleResult" class="mt-2"></div>
                        </div>
                        
                        <div class="mb-3">
                            <button class="btn btn-success" onclick="testCityTypes()">测试城市列表</button>
                            <div id="cityTypesResult" class="mt-2"></div>
                        </div>
                        
                        <div class="mb-3">
                            <button class="btn btn-warning" onclick="testSequence()">测试序列获取</button>
                            <div id="sequenceResult" class="mt-2"></div>
                        </div>
                        
                        <div class="mb-3">
                            <button class="btn btn-info" onclick="testBatchCopy()">测试批量复制</button>
                            <div id="batchCopyResult" class="mt-2"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p class="text-muted">点击左侧按钮开始测试...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>批量复制功能演示</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">选择规则ID:</label>
                                <input type="text" class="form-control" id="ruleIds" placeholder="例如: 3338,3337" value="3338,3337">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">目标城市:</label>
                                <select class="form-control" id="targetCity">
                                    <option value="">请选择城市</option>
                                    <option value="北京">北京</option>
                                    <option value="上海">上海</option>
                                    <option value="广州">广州</option>
                                    <option value="深圳">深圳</option>
                                    <option value="测试城市">测试城市</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary d-block" onclick="testCustomBatchCopy()">执行批量复制</button>
                            </div>
                        </div>
                        <div id="customBatchResult" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const BASE_URL = 'http://localhost:5001';
        
        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            const alertClass = success ? 'alert-success' : 'alert-danger';
            const icon = success ? 'bi-check-circle' : 'bi-x-circle';
            
            let html = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="bi ${icon} me-2"></i>
                    ${message}
                    ${data ? `<pre class="mt-2 mb-0">${JSON.stringify(data, null, 2)}</pre>` : ''}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            element.innerHTML = html;
        }
        
        function addTestResult(testName, success, message) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = success ? '✅' : '❌';
            const statusClass = success ? 'text-success' : 'text-danger';
            
            const resultHtml = `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between">
                        <span><strong>${testName}</strong></span>
                        <span class="text-muted">${timestamp}</span>
                    </div>
                    <div class="${statusClass}">
                        ${statusIcon} ${message}
                    </div>
                </div>
            `;
            
            if (resultsDiv.innerHTML.includes('点击左侧按钮开始测试')) {
                resultsDiv.innerHTML = resultHtml;
            } else {
                resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
            }
        }
        
        async function testNewRule() {
            try {
                const response = await fetch(`${BASE_URL}/api/rules/next-id`);
                const data = await response.json();
                
                if (response.ok && data.next_id) {
                    showResult('newRuleResult', true, `获取下一个规则ID成功: ${data.next_id}`, data);
                    addTestResult('新增规则ID获取', true, `成功获取ID: ${data.next_id}`);
                } else {
                    showResult('newRuleResult', false, '获取下一个规则ID失败', data);
                    addTestResult('新增规则ID获取', false, '获取失败');
                }
            } catch (error) {
                showResult('newRuleResult', false, `请求失败: ${error.message}`);
                addTestResult('新增规则ID获取', false, `请求失败: ${error.message}`);
            }
        }
        
        async function testCityTypes() {
            try {
                const response = await fetch(`${BASE_URL}/api/city_types`);
                const data = await response.json();
                
                if (response.ok && data.success && data.types) {
                    showResult('cityTypesResult', true, `获取城市列表成功，共 ${data.types.length} 个城市`, data.types.slice(0, 5));
                    addTestResult('城市列表获取', true, `成功获取 ${data.types.length} 个城市`);
                } else {
                    showResult('cityTypesResult', false, '获取城市列表失败', data);
                    addTestResult('城市列表获取', false, '获取失败');
                }
            } catch (error) {
                showResult('cityTypesResult', false, `请求失败: ${error.message}`);
                addTestResult('城市列表获取', false, `请求失败: ${error.message}`);
            }
        }
        
        async function testSequence() {
            try {
                const response = await fetch(`${BASE_URL}/api/sequence/规则医保编码对照_SEQ`);
                const data = await response.json();
                
                if (response.ok && data.sequence) {
                    showResult('sequenceResult', true, `获取序列号成功: ${data.sequence}`, data);
                    addTestResult('序列号获取', true, `成功获取序列号: ${data.sequence}`);
                } else {
                    showResult('sequenceResult', false, '获取序列号失败', data);
                    addTestResult('序列号获取', false, `获取失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                showResult('sequenceResult', false, `请求失败: ${error.message}`);
                addTestResult('序列号获取', false, `请求失败: ${error.message}`);
            }
        }
        
        async function testBatchCopy() {
            const testData = {
                rule_ids: [3338],
                target_city: '测试城市'
            };
            
            try {
                const response = await fetch(`${BASE_URL}/api/rules/batch-copy`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('batchCopyResult', true, `批量复制成功: ${data.message}`, data);
                    addTestResult('批量复制测试', true, data.message);
                } else {
                    showResult('batchCopyResult', false, `批量复制失败: ${data.error || '未知错误'}`, data);
                    addTestResult('批量复制测试', false, data.error || '未知错误');
                }
            } catch (error) {
                showResult('batchCopyResult', false, `请求失败: ${error.message}`);
                addTestResult('批量复制测试', false, `请求失败: ${error.message}`);
            }
        }
        
        async function testCustomBatchCopy() {
            const ruleIdsStr = document.getElementById('ruleIds').value;
            const targetCity = document.getElementById('targetCity').value;
            
            if (!ruleIdsStr || !targetCity) {
                showResult('customBatchResult', false, '请填写规则ID和选择目标城市');
                return;
            }
            
            const ruleIds = ruleIdsStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            
            if (ruleIds.length === 0) {
                showResult('customBatchResult', false, '请输入有效的规则ID');
                return;
            }
            
            const testData = {
                rule_ids: ruleIds,
                target_city: targetCity
            };
            
            try {
                const response = await fetch(`${BASE_URL}/api/rules/batch-copy`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('customBatchResult', true, `批量复制成功: ${data.message}`, data);
                    addTestResult('自定义批量复制', true, data.message);
                } else {
                    showResult('customBatchResult', false, `批量复制失败: ${data.error || '未知错误'}`, data);
                    addTestResult('自定义批量复制', false, data.error || '未知错误');
                }
            } catch (error) {
                showResult('customBatchResult', false, `请求失败: ${error.message}`);
                addTestResult('自定义批量复制', false, `请求失败: ${error.message}`);
            }
        }
    </script>
</body>
</html>
