"""
深度SQL解析器第一步测试
测试基础架构和简单SQL解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sql_deep_parser import DeepSQLParser, RuleType, FieldType, OperatorType


def test_basic_parsing():
    """测试基础SQL解析功能"""
    print("=== 测试基础SQL解析功能 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    # 测试简单的年龄限制SQL
    sql = """
    SELECT * FROM 医保住院结算明细 B
    JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
    WHERE A.患者年龄 > 50
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        print(f"数据源数量: {len(rule_ir.data_sources)}")
        print(f"条件数量: {len(rule_ir.conditions)}")
        
        # 打印数据源信息
        print("\n数据源:")
        for i, ds in enumerate(rule_ir.data_sources):
            print(f"  {i+1}. 表名: {ds.table_name}, 别名: {ds.alias}, JOIN类型: {ds.join_type}")
        
        # 打印条件信息
        print("\n条件:")
        for i, cond in enumerate(rule_ir.conditions):
            print(f"  {i+1}. {cond.field.field_name} ({cond.field.field_type.value}) {cond.operator.value} {cond.value}")
        
        # 打印JSON格式
        print(f"\nJSON格式:\n{rule_ir.to_json()}")
        
    else:
        print(f"解析失败: {result.error_message}")
    
    return result.success


def test_multiple_conditions():
    """测试多条件SQL解析"""
    print("\n=== 测试多条件SQL解析 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    SELECT * FROM 医保住院结算明细 B
    JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
    WHERE A.患者年龄 > 50 AND A.患者年龄 < 75 AND A.患者性别 = '男'
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        print(f"条件数量: {len(rule_ir.conditions)}")
        
        print("\n条件详情:")
        for i, cond in enumerate(rule_ir.conditions):
            print(f"  {i+1}. {cond.field.table_alias}.{cond.field.field_name} {cond.operator.value} {cond.value}")
            print(f"      字段类型: {cond.field.field_type.value}")
            print(f"      逻辑类型: {cond.logic_type.value}")
    else:
        print(f"解析失败: {result.error_message}")
    
    return result.success


def test_medical_item_condition():
    """测试医保项目条件解析"""
    print("\n=== 测试医保项目条件解析 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    SELECT * FROM 医保住院结算明细 B
    WHERE B.医保项目名称 = '血液灌流'
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        
        print("\n条件详情:")
        for cond in rule_ir.conditions:
            print(f"  字段: {cond.field.field_name}")
            print(f"  字段类型: {cond.field.field_type.value}")
            print(f"  操作符: {cond.operator.value}")
            print(f"  值: {cond.value}")
    else:
        print(f"解析失败: {result.error_message}")
    
    return result.success


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    # 测试无效SQL
    invalid_sql = "SELECT * FROM WHERE"
    
    result = parser.parse(invalid_sql)
    
    print(f"解析成功: {result.success}")
    if not result.success:
        print(f"错误信息: {result.error_message}")
        print("✅ 错误处理正常")
    else:
        print("❌ 应该解析失败但却成功了")
    
    return not result.success


def main():
    """主测试函数"""
    print("开始深度SQL解析器第一步测试...\n")
    
    tests = [
        ("基础SQL解析", test_basic_parsing),
        ("多条件SQL解析", test_multiple_conditions),
        ("医保项目条件解析", test_medical_item_condition),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 第一步测试全部通过！可以继续下一步开发。")
    else:
        print("⚠️  存在失败的测试，需要修复后再继续。")


if __name__ == "__main__":
    main()
