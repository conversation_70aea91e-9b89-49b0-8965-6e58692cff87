#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试移除去重逻辑后的效果
"""

import requests
import json
import sys

def test_no_deduplication():
    """测试移除去重逻辑的效果"""
    base_url = 'http://localhost:5001'
    
    print("=== 测试移除去重逻辑 ===")
    
    try:
        # 1. 获取医院列表
        print("1. 获取医院列表...")
        response = requests.get(f'{base_url}/api/hospitals', timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 获取医院列表失败: HTTP {response.status_code}")
            return False
            
        hospitals_data = response.json()
        if not hospitals_data.get('success') or not hospitals_data.get('hospitals'):
            print(f"❌ 医院列表API返回失败")
            return False
            
        hospitals = hospitals_data['hospitals']
        print(f"✅ 成功获取 {len(hospitals)} 家医院")
        
        # 选择第一家医院进行测试
        test_hospital = hospitals[0]
        hospital_id = test_hospital['医院ID']
        hospital_name = test_hospital['医院名称']
        print(f"📋 测试医院: {hospital_name} (ID: {hospital_id})")
        
        # 2. 测试获取已采用规则（检查是否有重复记录）
        print("\n2. 测试获取已采用规则...")
        response = requests.get(f'{base_url}/api/hospital-rules/adopted/{hospital_id}', timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 获取已采用规则失败: HTTP {response.status_code}")
            return False
            
        adopted_data = response.json()
        if not adopted_data.get('success'):
            print(f"❌ 已采用规则API返回失败")
            return False
            
        adopted_rules = adopted_data.get('rules', [])
        print(f"✅ 获取到 {len(adopted_rules)} 条已采用规则")
        
        # 分析是否有同规则ID的多条记录
        rule_id_count = {}
        for rule in adopted_rules:
            rule_id = rule.get('规则ID')
            if rule_id in rule_id_count:
                rule_id_count[rule_id] += 1
            else:
                rule_id_count[rule_id] = 1
        
        multi_record_rules = {rule_id: count for rule_id, count in rule_id_count.items() if count > 1}
        
        print(f"   独立规则ID数: {len(rule_id_count)}")
        print(f"   有多条记录的规则ID数: {len(multi_record_rules)}")
        
        if multi_record_rules:
            print("   多记录规则ID详情:")
            for rule_id, count in list(multi_record_rules.items())[:3]:
                print(f"     规则ID {rule_id}: {count} 条记录")
                # 显示这些记录的详细信息
                same_id_rules = [r for r in adopted_rules if r.get('规则ID') == rule_id]
                for i, rule in enumerate(same_id_rules):
                    print(f"       记录 {i+1}: 适用ID={rule.get('适用ID')}, 对照ID={rule.get('对照ID')}, 城市={rule.get('城市')}")
        
        # 3. 快速测试生成推荐（只生成少量）
        print("\n3. 测试生成推荐（限制数量）...")
        response = requests.post(f'{base_url}/api/hospital-rules/generate', 
                               json={'hospital_id': hospital_id, 'limit': 50}, timeout=30)
        
        if response.status_code == 200:
            recommendations_data = response.json()
            if recommendations_data.get('success'):
                recommendations = recommendations_data.get('recommendations', [])
                print(f"✅ 成功生成 {len(recommendations)} 条推荐")
                
                # 检查推荐中是否有合并标记
                merged_count = sum(1 for rec in recommendations if rec.get('合并规则数量', 1) > 1)
                print(f"   合并记录数: {merged_count}")
                
                if merged_count == 0:
                    print("✅ 确认推荐记录没有合并")
                else:
                    print("⚠️  仍有合并记录")
            else:
                print(f"⚠️  生成推荐失败: {recommendations_data.get('error')}")
        else:
            print(f"⚠️  生成推荐请求失败: HTTP {response.status_code}")
        
        # 4. 总结
        print("\n=== 测试结果总结 ===")
        print(f"医院: {hospital_name}")
        print(f"已采用规则总数: {len(adopted_rules)}")
        print(f"独立规则ID数: {len(rule_id_count)}")
        print(f"多记录规则ID数: {len(multi_record_rules)}")
        
        if len(multi_record_rules) > 0:
            print("✅ 成功移除去重逻辑！同规则ID的多条记录都能显示")
            return True
        else:
            print("ℹ️  当前数据中没有同规则ID的多条记录，无法验证去重逻辑是否移除")
            print("   但API调用正常，功能应该已经修复")
            return True
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == '__main__':
    print("开始测试移除去重逻辑...\n")
    
    success = test_no_deduplication()
    
    if success:
        print("\n🎉 测试通过！去重逻辑已成功移除。")
        sys.exit(0)
    else:
        print("\n❌ 测试失败。")
        sys.exit(1)
