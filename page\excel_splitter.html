<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel文件拆分</title>
    <style>
        :root {
            --primary: #0078D4;
            --primary-hover: #106EBE;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --border-color: rgba(0, 0, 0, 0.1);
            --radius: 12px;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --transition: all 0.2s ease;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        nav {
            margin-bottom: 2rem;
        }

        nav a {
            color: var(--primary);
            text-decoration: none;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: var(--transition);
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav a:hover {
            background-color: rgba(0, 120, 212, 0.1);
        }

        h1 {
            font-size: 1.875rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--text-primary);
            text-align: center;
        }

        .card {
            background: var(--card-bg);
            border-radius: var(--radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
        }

        .file-input {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            background-color: var(--card-bg);
            margin-bottom: 1rem;
        }

        .file-input span {
            color: var(--text-secondary);
            flex-grow: 1;
        }

        .text-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-family: inherit;
            transition: var(--transition);
            margin-bottom: 1rem;
        }

        .text-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
        }

        .btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            width: 100%;
            font-size: 1rem;
        }

        .btn:hover {
            background: var(--primary-hover);
        }

        .btn-secondary {
            background-color: #f0f0f0;
            color: var(--text-primary);
        }

        .btn-secondary:hover {
            background-color: #e0e0e0;
        }

        .message {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: var(--radius);
            background-color: #f0fdf4;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <nav>
            <a href="{{ url_for('index') }}">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M9.78 12.78a.75.75 0 01-1.06 0L4.47 8.53a.75.75 0 010-1.06l4.25-4.25a.75.75 0 011.06 1.06L6.06 8l3.72 3.72a.75.75 0 010 1.06z"/>
                </svg>
                返回主页
            </a>
        </nav>
        <h1>Excel文件拆分</h1>
        <div class="card">
            <h2>选择并拆分Excel文件</h2>
            <form action="{{ url_for('split_excel') }}" method="post" enctype="multipart/form-data">
                <div class="file-input">
                    <span id="file-name">未选择任何文件</span>
                    <button type="button" class="btn btn-secondary" onclick="document.getElementById('file-upload').click()">
                        选择文件
                    </button>
                    <input id="file-upload" type="file" name="file" accept=".xlsx, .xls" required style="display: none;">
                </div>
                <input type="text" name="group_column" placeholder="输入用于分组的列名" required class="text-input">
                <button type="submit" class="btn">拆分文件</button>
            </form>
        </div>
        {% if message %}
        <div class="message">
            {{ message }}
        </div>
        {% endif %}
    </div>
    <script>
        document.getElementById('file-upload').addEventListener('change', function() {
            var fileName = this.files[0].name;
            document.getElementById('file-name').textContent = fileName;
        });
    </script>
</body>
</html>
