"""
调试CTE中的INTERSECT检测问题
"""

import sqlglot
from sqlglot import expressions as exp

sql = """
WITH tab1 AS (
    SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
    FROM 医保住院结算明细 WHERE 医保项目名称 = 'A'
    INTERSECT
    SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
    FROM 医保住院结算明细 WHERE 医保项目名称 = 'B'
)
SELECT * FROM tab1
"""

# 解析SQL
ast = sqlglot.parse_one(sql)
print("AST结构:")
print(ast)
print(f"AST类型: {type(ast)}")

# 查找WITH节点
with_node = ast.find(exp.With)
if with_node:
    print(f"\nWITH节点: {with_node}")
    print(f"CTE数量: {len(with_node.expressions)}")
    
    for i, cte in enumerate(with_node.expressions):
        print(f"\nCTE {i+1}:")
        print(f"  类型: {type(cte)}")
        print(f"  别名: {cte.alias}")
        print(f"  查询: {cte.this}")
        print(f"  查询类型: {type(cte.this)}")
        
        # 检查CTE查询是否为INTERSECT
        if isinstance(cte.this, exp.Intersect):
            print("  ✅ 这是一个INTERSECT查询")
        else:
            print("  ❌ 这不是INTERSECT查询")
            
        # 查找所有INTERSECT节点
        intersects = list(cte.this.find_all(exp.Intersect))
        print(f"  找到的INTERSECT节点: {len(intersects)}")
        
        for j, intersect in enumerate(intersects):
            print(f"    INTERSECT {j+1}: {intersect}")

# 查找所有INTERSECT节点
all_intersects = list(ast.find_all(exp.Intersect))
print(f"\n整个AST中的INTERSECT节点数量: {len(all_intersects)}")

for i, intersect in enumerate(all_intersects):
    print(f"INTERSECT {i+1}: {intersect}")
    print(f"  左侧: {intersect.left}")
    print(f"  右侧: {intersect.right}")
