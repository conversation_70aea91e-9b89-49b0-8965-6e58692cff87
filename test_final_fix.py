#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复效果：已采用规则仍然显示，但不能重复选择
"""

import requests
import json
import sys
from collections import defaultdict

def test_final_fix():
    """测试最终修复效果"""
    base_url = 'http://localhost:5001'
    
    print("=== 测试最终修复效果 ===")
    print("预期效果：")
    print("1. 已采用规则仍然显示在推荐列表中")
    print("2. 但已采用规则ID的其他记录不能被勾选")
    print("3. 跨城市的已采用规则ID也不能被重复选择")
    print()
    
    try:
        # 1. 测试全局已采用规则ID API
        print("1. 测试全局已采用规则ID API...")
        response = requests.get(f'{base_url}/api/global-adopted-rule-ids', timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 获取全局已采用规则ID失败: HTTP {response.status_code}")
            return False
            
        global_data = response.json()
        if not global_data.get('success'):
            print(f"❌ 全局已采用规则ID API返回失败")
            return False
            
        global_adopted_rule_ids = set(global_data.get('rule_ids', []))
        print(f"✅ 成功获取全局已采用规则ID: {len(global_adopted_rule_ids)} 个")
        
        if len(global_adopted_rule_ids) == 0:
            print("⚠️  没有全局已采用规则，无法测试禁用功能")
            return True
        
        # 显示一些已采用规则ID的示例
        print(f"   已采用规则ID示例: {list(global_adopted_rule_ids)[:5]}")
        
        # 2. 获取医院列表
        print("\n2. 获取医院列表...")
        response = requests.get(f'{base_url}/api/hospitals', timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 获取医院列表失败: HTTP {response.status_code}")
            return False
            
        hospitals_data = response.json()
        if not hospitals_data.get('success') or not hospitals_data.get('hospitals'):
            print(f"❌ 医院列表API返回失败")
            return False
            
        hospitals = hospitals_data['hospitals']
        print(f"✅ 成功获取 {len(hospitals)} 家医院")
        
        # 3. 选择一家医院测试推荐生成
        test_hospital = hospitals[0]
        hospital_id = test_hospital['医院ID']
        hospital_name = test_hospital['医院名称']
        hospital_city = test_hospital.get('所在城市', '未知')
        
        print(f"\n3. 测试医院推荐生成: {hospital_name} ({hospital_city})")
        
        response = requests.post(f'{base_url}/api/hospital-rules/generate', 
                               json={'hospital_id': hospital_id, 'limit': 200}, timeout=60)
        
        if response.status_code != 200:
            print(f"❌ 生成推荐失败: HTTP {response.status_code}")
            return False
            
        recommendations_data = response.json()
        if not recommendations_data.get('success'):
            print(f"❌ 推荐API返回失败: {recommendations_data.get('error', '无数据')}")
            return False
            
        recommendations = recommendations_data.get('recommendations', [])
        print(f"✅ 成功生成 {len(recommendations)} 条推荐")
        
        # 4. 分析推荐结果
        print("\n4. 分析推荐结果...")
        
        # 统计推荐中的规则ID
        rec_rule_ids = set()
        adopted_in_recommendations = []
        conflicting_recommendations = []
        
        for rec in recommendations:
            rule_id = rec.get('规则ID')
            status = rec.get('状态')
            rec_rule_ids.add(rule_id)
            
            if status == '已采用':
                adopted_in_recommendations.append(rec)
            
            # 检查是否有冲突（推荐状态但规则ID已被全局采用）
            if status == '推荐' and rule_id in global_adopted_rule_ids:
                conflicting_recommendations.append(rec)
        
        print(f"   推荐中的规则ID总数: {len(rec_rule_ids)}")
        print(f"   推荐中的已采用记录数: {len(adopted_in_recommendations)}")
        print(f"   推荐中与全局已采用冲突的记录数: {len(conflicting_recommendations)}")
        
        # 5. 检查是否有已采用规则显示
        if len(adopted_in_recommendations) > 0:
            print(f"\n✅ 已采用规则正常显示: {len(adopted_in_recommendations)} 条")
            print("   已采用规则示例 (前3条):")
            for i, rec in enumerate(adopted_in_recommendations[:3]):
                print(f"   记录 {i+1}:")
                print(f"     规则ID: {rec.get('规则ID')}")
                print(f"     规则名称: {rec.get('规则名称')}")
                print(f"     状态: {rec.get('状态')}")
                print(f"     城市: {rec.get('城市')}")
                print()
        else:
            print("⚠️  推荐中没有已采用规则显示")
        
        # 6. 检查冲突记录
        if len(conflicting_recommendations) > 0:
            print(f"❌ 发现问题：有 {len(conflicting_recommendations)} 条推荐记录的规则ID已被全局采用")
            print("   冲突记录详情 (前3条):")
            for i, rec in enumerate(conflicting_recommendations[:3]):
                print(f"   记录 {i+1}:")
                print(f"     规则ID: {rec.get('规则ID')} (已被全局采用)")
                print(f"     规则名称: {rec.get('规则名称')}")
                print(f"     状态: {rec.get('状态')} (应该被禁用)")
                print(f"     城市: {rec.get('城市')}")
                print()
        else:
            print("✅ 没有发现冲突记录，推荐状态的记录都是可选择的")
        
        # 7. 测试已采用规则查看功能
        print(f"\n7. 测试已采用规则查看功能...")
        response = requests.get(f'{base_url}/api/hospital-rules/adopted/{hospital_id}', timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 获取已采用规则失败: HTTP {response.status_code}")
            return False
            
        adopted_data = response.json()
        if not adopted_data.get('success'):
            print(f"❌ 已采用规则API返回失败")
            return False
            
        adopted_rules = adopted_data.get('rules', [])
        print(f"✅ 成功获取已采用规则: {len(adopted_rules)} 条")
        
        if len(adopted_rules) > 0:
            print("   已采用规则示例 (前3条):")
            for i, rule in enumerate(adopted_rules[:3]):
                print(f"   规则 {i+1}:")
                print(f"     规则ID: {rule.get('规则ID')}")
                print(f"     规则名称: {rule.get('规则名称')}")
                print(f"     状态: {rule.get('状态')}")
                print(f"     城市: {rule.get('城市')}")
                print()
        
        # 8. 测试结果总结
        print("=== 测试结果总结 ===")
        print(f"全局已采用规则ID数: {len(global_adopted_rule_ids)}")
        print(f"推荐记录总数: {len(recommendations)}")
        print(f"推荐中已采用记录数: {len(adopted_in_recommendations)}")
        print(f"推荐中冲突记录数: {len(conflicting_recommendations)}")
        print(f"医院已采用规则数: {len(adopted_rules)}")
        
        # 判断测试结果
        success_criteria = [
            (len(global_adopted_rule_ids) > 0, "有全局已采用规则ID"),
            (len(recommendations) > 0, "能够生成推荐"),
            (len(conflicting_recommendations) == 0, "没有冲突的推荐记录"),
            (len(adopted_rules) >= 0, "能够查看已采用规则")  # 可能为0
        ]
        
        passed_count = sum(1 for passed, _ in success_criteria if passed)
        total_count = len(success_criteria)
        
        print(f"\n测试通过率: {passed_count}/{total_count}")
        
        for passed, description in success_criteria:
            status = "✅" if passed else "❌"
            print(f"{status} {description}")
        
        if passed_count == total_count:
            print("\n🎉 最终修复测试成功！")
            print("   - 已采用规则正常显示")
            print("   - 没有冲突的推荐记录")
            print("   - 前端禁用逻辑应该能正确工作")
            return True
        else:
            print(f"\n❌ 测试未完全通过，{total_count - passed_count} 项失败")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == '__main__':
    print("开始测试最终修复效果...\n")
    
    success = test_final_fix()
    
    if success:
        print("\n🎉 测试通过！最终修复方案正常工作。")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，需要进一步检查。")
        sys.exit(1)
