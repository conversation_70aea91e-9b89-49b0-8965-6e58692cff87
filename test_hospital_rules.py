#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院规则推荐功能测试脚本
"""

import requests
import json

# 测试医院规则推荐相关API
base_url = 'http://localhost:5001'

print('=== 医院规则推荐功能测试 ===')

# 1. 测试获取医院列表
try:
    response = requests.get(f'{base_url}/api/hospitals', timeout=10)
    print(f'获取医院列表 - 状态码: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        hospitals = data.get('hospitals', [])
        print(f'医院数量: {len(hospitals)}')
        if hospitals:
            hospital_id = hospitals[0]['医院ID']
            hospital_name = hospitals[0]['医院名称']
            print(f'测试医院: {hospital_name} (ID: {hospital_id})')
            
            # 2. 测试生成规则推荐
            try:
                response = requests.post(f'{base_url}/api/hospital-rules/generate', 
                                       json={'hospital_id': hospital_id}, timeout=30)
                print(f'生成规则推荐 - 状态码: {response.status_code}')
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        recommendations = data.get('recommendations', [])
                        print(f'推荐规则数量: {len(recommendations)}')
                        print(f'总数量: {data.get("total_count", 0)}')
                        if recommendations:
                            print(f'第一条推荐: {recommendations[0].get("规则名称", "未知")}')
                    else:
                        print(f'推荐失败: {data.get("error")}')
                else:
                    print(f'推荐失败: {response.text}')
            except Exception as e:
                print(f'生成推荐失败: {e}')
            
            # 3. 测试获取已采用规则
            try:
                response = requests.get(f'{base_url}/api/hospital-rules/adopted/{hospital_id}', timeout=10)
                print(f'获取已采用规则 - 状态码: {response.status_code}')
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        rules = data.get('rules', [])
                        print(f'已采用规则数量: {len(rules)}')
                        if rules:
                            print(f'第一条已采用规则: {rules[0].get("规则名称", "未知")}')
                    else:
                        print(f'获取失败: {data.get("error")}')
                else:
                    print(f'获取失败: {response.text}')
            except Exception as e:
                print(f'获取已采用规则失败: {e}')
                
            # 4. 测试批量状态更新（模拟）
            try:
                test_data = {
                    'rule_ids': [1, 2],  # 使用测试ID
                    'status': '已采用'
                }
                response = requests.put(f'{base_url}/api/hospital-rules/batch-status', 
                                      json=test_data, timeout=10)
                print(f'批量状态更新 - 状态码: {response.status_code}')
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        print(f'更新成功: {data.get("message")}')
                    else:
                        print(f'更新失败: {data.get("error")}')
                else:
                    print(f'更新失败: {response.text}')
            except Exception as e:
                print(f'批量状态更新失败: {e}')
    else:
        print(f'获取医院列表失败: {response.text}')
except Exception as e:
    print(f'获取医院列表失败: {e}')

print('\n=== 测试完成 ===')
