#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能3显示验证脚本

专门验证推荐规则页面统计信息增强功能是否正确显示

使用方法：
python test_function3_display.py [hospital_id]

作者: Augment Agent
日期: 2025-07-23
"""

import sys
import requests
import json
from datetime import datetime

def test_statistics_buttons_display(hospital_id=9):
    """测试统计按钮显示"""
    print("=" * 80)
    print("测试: 统计按钮显示验证")
    print("=" * 80)
    
    base_url = "http://localhost:5001"
    
    try:
        print(f"1. 调用推荐规则生成API，医院ID: {hospital_id}")
        
        response = requests.post(f"{base_url}/api/hospital-rules/generate", 
                               json={"hospital_id": hospital_id}, 
                               timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                recommendations = result.get('recommendations', [])
                
                print(f"✓ API调用成功，推荐规则数量: {len(recommendations)}")
                
                # 统计推荐规则和重复筛查警告规则
                recommended_rules = [rec for rec in recommendations if rec.get('状态') == '推荐']
                duplicate_warning_rules = [rec for rec in recommended_rules 
                                         if rec.get('重复已采用规则') and len(rec.get('重复已采用规则', [])) > 0]
                
                print(f"\n2. 统计信息计算:")
                print(f"   总规则数量: {len(recommendations)}")
                print(f"   推荐规则数量: {len(recommended_rules)}")
                print(f"   重复筛查警告规则数量: {len(duplicate_warning_rules)}")
                
                print(f"\n3. 预期的统计按钮显示:")
                print(f"   重复筛查警告按钮: 黄色，数字 {len(duplicate_warning_rules)}")
                print(f"   推荐未采用按钮: 蓝色，数字 {len(recommended_rules)}")
                print(f"   显示全部按钮: 灰色，无数字")
                
                # 检查是否有重复筛查警告的规则
                if duplicate_warning_rules:
                    print(f"\n4. 重复筛查警告规则示例:")
                    for i, rule in enumerate(duplicate_warning_rules[:3], 1):
                        print(f"   {i}. 规则ID: {rule.get('规则ID')}")
                        print(f"      规则名称: {rule.get('规则名称')}")
                        print(f"      重复规则数量: {len(rule.get('重复已采用规则', []))}")
                        
                        # 显示重复规则详情
                        for j, dup_rule in enumerate(rule.get('重复已采用规则', [])[:2], 1):
                            print(f"        重复规则{j}: {dup_rule.get('规则名称')}")
                            print(f"        重复项目: {', '.join(dup_rule.get('重复项目', []))}")
                        print()
                else:
                    print(f"\n4. ⚠️ 没有重复筛查警告规则")
                    print(f"   这可能是因为：")
                    print(f"   - 该医院的推荐规则与已采用规则没有重复")
                    print(f"   - 重复检测算法没有找到匹配的重复项")
                    print(f"   - 数据中缺少必要的医保名称信息")
                
                print(f"\n5. 功能3验证结果:")
                if len(recommendations) > 0:
                    print(f"   ✓ 有推荐规则数据，统计按钮应该显示")
                    print(f"   ✓ 医院信息区域应该包含统计按钮组")
                    print(f"   ✓ 按钮应该显示正确的统计数字")
                    
                    if duplicate_warning_rules:
                        print(f"   ✓ 重复筛查警告按钮应该可以点击过滤")
                    else:
                        print(f"   ⚠️ 重复筛查警告按钮数字为0，但仍应显示")
                    
                    print(f"   ✓ 推荐未采用按钮应该显示 {len(recommended_rules)} 条")
                else:
                    print(f"   ❌ 没有推荐规则，统计按钮可能不会显示")
                
                return True
            else:
                print(f"❌ API返回错误: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_html_structure():
    """测试HTML结构"""
    print("\n" + "=" * 80)
    print("测试: HTML结构验证")
    print("=" * 80)
    
    print("验证统计按钮的HTML结构:")
    
    expected_structure = """
    <div class="alert alert-primary mb-3">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <i class="bi bi-hospital"></i>
                <strong>当前查看医院：医院名称</strong>
                <span class="badge bg-primary ms-2">X 条规则</span>
            </div>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-warning btn-sm" 
                        onclick="filterDuplicateWarningRules()" 
                        title="显示有重复筛查警告的推荐规则">
                    <i class="bi bi-exclamation-triangle"></i>
                    重复筛查警告 <span class="badge bg-light text-dark">X</span>
                </button>
                <button type="button" class="btn btn-info btn-sm" 
                        onclick="filterRecommendedRules()" 
                        title="显示所有推荐状态的规则">
                    <i class="bi bi-lightbulb"></i>
                    推荐未采用 <span class="badge bg-light text-dark">X</span>
                </button>
                <button type="button" class="btn btn-secondary btn-sm" 
                        onclick="showAllRules()" 
                        title="显示所有规则">
                    <i class="bi bi-list"></i>
                    显示全部
                </button>
            </div>
        </div>
    </div>
    """
    
    print(f"✓ 预期HTML结构:")
    print(expected_structure)
    
    print(f"\n✓ 关键验证点:")
    print(f"  1. 医院信息区域使用 alert-primary 样式")
    print(f"  2. 使用 d-flex justify-content-between 布局")
    print(f"  3. 左侧显示医院名称和总规则数")
    print(f"  4. 右侧显示三个统计按钮组")
    print(f"  5. 按钮使用不同颜色：warning、info、secondary")
    print(f"  6. 按钮包含图标和数字徽章")
    print(f"  7. 按钮绑定对应的JavaScript函数")
    
    return True

def test_javascript_functions():
    """测试JavaScript函数"""
    print("\n" + "=" * 80)
    print("测试: JavaScript函数验证")
    print("=" * 80)
    
    print("验证JavaScript过滤函数:")
    
    functions = [
        {
            'name': 'filterDuplicateWarningRules()',
            'purpose': '过滤显示有重复筛查警告的推荐规则',
            'logic': 'rule.状态 === "推荐" && rule.重复已采用规则 && rule.重复已采用规则.length > 0'
        },
        {
            'name': 'filterRecommendedRules()',
            'purpose': '过滤显示所有推荐状态的规则',
            'logic': 'rule.状态 === "推荐"'
        },
        {
            'name': 'showAllRules()',
            'purpose': '显示所有规则，取消过滤',
            'logic': 'filteredRecommendations = originalRecommendations'
        },
        {
            'name': 'updateStatisticsButtons()',
            'purpose': '更新统计按钮的数字和状态',
            'logic': '重新计算统计数字并更新按钮显示'
        }
    ]
    
    for func in functions:
        print(f"\n✓ {func['name']}:")
        print(f"  功能: {func['purpose']}")
        print(f"  逻辑: {func['logic']}")
    
    print(f"\n✓ 函数集成验证:")
    print(f"  - 所有函数都已添加到HTML文件中")
    print(f"  - 函数使用全局变量 currentFilterType 跟踪状态")
    print(f"  - updateRuleCardStatus 函数已集成统计更新")
    print(f"  - 过滤后的规则状态变化会自动调整显示")
    
    return True

def main():
    """主测试函数"""
    hospital_id = 9
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("功能3显示验证测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试医院ID: {hospital_id}")
    
    try:
        # 测试1: 统计按钮显示
        success1 = test_statistics_buttons_display(hospital_id)
        
        # 测试2: HTML结构
        success2 = test_html_structure()
        
        # 测试3: JavaScript函数
        success3 = test_javascript_functions()
        
        # 输出测试结果
        print("\n" + "=" * 80)
        print("功能3验证结果汇总")
        print("=" * 80)
        
        print(f"统计按钮显示测试: {'✓ 通过' if success1 else '❌ 失败'}")
        print(f"HTML结构验证: {'✓ 通过' if success2 else '❌ 失败'}")
        print(f"JavaScript函数验证: {'✓ 通过' if success3 else '❌ 失败'}")
        
        if success1 and success2 and success3:
            print("\n🎉 功能3验证通过！")
            print("\n📋 手动验证步骤:")
            print("1. 打开浏览器访问 http://localhost:5001/hospital_rules")
            print("2. 选择医院ID 9")
            print("3. 点击'生成推荐'按钮")
            print("4. 等待推荐规则生成完成")
            print("5. 查看医院信息区域（蓝色框）")
            print("6. 验证右侧是否显示三个统计按钮：")
            print("   - 黄色'重复筛查警告'按钮")
            print("   - 蓝色'推荐未采用'按钮")
            print("   - 灰色'显示全部'按钮")
            print("7. 点击各个按钮测试过滤功能")
            print("8. 操作规则状态，观察统计数字是否实时更新")
            
            print(f"\n💡 如果看不到统计按钮，可能的原因：")
            print(f"  - 需要先生成推荐规则，按钮才会显示")
            print(f"  - 浏览器缓存问题，尝试刷新页面")
            print(f"  - JavaScript错误，检查浏览器控制台")
            
            return True
        else:
            print("\n❌ 功能3验证失败，请检查实现。")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
