"""
测试复杂的ClickHouse SQL解析
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_complex_clickhouse_sql():
    """测试复杂的ClickHouse SQL"""
    print("=== 测试复杂ClickHouse SQL ===")
    
    sql_content = """
    -- 规则名称: 血小板减少症用药限制
    -- 城市: 测试城市
    -- 行为认定: 诊断限制
    SELECT
      A.`病案号` AS `病案号`,
      A.`结算单据号` AS `结算单据号`,
      A.`医疗机构编码` AS `医疗机构编码`,
      A.`医疗机构名称` AS `医疗机构名称`,
      A.`结算日期` AS `结算日期`,
      A.`住院号` AS `住院号`,
      A.`个人编码` AS `个人编码`,
      A.`患者社会保障号码` AS `患者社会保障号码`,
      A.`身份证号` AS `身份证号`,
      A.`险种类型` AS `险种类型`,
      A.`入院科室` AS `入院科室`,
      A.`出院科室` AS `出院科室`,
      A.`主诊医师姓名` AS `主诊医师姓名`,
      A.`患者姓名` AS `患者姓名`,
      A.`患者性别` AS `患者性别`,
      A.`患者出生日期` AS `患者出生日期`,
      A.`患者年龄` AS `患者年龄`,
      A.`异地标志` AS `异地标志`,
      A.`入院日期` AS `入院日期`,
      A.`出院日期` AS `出院日期`,
      toDate(A.`出院日期`) - toDate(A.`入院日期`) + 1 AS `住院天数`,
      A.`医疗总费用` AS `医疗总费用`,
      A.`基本统筹支付` AS `基本统筹支付`,
      A.`个人自付` AS `个人自付`,
      A.`个人自费` AS `个人自费`,
      A.`符合基本医疗保险的费用` AS `符合基本医疗保险的费用`,
      A.`入院诊断编码` AS `入院诊断编码`,
      A.`入院诊断名称` AS `入院诊断名称`,
      A.`出院诊断编码` AS `出院诊断编码`,
      A.`出院诊断名称` AS `出院诊断名称`,
      A.`主诊断及操作名称` as `主诊断及操作名称`,
      A.`主手术及操作编码` AS `主手术及操作编码`,
      A.`主手术及操作名称` AS `主手术及操作名称`,
      A.`其他手术及操作编码` AS `其他手术及操作编码`,
      A.`其他手术及操作名称` AS `其他手术及操作名称`,
      C.`医保项目编码` AS `医保项目编码`,
      C.`医保项目名称` AS `医保项目名称`,
      C.`总数量` AS `总数量`,
      C.`总金额` AS `总金额`
    FROM
      (SELECT
      B.`结算单据号`  AS `结算单据号` ,
      B.`医保项目编码` AS `医保项目编码`,
      B.`医保项目名称` AS `医保项目名称`,
      sum(B.`数量`)  AS `总数量` ,
      sum(B.`金额`) AS `总金额`
    FROM
      ZZS_YB_ZDYFY_9LY.`医保住院结算明细` B
    WHERE B.`医保项目编码`  like '%XB02BXC117B002010101313%'
    group by
      B.`结算单据号`,
      B.`医保项目编码`,
      B.`医保项目名称`) C
    JOIN
      ZZS_YB_ZDYFY_9LY.`医保住院结算主单` A
    ON
      C.`结算单据号` = A.`结算单据号`
      where (A.`主诊断及操作名称` not like '%诊断性血小板减少%' and A.`主诊断及操作名称` not like '%肿瘤%' and A.`主诊断及操作名称` not like '%癌症%')
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"HTTP状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ 复杂ClickHouse SQL解析成功")
            
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"\n📋 基本信息:")
            print(f"  规则名称: {rule_info.get('rule_name', 'N/A')}")
            print(f"  规则类型: {rule_info.get('rule_type', 'N/A')}")
            print(f"  城市: {rule_info.get('city', 'N/A')}")
            print(f"  行为认定: {rule_info.get('behavior', 'N/A')}")
            
            print(f"\n🔍 深度分析:")
            print(f"  数据源数量: {deep_analysis.get('data_sources_count', 'N/A')}")
            print(f"  条件数量: {deep_analysis.get('conditions_count', 'N/A')}")
            print(f"  聚合函数数量: {deep_analysis.get('aggregations_count', 'N/A')}")
            
            print(f"\n🏥 医保项目:")
            medical_items = rule_info.get('medical_items', [])
            if medical_items:
                for item in medical_items:
                    print(f"  - {item}")
            else:
                print("  未检测到医保项目")
            
            # 解析JSON输出查看详细信息
            json_output = deep_analysis.get('json_output', '{}')
            try:
                json_obj = json.loads(json_output)
                
                print(f"\n📊 数据源详情:")
                data_sources = json_obj.get('data_sources', [])
                for ds in data_sources:
                    print(f"  - {ds.get('table_name', 'N/A')} ({ds.get('alias', 'N/A')})")
                
                print(f"\n🔍 条件详情:")
                conditions = json_obj.get('conditions', [])
                for i, cond in enumerate(conditions, 1):
                    field = cond.get('field', {})
                    print(f"  {i}. {field.get('field_name', 'N/A')} ({field.get('field_type', 'N/A')}) {cond.get('operator', 'N/A')} {cond.get('value', 'N/A')}")
                
                print(f"\n📈 聚合函数详情:")
                aggregations = json_obj.get('aggregations', [])
                for i, agg in enumerate(aggregations, 1):
                    print(f"  {i}. {agg.get('function', 'N/A')}({agg.get('field', 'N/A')}) AS {agg.get('alias', 'N/A')}")
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
            
            return True
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        try:
            error_info = response.json()
            print(f"错误信息: {error_info}")
        except:
            print(f"响应内容: {response.text}")
        return False


if __name__ == "__main__":
    print("开始测试复杂ClickHouse SQL解析...\n")
    
    if test_complex_clickhouse_sql():
        print("\n✅ 测试完成")
    else:
        print("\n❌ 测试失败")
