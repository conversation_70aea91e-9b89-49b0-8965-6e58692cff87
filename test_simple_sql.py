#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试SQL执行功能
"""

import urllib.request
import json

def test_simple_sql():
    """简单测试SQL执行"""
    base_url = "http://127.0.0.1:5001"
    
    # 测试默认Oracle实例
    print("测试默认Oracle实例...")
    try:
        test_data = {
            "sql": "SELECT 1 as test_column FROM dual",
            "database": "oracle",
            "instance": "default"
        }
        
        req_data = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=10) as response:
            content = response.read().decode('utf-8')
            data = json.loads(content)
            print(f"Oracle结果: {data}")
            
    except Exception as e:
        print(f"Oracle测试失败: {str(e)}")
    
    # 测试默认PostgreSQL实例
    print("\n测试默认PostgreSQL实例...")
    try:
        test_data = {
            "sql": "SELECT 1 as test_column",
            "database": "pg",
            "instance": "default"
        }
        
        req_data = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=10) as response:
            content = response.read().decode('utf-8')
            data = json.loads(content)
            print(f"PostgreSQL结果: {data}")
            
    except Exception as e:
        print(f"PostgreSQL测试失败: {str(e)}")

if __name__ == "__main__":
    test_simple_sql()
