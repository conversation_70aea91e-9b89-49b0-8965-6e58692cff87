#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复规则检测逻辑修正测试脚本

验证修正后的算法逻辑：
1. 只在医保名称1字段内部进行比较
2. 只在医保名称2字段内部进行比较
3. 不在医保名称1和医保名称2之间进行交叉比较

使用方法：
python test_duplicate_logic_fix.py [hospital_id]

作者: Augment Agent
日期: 2025-07-21
"""

import sys
import requests
import json
from datetime import datetime

def test_duplicate_logic(hospital_id=1):
    """测试修正后的重复规则检测逻辑"""
    url = f"http://localhost:5000/api/hospital-rules/duplicate-analysis/{hospital_id}"
    
    print(f"测试修正后的重复规则检测逻辑")
    print(f"URL: {url}")
    print("=" * 80)
    
    try:
        response = requests.get(url, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✓ API调用成功")
                print(f"总规则数: {data.get('total_rules', 0)}")
                print(f"重复规则数: {data.get('duplicate_rules', 0)}")
                print(f"重复组数: {data.get('duplicate_groups_count', 0)}")
                
                # 详细分析重复组
                groups = data.get('duplicate_groups', [])
                if groups:
                    print(f"\n重复组详细分析:")
                    print("-" * 80)
                    
                    for i, group in enumerate(groups):
                        print(f"\n【重复组 {i+1}】")
                        print(f"规则数量: {group.get('rule_count', 0)}")
                        print(f"相似度: {group.get('similarity', 0):.2f}")
                        print(f"对照ID: {group.get('compare_ids', [])}")
                        
                        # 分析共同医保项目
                        common_names = group.get('common_medical_names', [])
                        print(f"共同医保项目 ({len(common_names)} 个):")
                        for name in common_names:
                            print(f"  - {name}")
                        
                        # 分析规则详情
                        rules = group.get('rules', [])
                        print(f"\n包含的规则 ({len(rules)} 条):")
                        
                        for j, rule in enumerate(rules):
                            print(f"  规则 {j+1}:")
                            print(f"    适用ID: {rule.get('适用ID')}")
                            print(f"    对照ID: {rule.get('对照ID')}")
                            print(f"    规则名称: {rule.get('规则名称', 'N/A')}")
                            print(f"    医保名称1: {rule.get('医保名称1', 'N/A')}")
                            print(f"    医保名称2: {rule.get('医保名称2', 'N/A')}")
                            
                            # 分析这条规则的医保名称
                            if rule.get('医保名称1'):
                                names1 = [name.strip() for name in rule['医保名称1'].split('、') if name.strip()]
                                print(f"    医保名称1拆分: {names1}")
                            
                            if rule.get('医保名称2'):
                                names2 = [name.strip() for name in rule['医保名称2'].split('、') if name.strip()]
                                print(f"    医保名称2拆分: {names2}")
                            
                            print()
                        
                        # 验证重复逻辑
                        print("重复逻辑验证:")
                        verify_duplicate_logic(rules, common_names)
                        print("-" * 80)
                else:
                    print("\n没有发现重复规则")
                
                return True
            else:
                print(f"✗ API返回错误: {data.get('error')}")
                if data.get('details'):
                    print(f"详细错误信息:")
                    print(data.get('details'))
                return False
        else:
            print(f"✗ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return False

def verify_duplicate_logic(rules, common_names):
    """验证重复逻辑是否正确"""
    print("  验证结果:")
    
    # 收集所有医保名称1和医保名称2
    all_names1 = []
    all_names2 = []
    
    for rule in rules:
        if rule.get('医保名称1'):
            names1 = [name.strip() for name in rule['医保名称1'].split('、') if name.strip()]
            all_names1.extend(names1)
        
        if rule.get('医保名称2'):
            names2 = [name.strip() for name in rule['医保名称2'].split('、') if name.strip()]
            all_names2.extend(names2)
    
    # 检查医保名称1中的重复
    names1_count = {}
    for name in all_names1:
        names1_count[name] = names1_count.get(name, 0) + 1
    
    duplicate_names1 = [name for name, count in names1_count.items() if count > 1]
    
    # 检查医保名称2中的重复
    names2_count = {}
    for name in all_names2:
        names2_count[name] = names2_count.get(name, 0) + 1
    
    duplicate_names2 = [name for name, count in names2_count.items() if count > 1]
    
    print(f"    医保名称1中的重复项目: {duplicate_names1}")
    print(f"    医保名称2中的重复项目: {duplicate_names2}")
    
    # 验证共同医保项目是否正确
    expected_common = []
    for name in duplicate_names1:
        expected_common.append(f"医保名称1: {name}")
    for name in duplicate_names2:
        expected_common.append(f"医保名称2: {name}")
    
    print(f"    预期共同项目: {expected_common}")
    print(f"    实际共同项目: {common_names}")
    
    # 检查是否有医保名称1和医保名称2之间的交叉比较
    names1_set = set(all_names1)
    names2_set = set(all_names2)
    cross_names = names1_set & names2_set
    
    if cross_names:
        print(f"    ⚠️  发现医保名称1和医保名称2的交集: {cross_names}")
        print(f"    ⚠️  请确认这些交集项目是否应该被视为重复")
    else:
        print(f"    ✓ 没有发现医保名称1和医保名称2的交集")

def test_algorithm_principles():
    """测试算法原则"""
    print("\n算法原则验证:")
    print("=" * 80)
    
    print("修正后的算法原则:")
    print("1. ✓ 只在医保名称1字段内部进行重复检测")
    print("2. ✓ 只在医保名称2字段内部进行重复检测")
    print("3. ✓ 不在医保名称1和医保名称2之间进行交叉比较")
    print("4. ✓ 基于对照ID进行分组，而不是适用ID")
    print("5. ✓ 使用分隔符（、,，|;；）正确拆分医保项目名称")
    
    print("\n预期改进效果:")
    print("- 减少误报：不会因为医保名称1和医保名称2的交集而产生误报")
    print("- 提高精度：只有真正在同一字段中重复的项目才被识别")
    print("- 逻辑清晰：医保名称1和医保名称2的处理逻辑分离")
    print("- 符合业务：更符合实际的医保项目管理逻辑")

def main():
    """主测试函数"""
    hospital_id = 1
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("重复规则检测逻辑修正测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试医院ID: {hospital_id}")
    
    try:
        # 1. 测试修正后的逻辑
        api_success = test_duplicate_logic(hospital_id)
        
        # 2. 验证算法原则
        test_algorithm_principles()
        
        print("\n" + "=" * 80)
        if api_success:
            print("✓ 逻辑修正测试完成")
            print("请检查上述验证结果，确认重复检测逻辑是否正确")
        else:
            print("✗ 逻辑修正测试发现问题，请检查API实现")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
