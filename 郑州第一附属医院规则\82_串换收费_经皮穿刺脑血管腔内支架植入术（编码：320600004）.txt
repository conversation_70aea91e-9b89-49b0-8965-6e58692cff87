SELECT
  A.`病案号` AS `病案号`,
  A.`结算单据号` AS `结算单据号`,
  A.`医疗机构编码` AS `医疗机构编码`,
  A.`医疗机构名称` AS `医疗机构名称`,
  A.`结算日期` AS `结算日期`,
  A.`住院号` AS `住院号`,
  A.`个人编码` AS `个人编码`,
  A.`患者社会保障号码` AS `患者社会保障号码`,
  A.`身份证号` AS `身份证号`,
  A.`险种类型` AS `险种类型`,
  A.`入院科室` AS `入院科室`,
  A.`出院科室` AS `出院科室`,
  A.`主诊医师姓名` AS `主诊医师姓名`,
  A.`患者姓名` AS `患者姓名`,
  A.`患者性别` AS `患者性别`,
  A.`患者出生日期` AS `患者出生日期`,
  A.`患者年龄` AS `患者年龄`,
  A.`异地标志` AS `异地标志`,
  A.`入院日期` AS `入院日期`,
  A.`出院日期` AS `出院日期`,
  toDate(A.`出院日期`) - toDate(A.`入院日期`) + 1 AS `住院天数`,
  A.`医疗总费用` AS `医疗总费用`,
  A.`基本统筹支付` AS `基本统筹支付`,
  A.`个人自付` AS `个人自付`,
  A.`个人自费` AS `个人自费`,
  A.`符合基本医疗保险的费用` AS `符合基本医疗保险的费用`,
  A.`入院诊断编码` AS `入院诊断编码`,
  A.`入院诊断名称` AS `入院诊断名称`,
  A.`出院诊断编码` AS `出院诊断编码`,
  A.`出院诊断名称` AS `出院诊断名称`,
  A.`主手术及操作编码` AS `主手术及操作编码`,
  A.`主手术及操作名称` AS `主手术及操作名称`,
  A.`其他手术及操作编码` AS `其他手术及操作编码`,
  A.`其他手术及操作名称` AS `其他手术及操作名称`,
  C.`医保项目编码` AS `医保项目编码`,
  C.`医保项目名称` AS `医保项目名称`,
  C.`总数量` AS `总数量`,
  C.`总金额` AS `总金额`
FROM
  (SELECT
  B.`结算单据号`  AS `结算单据号` ,
  B.`医保项目编码` AS `医保项目编码`,
  B.`医保项目名称` AS `医保项目名称`,
  sum(B.`数量`)  AS `总数量` ,
  sum(B.`金额`) AS `总金额`
FROM
  ZZS_YB_ZDYFY_9LY.`医保住院结算明细` B
WHERE B.`医保项目名称`  like '%经皮穿刺脑血管腔内支架植入术%' 
group  by 
  B.`结算单据号`  AS `结算单据号` ,
  B.`医保项目编码` AS `医保项目编码`,
  B.`医保项目名称` AS `医保项目名称`) C
JOIN
  ZZS_YB_ZDYFY_9LY.`医保住院结算主单` A
ON
  C.`结算单据号` = A.`结算单据号` ;
 