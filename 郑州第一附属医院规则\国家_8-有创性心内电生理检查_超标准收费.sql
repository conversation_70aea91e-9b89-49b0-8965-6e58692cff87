WITH tab1 AS ( 
	SELECT 	
		结算单据号,
		医保项目编码,
		医保项目名称,
		医院项目编码,
		医院项目名称,
		单价,
		规格,
		费用类别,
		支付类别,
		sum( 数量 ) AS 使用数量,
		sum( 医保范围内金额 ) AS 医保范围内总金额
	FROM 医保住院结算明细 
	WHERE 医保项目名称 = '有创性心内电生理检查' AND 数量 > 1
	GROUP BY
		结算单据号,
		医保项目编码,
		医保项目名称,
		医院项目编码,
		医院项目名称,
		单价,
		规格,
		费用类别,
		支付类别
) 
SELECT
	a.结算单据号,
	a.医疗机构编码,
	a.医疗机构名称,
	a.住院号,
	a.个人编码,
	a.患者姓名,
	a.患者性别,
	a.患者社会保障号码,
	a.患者年龄,
	a.险种类型,
	a.入院日期,
	a.出院科室,
	a.出院诊断名称,
	a.出院日期,
	TRUNC(a.出院日期) - TRUNC(a.入院日期)+1 as 住院天数,
	a.结算日期,
	b.医保项目编码,
	b.医保项目名称,
	b.医院项目编码,
	b.医院项目名称,
	B.费用类别,
	b.支付类别,
	B.规格,
	b.单价,
	b.使用数量,
	( b.使用数量 - (TRUNC(a.出院日期) - TRUNC(a.入院日期)+1) * 24 ) AS 超出数量,
	( b.使用数量 - (TRUNC(a.出院日期) - TRUNC(a.入院日期)+1) * 24 ) * b.单价 AS 超出金额,
	b.医保范围内总金额
FROM
	医保住院结算主单 a
JOIN tab1 b ON a.结算单据号 = b.结算单据号    