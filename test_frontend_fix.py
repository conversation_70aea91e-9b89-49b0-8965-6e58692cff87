"""
测试前端显示修复
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_frontend_display():
    """测试前端显示功能"""
    print("=== 测试前端显示功能 ===")
    
    # 测试包含中文的PostgreSQL SQL
    sql_content = """
    -- 规则名称: 测试前端显示
    -- 城市: 北京市
    -- 行为认定: 超量使用
    SELECT 结算单据号, SUM(数量) as 总数量
    FROM 医保住院结算明细
    WHERE 医保项目名称 = 'CT检查'
    GROUP BY 结算单据号
    HAVING SUM(数量) > 3
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"HTTP状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ API调用成功")
            
            # 检查关键字段
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"规则名称: {rule_info.get('rule_name', 'N/A')}")
            print(f"规则类型: {rule_info.get('rule_type', 'N/A')}")
            print(f"城市: {rule_info.get('city', 'N/A')}")
            print(f"行为认定: {rule_info.get('behavior', 'N/A')}")
            print(f"置信度分数: {rule_info.get('confidence_score', 'N/A')}")
            
            print(f"数据源数量: {deep_analysis.get('data_sources_count', 'N/A')}")
            print(f"条件数量: {deep_analysis.get('conditions_count', 'N/A')}")
            print(f"聚合函数数量: {deep_analysis.get('aggregations_count', 'N/A')}")
            print(f"重复收费模式: {deep_analysis.get('has_duplicate_pattern', 'N/A')}")
            
            # 检查JSON输出
            json_output = deep_analysis.get('json_output', '')
            print(f"JSON输出长度: {len(json_output)} 字符")
            
            # 测试JSON解析
            try:
                json_obj = json.loads(json_output)
                print("✅ JSON输出格式正确")
                
                # 测试URL编码（模拟前端处理）
                import urllib.parse
                encoded = urllib.parse.quote(json_output)
                decoded = urllib.parse.unquote(encoded)
                
                if decoded == json_output:
                    print("✅ URL编码/解码正常")
                else:
                    print("❌ URL编码/解码异常")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON格式错误: {e}")
                return False
            
            # 检查医保项目
            medical_items = rule_info.get('medical_items', [])
            print(f"医保项目: {medical_items}")
            
            return True
        else:
            print(f"❌ API返回错误: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


def test_clickhouse_frontend():
    """测试ClickHouse SQL前端显示"""
    print("\n=== 测试ClickHouse SQL前端显示 ===")
    
    sql_content = """
    -- 规则名称: ClickHouse测试规则
    -- 城市: 上海市
    -- 行为认定: 年龄限制
    SELECT
    A.`病案号` AS `病案号`,
    A.`结算单据号` AS `结算单据号`,
    A.`医疗机构编码` AS `医疗机构编码`,
    A.`医疗机构名称` AS `医疗机构名称`,
    A.`结算日期` AS `结算日期`
    FROM 医保住院结算主单 A
    WHERE A.`患者年龄` > 65
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"HTTP状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ ClickHouse SQL解析成功")
            
            rule_info = result['rule_info']
            print(f"规则名称: {rule_info.get('rule_name', 'N/A')}")
            print(f"规则类型: {rule_info.get('rule_type', 'N/A')}")
            print(f"城市: {rule_info.get('city', 'N/A')}")
            
            return True
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


if __name__ == "__main__":
    print("开始测试前端显示修复...\n")
    
    tests = [
        ("PostgreSQL前端显示", test_frontend_display),
        ("ClickHouse前端显示", test_clickhouse_frontend)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 前端显示修复测试全部通过！")
        print("\n📋 修复总结:")
        print("1. ✅ ClickHouse SQL解析问题已修复")
        print("2. ✅ 前端JSON编码问题已修复")
        print("3. ✅ 深度解析器集成功能正常")
        print("4. ✅ 多方言SQL支持正常")
    else:
        print("⚠️  仍有问题需要解决。")
