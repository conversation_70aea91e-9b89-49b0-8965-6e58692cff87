<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL规则解析器</title>

    <!-- CSS 文件 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">

    <!-- JavaScript 文件 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <style>
        :root {
            --primary-color: #0078D4;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            padding: 20px;
        }

        .container-fluid {
            max-width: 2400px;
        }

        .card {
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 120, 212, 0.25);
        }

        .nav-tabs .nav-link.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .nav-tabs .nav-link {
            color: var(--primary-color);
        }

        .nav-tabs .nav-link:hover {
            border-color: var(--primary-color);
        }

        .sql-textarea {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .rule-info-card {
            border-left: 4px solid var(--primary-color);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .condition-badge {
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.875rem;
            margin: 2px;
            display: inline-block;
        }

        .rule-type-badge {
            font-size: 0.875rem;
            padding: 6px 12px;
        }

        .confidence-bar {
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
            transition: width 0.3s ease;
        }

        .file-drop-zone {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-drop-zone:hover {
            border-color: var(--primary-color);
            background-color: #e3f2fd;
        }

        .file-drop-zone.dragover {
            border-color: var(--primary-color);
            background-color: #e3f2fd;
        }

        .loading-spinner {
            display: none;
        }

        .results-table {
            font-size: 0.9rem;
        }

        .results-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 10px 20px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(0, 120, 212, 0.3);
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background-color: #106ebe;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 120, 212, 0.4);
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="/" class="back-button">
        <i class="bi bi-arrow-left"></i>
        返回主页
    </a>

    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">
                            <i class="bi bi-cpu me-2"></i>
                            深度SQL规则解析器
                        </h3>
                        <p class="mb-0 mt-2">基于AST的深度SQL语义分析，智能提取医保规则的业务逻辑和结构信息</p>
                        <div class="mt-2">
                            <span class="badge bg-light text-dark me-2">
                                <i class="bi bi-check-circle me-1"></i>规则类型识别
                            </span>
                            <span class="badge bg-light text-dark me-2">
                                <i class="bi bi-diagram-3 me-1"></i>数据源分析
                            </span>
                            <span class="badge bg-light text-dark me-2">
                                <i class="bi bi-funnel me-1"></i>条件提取
                            </span>
                            <span class="badge bg-light text-dark me-2">
                                <i class="bi bi-arrow-repeat me-1"></i>重复收费检测
                            </span>
                            <span class="badge bg-light text-dark">
                                <i class="bi bi-calculator me-1"></i>聚合函数分析
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作选项卡 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="parserTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="single-tab" data-bs-toggle="tab" data-bs-target="#single-parse" type="button" role="tab">
                                    <i class="bi bi-file-text me-2"></i>单个SQL解析
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="file-tab" data-bs-toggle="tab" data-bs-target="#file-parse" type="button" role="tab">
                                    <i class="bi bi-file-earmark-code me-2"></i>文件上传解析
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="directory-tab" data-bs-toggle="tab" data-bs-target="#directory-parse" type="button" role="tab">
                                    <i class="bi bi-folder me-2"></i>目录批量解析
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="results-tab" data-bs-toggle="tab" data-bs-target="#results-view" type="button" role="tab">
                                    <i class="bi bi-table me-2"></i>解析结果查看
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content mt-4" id="parserTabContent">
                            <!-- 单个SQL解析 -->
                            <div class="tab-pane fade show active" id="single-parse" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>SQL输入</h5>
                                        <textarea id="sqlInput" class="form-control sql-textarea" rows="15"
                                                placeholder="请输入要解析的SQL语句...&#10;&#10;深度解析器示例：&#10;-- 规则名称: 血液透析与血液灌流同日重复收费检测&#10;-- 城市: 北京市&#10;-- 行为认定: 重复收费&#10;WITH duplicate_check AS (&#10;  SELECT A.结算单据号, to_char(B.项目使用日期,'yyyy-MM-dd hh24') 使用日期,&#10;         SUM(B.数量) AS 总数量&#10;  FROM 医保住院结算主单 A&#10;  JOIN 医保住院结算明细 B ON A.结算单据号 = B.结算单据号&#10;  WHERE A.患者年龄 > 18 AND A.患者性别 = '男'&#10;    AND B.医保项目名称 IN ('血液透析', '血液灌流')&#10;  GROUP BY A.结算单据号, to_char(B.项目使用日期,'yyyy-MM-dd hh24')&#10;  HAVING SUM(B.数量) > 1&#10;),&#10;intersect_pattern AS (&#10;  SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = '血液透析'&#10;  INTERSECT&#10;  SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = '血液灌流'&#10;)&#10;SELECT * FROM duplicate_check D JOIN intersect_pattern I USING(结算单据号)"></textarea>
                                        <div class="mt-3">
                                            <button id="parseSqlBtn" class="btn btn-primary">
                                                <i class="bi bi-play-circle me-2"></i>解析SQL
                                            </button>
                                            <button id="clearSqlBtn" class="btn btn-outline-secondary ms-2">
                                                <i class="bi bi-x-circle me-2"></i>清空
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>解析结果</h5>
                                        <div id="singleParseResult">
                                            <div class="text-muted text-center py-5">
                                                <i class="bi bi-info-circle fs-1"></i>
                                                <p class="mt-3">请在左侧输入SQL语句并点击解析按钮</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 文件上传解析 -->
                            <div class="tab-pane fade" id="file-parse" role="tabpanel">
                                <div class="row">
                                    <div class="col-12">
                                        <h5>文件上传</h5>
                                        <div class="file-drop-zone" id="fileDropZone">
                                            <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                                            <p class="mt-3 mb-2">拖拽文件到此处或点击选择文件</p>
                                            <p class="text-muted small">支持 .sql, .txt, .xlsx, .xls 格式文件</p>
                                            <input type="file" id="fileInput" multiple accept=".sql,.txt,.xlsx,.xls" style="display: none;">
                                        </div>

                                        <div id="fileList" class="mt-3" style="display: none;">
                                            <h6>已选择的文件：</h6>
                                            <ul id="selectedFiles" class="list-group"></ul>
                                            <div class="mt-3">
                                                <button id="parseFilesBtn" class="btn btn-primary">
                                                    <i class="bi bi-play-circle me-2"></i>开始解析
                                                </button>
                                                <button id="clearFilesBtn" class="btn btn-outline-secondary ms-2">
                                                    <i class="bi bi-x-circle me-2"></i>清空文件
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 目录批量解析 -->
                            <div class="tab-pane fade" id="directory-parse" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h5>目录路径</h5>
                                        <div class="input-group">
                                            <input type="text" id="directoryPath" class="form-control"
                                                   placeholder="输入包含SQL文件的目录路径"
                                                   value="郑州第一附属医院规则">
                                            <button id="parseDirectoryBtn" class="btn btn-primary">
                                                <i class="bi bi-folder-check me-2"></i>解析目录
                                            </button>
                                        </div>
                                        <div class="form-text">
                                            <i class="bi bi-info-circle me-1"></i>
                                            将解析目录中所有的 .sql, .txt, .xlsx, .xls 文件
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 解析结果查看 -->
                            <div class="tab-pane fade" id="results-view" role="tabpanel">
                                <div id="resultsContainer">
                                    <div class="text-muted text-center py-5">
                                        <i class="bi bi-table fs-1"></i>
                                        <p class="mt-3">暂无解析结果，请先进行解析操作</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载提示 -->
        <div id="loadingModal" class="modal fade" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-body text-center py-4">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h5 id="loadingText">正在解析...</h5>
                        <p class="text-muted mb-0">请稍候，正在处理您的请求</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let parseResults = [];
        let loadingModal;

        // 页面加载完成后初始化
        $(document).ready(function() {
            loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            initializeEventHandlers();
        });

        // 初始化事件处理器
        function initializeEventHandlers() {
            // 单个SQL解析
            $('#parseSqlBtn').click(function() {
                const sqlContent = $('#sqlInput').val().trim();
                if (!sqlContent) {
                    showAlert('请输入SQL内容', 'warning');
                    return;
                }
                parseSingleSQL(sqlContent);
            });

            // 清空SQL输入
            $('#clearSqlBtn').click(function() {
                $('#sqlInput').val('');
                $('#singleParseResult').html(`
                    <div class="text-muted text-center py-5">
                        <i class="bi bi-info-circle fs-1"></i>
                        <p class="mt-3">请在左侧输入SQL语句并点击解析按钮</p>
                    </div>
                `);
            });

            // 文件拖拽上传
            const fileDropZone = $('#fileDropZone');
            const fileInput = $('#fileInput');

            fileDropZone.click(function() {
                fileInput.click();
            });

            fileInput.change(function() {
                handleFileSelection(this.files);
            });

            // 拖拽事件
            fileDropZone.on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            fileDropZone.on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            fileDropZone.on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                const files = e.originalEvent.dataTransfer.files;
                handleFileSelection(files);
            });

            // 解析文件
            $('#parseFilesBtn').click(function() {
                const files = fileInput[0].files;
                if (files.length === 0) {
                    showAlert('请先选择文件', 'warning');
                    return;
                }
                parseFiles(files);
            });

            // 清空文件
            $('#clearFilesBtn').click(function() {
                fileInput.val('');
                $('#fileList').hide();
                $('#selectedFiles').empty();
            });

            // 目录解析
            $('#parseDirectoryBtn').click(function() {
                const directoryPath = $('#directoryPath').val().trim();
                if (!directoryPath) {
                    showAlert('请输入目录路径', 'warning');
                    return;
                }
                parseDirectory(directoryPath);
            });
        }

        // 处理文件选择
        function handleFileSelection(files) {
            if (files.length === 0) return;

            const fileList = $('#selectedFiles');
            fileList.empty();

            Array.from(files).forEach(file => {
                const fileSize = (file.size / 1024).toFixed(2);
                const listItem = $(`
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-file-earmark-code me-2"></i>
                            ${file.name}
                        </div>
                        <span class="badge bg-secondary">${fileSize} KB</span>
                    </li>
                `);
                fileList.append(listItem);
            });

            $('#fileList').show();
        }

        // 解析单个SQL
        function parseSingleSQL(sqlContent) {
            showLoading('正在解析SQL...');

            $.ajax({
                url: '/api/parse_sql_content',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ sql_content: sqlContent }),
                success: function(response) {
                    hideLoading();
                    console.log('API响应:', response); // 调试日志

                    try {
                        if (response.success) {
                            console.log('开始显示解析结果'); // 调试日志
                            displaySingleResult(response.rule_info, response.explanation, response.completeness, response.deep_analysis);
                            // 添加到结果列表
                            parseResults.push(response.rule_info);
                            updateResultsTab();
                            console.log('解析结果显示完成'); // 调试日志
                        } else {
                            showAlert('解析失败: ' + response.error, 'danger');
                        }
                    } catch (error) {
                        console.error('前端处理响应时出错:', error);
                        showAlert('前端处理响应时出错: ' + error.message, 'danger');
                    }
                },
                error: function(xhr) {
                    hideLoading();
                    const error = xhr.responseJSON ? xhr.responseJSON.error : '网络错误';
                    showAlert('解析失败: ' + error, 'danger');
                }
            });
        }

        // 解析文件
        function parseFiles(files) {
            showLoading('正在解析文件...');

            const formData = new FormData();
            Array.from(files).forEach(file => {
                formData.append('files', file);
            });

            $.ajax({
                url: '/api/parse_sql_files',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    hideLoading();
                    if (response.success) {
                        parseResults = parseResults.concat(response.results);
                        updateResultsTab();
                        showAlert(`成功解析 ${response.total_count} 个规则`, 'success');
                        // 切换到结果查看标签
                        $('#results-tab').click();
                    } else {
                        showAlert('解析失败: ' + response.error, 'danger');
                    }
                },
                error: function(xhr) {
                    hideLoading();
                    const error = xhr.responseJSON ? xhr.responseJSON.error : '网络错误';
                    showAlert('解析失败: ' + error, 'danger');
                }
            });
        }

        // 解析目录
        function parseDirectory(directoryPath) {
            showLoading('正在解析目录...');

            $.ajax({
                url: '/api/parse_directory',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ directory_path: directoryPath }),
                success: function(response) {
                    hideLoading();
                    if (response.success) {
                        parseResults = response.results;
                        updateResultsTab();
                        showAlert(`成功解析 ${response.total_count} 个规则`, 'success');
                        // 切换到结果查看标签
                        $('#results-tab').click();
                    } else {
                        showAlert('解析失败: ' + response.error, 'danger');
                    }
                },
                error: function(xhr) {
                    hideLoading();
                    const error = xhr.responseJSON ? xhr.responseJSON.error : '网络错误';
                    showAlert('解析失败: ' + error, 'danger');
                }
            });
        }

        // 显示单个解析结果
        function displaySingleResult(ruleInfo, explanation, completeness, deepAnalysis) {
            const resultHtml = `
                <div class="rule-info-card card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="bi bi-cpu me-2"></i>
                            ${ruleInfo.rule_name || '未命名规则'}
                        </h6>
                        <span class="badge bg-success">深度解析</span>
                    </div>
                    <div class="card-body">
                        <!-- 基本信息行 -->
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="bi bi-info-circle me-2"></i>基本信息</h6>
                                <p><strong>规则类型:</strong>
                                    <span class="badge rule-type-badge bg-primary">${ruleInfo.rule_type}</span>
                                </p>
                                <p><strong>城市:</strong> ${ruleInfo.city || '未指定'}</p>
                                <p><strong>行为认定:</strong> ${ruleInfo.behavior || '未指定'}</p>
                                <p><strong>规则来源:</strong> ${ruleInfo.source || '未指定'}</p>
                                <p><strong>置信度分数:</strong>
                                    <span class="badge bg-info">${(ruleInfo.confidence_score * 100).toFixed(1)}%</span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="bi bi-diagram-3 me-2"></i>解析统计</h6>
                                <p><strong>数据源:</strong> ${deepAnalysis?.data_sources_count || 0} 个表</p>
                                <p><strong>业务条件:</strong> ${deepAnalysis?.conditions_count || 0} 个</p>
                                <p><strong>聚合函数:</strong> ${deepAnalysis?.aggregations_count || 0} 个</p>
                                <p><strong>重复收费模式:</strong>
                                    ${deepAnalysis?.has_duplicate_pattern ?
                                        '<span class="badge bg-warning">检测到</span>' :
                                        '<span class="badge bg-secondary">未检测到</span>'
                                    }
                                </p>
                            </div>
                        </div>

                        <!-- 医保项目信息 -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6><i class="bi bi-heart-pulse me-2"></i>医保项目</h6>
                                <div class="mb-2">
                                    ${ruleInfo.medical_items && ruleInfo.medical_items.length > 0 ?
                                        ruleInfo.medical_items.map(item =>
                                            `<span class="condition-badge">${item}</span>`
                                        ).join('') :
                                        '<span class="text-muted">未检测到医保项目</span>'
                                    }
                                </div>
                                ${ruleInfo.violation_items && ruleInfo.violation_items.length > 0 ? `
                                    <h6><i class="bi bi-exclamation-triangle me-2"></i>违规项目</h6>
                                    <div class="mb-2">
                                        ${ruleInfo.violation_items.map(item =>
                                            `<span class="condition-badge bg-danger text-white">${item}</span>`
                                        ).join('')}
                                    </div>
                                ` : ''}
                            </div>
                            <div class="col-md-6">
                                ${displayDataSources(ruleInfo.data_sources)}
                            </div>
                        </div>

                        <!-- 业务条件 -->
                        ${displayConditions(ruleInfo.conditions)}

                        <!-- 聚合函数信息 -->
                        ${displayAggregations(ruleInfo.aggregations)}

                        <!-- 重复收费模式 -->
                        ${displayDuplicatePattern(ruleInfo.duplicate_pattern)}

                        <!-- 分类置信度 -->
                        <div class="mt-3">
                            <h6><i class="bi bi-speedometer2 me-2"></i>分类置信度</h6>
                            <div class="confidence-bar">
                                <div class="confidence-fill" style="width: ${(explanation.confidence * 100)}%"></div>
                            </div>
                            <small class="text-muted">${(explanation.confidence * 100).toFixed(1)}%</small>
                        </div>

                        <!-- 分类依据 -->
                        ${explanation.explanations && explanation.explanations.length > 0 ? `
                            <div class="mt-3">
                                <h6><i class="bi bi-list-check me-2"></i>分类依据</h6>
                                <ul class="list-unstyled">
                                    ${explanation.explanations.map(exp => `<li><i class="bi bi-check-circle text-success me-2"></i>${exp}</li>`).join('')}
                                </ul>
                            </div>
                        ` : ''}

                        <!-- JSON输出按钮 -->
                        <div class="mt-3">
                            <button class="btn btn-outline-info btn-sm" onclick="showJsonOutput('${encodeURIComponent(deepAnalysis?.json_output || '{}')}')">
                                <i class="bi bi-code-square me-2"></i>查看JSON输出
                            </button>
                        </div>
                    </div>
                </div>
            `;

            $('#singleParseResult').html(resultHtml);
        }

        // 显示数据源信息
        function displayDataSources(dataSources) {
            if (!dataSources || dataSources.length === 0) {
                return '<h6><i class="bi bi-database me-2"></i>数据源</h6><p class="text-muted">未检测到数据源</p>';
            }

            let html = '<h6><i class="bi bi-database me-2"></i>数据源</h6>';
            dataSources.forEach((ds, index) => {
                html += `
                    <div class="mb-2">
                        <span class="condition-badge bg-info text-white">
                            ${ds.table_name}${ds.alias && ds.alias !== ds.table_name ? ` (${ds.alias})` : ''}
                        </span>
                        ${ds.join_type && ds.join_type !== 'INNER' ?
                            `<small class="text-muted ms-1">${ds.join_type} JOIN</small>` : ''
                        }
                    </div>
                `;
            });
            return html;
        }

        // 显示聚合函数信息
        function displayAggregations(aggregations) {
            if (!aggregations || aggregations.length === 0) {
                return '';
            }

            let html = '<div class="mt-3"><h6><i class="bi bi-calculator me-2"></i>聚合函数</h6>';
            aggregations.forEach(agg => {
                html += `
                    <div class="mb-2">
                        <span class="condition-badge bg-success text-white">
                            ${agg.function}(${agg.field.field_name})
                            ${agg.alias ? ` AS ${agg.alias}` : ''}
                        </span>
                    </div>
                `;
            });
            html += '</div>';
            return html;
        }

        // 显示重复收费模式
        function displayDuplicatePattern(duplicatePattern) {
            if (!duplicatePattern) {
                return '';
            }

            let html = '<div class="mt-3"><h6><i class="bi bi-arrow-repeat me-2"></i>重复收费模式</h6>';
            html += '<div class="row">';

            if (duplicatePattern.primary_items && duplicatePattern.primary_items.length > 0) {
                html += '<div class="col-md-6">';
                html += '<p><strong>主要项目:</strong></p>';
                duplicatePattern.primary_items.forEach(item => {
                    html += `<span class="condition-badge bg-primary text-white">${item}</span>`;
                });
                html += '</div>';
            }

            if (duplicatePattern.conflict_items && duplicatePattern.conflict_items.length > 0) {
                html += '<div class="col-md-6">';
                html += '<p><strong>冲突项目:</strong></p>';
                duplicatePattern.conflict_items.forEach(item => {
                    html += `<span class="condition-badge bg-danger text-white">${item}</span>`;
                });
                html += '</div>';
            }

            html += '</div>';
            html += `<p class="mt-2"><strong>时间精度:</strong> ${duplicatePattern.time_precision}</p>`;
            html += `<p><strong>检测方法:</strong> ${duplicatePattern.detection_method}</p>`;
            html += '</div>';

            return html;
        }

        // 显示条件信息
        function displayConditions(conditions) {
            if (!conditions) return '';

            let conditionsHtml = '';

            if (conditions.age_range || conditions.gender ||
                (conditions.include_diagnoses && conditions.include_diagnoses.length > 0) ||
                (conditions.exclude_diagnoses && conditions.exclude_diagnoses.length > 0) ||
                conditions.quantity_threshold) {

                conditionsHtml = '<div class="mt-3"><h6><i class="bi bi-funnel me-2"></i>业务条件</h6>';

                if (conditions.age_range) {
                    conditionsHtml += `<p><strong>年龄限制:</strong> ${conditions.age_range}</p>`;
                }
                if (conditions.gender) {
                    conditionsHtml += `<p><strong>性别限制:</strong> ${conditions.gender}</p>`;
                }
                if (conditions.quantity_threshold) {
                    conditionsHtml += `<p><strong>数量阈值:</strong> ${conditions.quantity_threshold}</p>`;
                }
                if (conditions.include_diagnoses && conditions.include_diagnoses.length > 0) {
                    conditionsHtml += `<p><strong>包含诊断:</strong></p><div class="mb-2">`;
                    conditions.include_diagnoses.forEach(diag => {
                        conditionsHtml += `<span class="condition-badge">${diag}</span>`;
                    });
                    conditionsHtml += '</div>';
                }
                if (conditions.exclude_diagnoses && conditions.exclude_diagnoses.length > 0) {
                    conditionsHtml += `<p><strong>排除诊断:</strong></p><div class="mb-2">`;
                    conditions.exclude_diagnoses.forEach(diag => {
                        conditionsHtml += `<span class="condition-badge bg-warning text-dark">${diag}</span>`;
                    });
                    conditionsHtml += '</div>';
                }
                if (conditions.include_departments && conditions.include_departments.length > 0) {
                    conditionsHtml += `<p><strong>包含科室:</strong></p><div class="mb-2">`;
                    conditions.include_departments.forEach(dept => {
                        conditionsHtml += `<span class="condition-badge bg-info text-white">${dept}</span>`;
                    });
                    conditionsHtml += '</div>';
                }
                if (conditions.time_conditions && conditions.time_conditions.length > 0) {
                    conditionsHtml += `<p><strong>时间条件:</strong></p><div class="mb-2">`;
                    conditions.time_conditions.forEach(time => {
                        conditionsHtml += `<span class="condition-badge bg-secondary text-white">${time}</span>`;
                    });
                    conditionsHtml += '</div>';
                }

                conditionsHtml += '</div>';
            }

            return conditionsHtml;
        }

        // 更新结果查看标签
        function updateResultsTab() {
            if (parseResults.length === 0) {
                $('#resultsContainer').html(`
                    <div class="text-muted text-center py-5">
                        <i class="bi bi-table fs-1"></i>
                        <p class="mt-3">暂无解析结果，请先进行解析操作</p>
                    </div>
                `);
                return;
            }

            // 统计信息
            const stats = calculateStats(parseResults);

            const resultsHtml = `
                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h3 class="mb-0">${parseResults.length}</h3>
                                <p class="mb-0">总规则数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h3 class="mb-0">${stats.ruleTypes}</h3>
                                <p class="mb-0">规则类型数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h3 class="mb-0">${stats.medicalItems}</h3>
                                <p class="mb-0">医保项目数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card">
                            <div class="card-body text-center">
                                <h3 class="mb-0">${stats.conditionRules}</h3>
                                <p class="mb-0">有条件规则</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 导出按钮 -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="d-flex gap-2">
                            <button class="btn btn-success" onclick="exportResults('excel')">
                                <i class="bi bi-file-earmark-excel me-2"></i>导出Excel
                            </button>
                            <button class="btn btn-info" onclick="exportResults('json')">
                                <i class="bi bi-file-earmark-code me-2"></i>导出JSON
                            </button>
                            <button class="btn btn-outline-danger" onclick="clearResults()">
                                <i class="bi bi-trash me-2"></i>清空结果
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 结果表格 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">解析结果详情</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="resultsTable" class="table table-striped results-table">
                                <thead>
                                    <tr>
                                        <th>规则名称</th>
                                        <th>规则类型</th>
                                        <th>城市</th>
                                        <th>行为认定</th>
                                        <th>医保项目数</th>
                                        <th>年龄限制</th>
                                        <th>性别限制</th>
                                        <th>数量阈值</th>
                                        <th>文件来源</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${parseResults.map(rule => `
                                        <tr>
                                            <td>${rule.rule_name || '未命名'}</td>
                                            <td><span class="badge bg-primary">${rule.rule_type}</span></td>
                                            <td>${rule.city || '-'}</td>
                                            <td>${rule.behavior || '-'}</td>
                                            <td>${rule.medical_items.length}</td>
                                            <td>${rule.conditions.age_range || '-'}</td>
                                            <td>${rule.conditions.gender || '-'}</td>
                                            <td>${rule.conditions.quantity_threshold || '-'}</td>
                                            <td>${rule.sql_content || '-'}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            $('#resultsContainer').html(resultsHtml);

            // 初始化DataTable
            $('#resultsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/zh.json'
                },
                pageLength: 25,
                order: [[0, 'asc']]
            });
        }

        // 计算统计信息
        function calculateStats(results) {
            const ruleTypes = new Set();
            let medicalItems = 0;
            let conditionRules = 0;

            results.forEach(rule => {
                ruleTypes.add(rule.rule_type);
                medicalItems += rule.medical_items.length;

                if (rule.conditions.age_range || rule.conditions.gender ||
                    rule.conditions.include_diagnoses.length > 0 ||
                    rule.conditions.exclude_diagnoses.length > 0 ||
                    rule.conditions.quantity_threshold) {
                    conditionRules++;
                }
            });

            return {
                ruleTypes: ruleTypes.size,
                medicalItems: medicalItems,
                conditionRules: conditionRules
            };
        }

        // 导出结果
        function exportResults(format) {
            if (parseResults.length === 0) {
                showAlert('没有数据可导出', 'warning');
                return;
            }

            showLoading('正在导出数据...');

            $.ajax({
                url: '/api/export_parsed_rules',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    rules: parseResults,
                    format: format
                }),
                xhrFields: {
                    responseType: 'blob'
                },
                success: function(data, status, xhr) {
                    hideLoading();

                    // 获取文件名
                    const disposition = xhr.getResponseHeader('Content-Disposition');
                    let filename = `规则解析结果.${format === 'excel' ? 'xlsx' : 'json'}`;
                    if (disposition && disposition.indexOf('filename=') !== -1) {
                        filename = disposition.split('filename=')[1].replace(/"/g, '');
                    }

                    // 创建下载链接
                    const url = window.URL.createObjectURL(data);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showAlert('导出成功', 'success');
                },
                error: function(xhr) {
                    hideLoading();
                    const error = xhr.responseJSON ? xhr.responseJSON.error : '导出失败';
                    showAlert('导出失败: ' + error, 'danger');
                }
            });
        }

        // 清空结果
        function clearResults() {
            if (confirm('确定要清空所有解析结果吗？')) {
                parseResults = [];
                updateResultsTab();
                showAlert('已清空解析结果', 'info');
            }
        }

        // 显示加载提示
        function showLoading(text = '正在处理...') {
            $('#loadingText').text(text);
            loadingModal.show();
        }

        // 隐藏加载提示
        function hideLoading() {
            loadingModal.hide();
        }

        // 显示JSON输出
        function showJsonOutput(encodedJson) {
            try {
                const jsonStr = decodeURIComponent(encodedJson);
                const jsonObj = JSON.parse(jsonStr);
                const formattedJson = JSON.stringify(jsonObj, null, 2);

                const modalHtml = `
                    <div class="modal fade" id="jsonModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">
                                        <i class="bi bi-code-square me-2"></i>深度解析JSON输出
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <pre class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"><code>${formattedJson}</code></pre>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                    <button type="button" class="btn btn-primary" onclick="copyJsonToClipboard('${encodedJson}')">
                                        <i class="bi bi-clipboard me-2"></i>复制到剪贴板
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 移除现有的modal
                $('#jsonModal').remove();

                // 添加新的modal
                $('body').append(modalHtml);

                // 显示modal
                const modal = new bootstrap.Modal(document.getElementById('jsonModal'));
                modal.show();

            } catch (e) {
                showAlert('JSON解析失败: ' + e.message, 'danger');
            }
        }

        // 复制JSON到剪贴板
        function copyJsonToClipboard(encodedJson) {
            try {
                const jsonStr = decodeURIComponent(encodedJson);
                const jsonObj = JSON.parse(jsonStr);
                const formattedJson = JSON.stringify(jsonObj, null, 2);

                navigator.clipboard.writeText(formattedJson).then(() => {
                    showAlert('JSON已复制到剪贴板', 'success');
                }).catch(() => {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = formattedJson;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showAlert('JSON已复制到剪贴板', 'success');
                });
            } catch (e) {
                showAlert('复制失败: ' + e.message, 'danger');
            }
        }

        // 显示提示消息
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // 移除现有的alert
            $('.alert').remove();

            // 添加新的alert到页面顶部
            $('body').prepend(alertHtml);

            // 3秒后自动消失
            setTimeout(() => {
                $('.alert').fadeOut();
            }, 3000);
        }
    </script>
</body>
</html>