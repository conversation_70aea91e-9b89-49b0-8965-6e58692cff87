<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框修复测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>模态框修复测试</h1>
        
        <div class="alert alert-info">
            <h5>测试说明</h5>
            <p>这个页面用于测试规则知识库页面的模态框修复是否有效。</p>
            <p>修复内容：</p>
            <ul>
                <li>改进了模态框初始化逻辑，使用 <code>bootstrap.Modal.getInstance()</code> 检查现有实例</li>
                <li>如果实例不存在，则创建新实例</li>
                <li>确保模态框元素存在后再进行操作</li>
                <li>添加了更详细的错误处理和日志</li>
            </ul>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试步骤</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>打开规则知识库页面</li>
                            <li>点击"新增规则"按钮</li>
                            <li>检查是否能正常打开模态框</li>
                            <li>关闭模态框</li>
                            <li>再次点击"新增规则"按钮</li>
                            <li>确认模态框能重复正常打开</li>
                        </ol>
                        
                        <div class="mt-3">
                            <a href="/rule_knowledge_base" class="btn btn-primary" target="_blank">
                                打开规则知识库页面
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>修复前的问题</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <strong>错误信息：</strong><br>
                            <code>TypeError: window.ruleModal.show is not a function</code>
                        </div>
                        
                        <p><strong>问题原因：</strong></p>
                        <ul>
                            <li>模态框初始化时机问题</li>
                            <li>DOM元素可能未完全加载</li>
                            <li>Bootstrap模态框实例创建失败</li>
                        </ul>
                        
                        <p><strong>修复方案：</strong></p>
                        <ul>
                            <li>使用 <code>bootstrap.Modal.getInstance()</code> 检查现有实例</li>
                            <li>动态创建模态框实例</li>
                            <li>增强错误处理</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>修复代码对比</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">修复前：</h6>
                                <pre class="bg-light p-3"><code>// 打开模态框
if (window.ruleModal) {
    window.ruleModal.show();
} else {
    const ruleModal = new bootstrap.Modal(document.getElementById('ruleModal'));
    ruleModal.show();
}</code></pre>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">修复后：</h6>
                                <pre class="bg-light p-3"><code>// 确保模态框元素存在
const modalElement = document.getElementById('ruleModal');
if (!modalElement) {
    throw new Error('找不到规则模态框元素');
}

// 尝试获取现有的模态框实例
let modalInstance = bootstrap.Modal.getInstance(modalElement);

if (!modalInstance) {
    modalInstance = new bootstrap.Modal(modalElement);
    window.ruleModal = modalInstance;
}

modalInstance.show();</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-success">
                    <h5>✅ 修复完成</h5>
                    <p>新增规则和编辑规则的模态框打开功能已修复，现在应该能正常工作了。</p>
                    <p>如果仍有问题，请检查浏览器控制台的错误信息。</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
