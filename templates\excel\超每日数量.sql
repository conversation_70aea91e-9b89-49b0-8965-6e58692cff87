WITH TAB1 AS (
 SELECT
  b.结算单据号,
  b.医保项目编码,
  b.医保项目名称,
  b.医院项目编码,
  b.医院项目名称,

  b.费用类别,
  b.支付类别,
  b.单价,
  B.规格,
    trunc(b.项目使用日期) 项目使用日期,
  sum( b.数量 ) AS 使用数量,
  sum( b.金额 ) AS 使用金额,
  sum( b.医保范围内金额 ) AS 医保范围内总金额
 FROM
  医保住院结算明细 B  
  WHERE
  医保项目名称 = '{医保项目名称}'
 GROUP BY
  b.结算单据号,
  b.医保项目编码,
  b.医保项目名称,
  b.医院项目编码,
  b.医院项目名称,
  trunc(b.项目使用日期),
  b.费用类别,
  b.支付类别,
  b.单价,
  B.规格
 HAVING
  sum( 数量 ) > '{每日数量}'

) 
SELECT
 a.结算单据号,
 a.住院号,
 A.病案号,
 a.个人编码,
 a.患者姓名,
 a.患者性别,
 a.患者社会保障号码,
 a.患者年龄,
 a.险种类型,
 a.入院科室,
 a.入院诊断名称,
 a.入院日期,
 a.出院科室,
 a.出院诊断名称,
 a.出院日期,
 TRUNC(a.出院日期) - TRUNC(a.入院日期) as 住院天数,
 a.结算日期,
 a.大病保险,
 b.医保项目编码,
 b.医保项目名称,
 b.医院项目编码,
 b.医院项目名称,
 b.项目使用日期,
 b.费用类别,
 b.支付类别,
 b.单价,
 B.规格,
 b.使用数量,
 b.使用金额,
 b.医保范围内总金额,
 (b.使用数量 - '{每日数量}') as 超出数量,
 (b.使用数量 - '{每日数量}') * b.单价 as 超出金额
FROM
 TAB1 b
 JOIN 医保住院结算主单 a ON a.结算单据号 = b.结算单据号
 WHERE
 医保项目名称 = '{医保项目名称}'
