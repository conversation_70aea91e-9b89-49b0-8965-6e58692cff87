"""
SQL规则解析器使用示例
"""

import os
import json
from sql_rule_parser import SQLRuleParser


def main():
    """主函数"""
    print("=== SQL规则解析器演示 ===\n")
    
    # 创建解析器实例
    parser = SQLRuleParser()
    
    # 解析样本目录
    sample_dir = "郑州第一附属医院规则"
    
    if not os.path.exists(sample_dir):
        print(f"样本目录不存在: {sample_dir}")
        return
    
    print(f"正在解析目录: {sample_dir}")
    print("=" * 50)
    
    try:
        # 解析所有规则文件
        rules = parser.parse_directory(sample_dir)
        
        print(f"成功解析 {len(rules)} 个规则文件\n")
        
        # 显示前5个规则的详细信息
        print("=== 规则解析结果示例 ===")
        for i, rule in enumerate(rules[:5]):
            print(f"\n规则 {i+1}:")
            print(f"  文件名: {rule.sql_content}")
            print(f"  规则名称: {rule.rule_name}")
            print(f"  规则类型: {rule.rule_type.value}")
            print(f"  城市: {rule.city}")
            print(f"  行为认定: {rule.behavior}")
            print(f"  医保项目: {', '.join(rule.medical_items[:3])}{'...' if len(rule.medical_items) > 3 else ''}")
            
            # 显示条件信息
            if rule.conditions.age_range:
                print(f"  年龄限制: {rule.conditions.age_range}")
            if rule.conditions.gender:
                print(f"  性别限制: {rule.conditions.gender}")
            if rule.conditions.quantity_threshold:
                print(f"  数量阈值: {rule.conditions.quantity_threshold}")
            if rule.conditions.include_diagnoses:
                print(f"  包含诊断: {', '.join(rule.conditions.include_diagnoses[:2])}{'...' if len(rule.conditions.include_diagnoses) > 2 else ''}")
            if rule.conditions.exclude_diagnoses:
                print(f"  排除诊断: {', '.join(rule.conditions.exclude_diagnoses[:2])}{'...' if len(rule.conditions.exclude_diagnoses) > 2 else ''}")
        
        # 统计分析
        print("\n=== 规则模式统计分析 ===")
        stats = parser.analyze_rule_patterns(rules)
        
        print(f"总规则数: {stats['total_rules']}")
        
        print("\n规则类型分布:")
        for rule_type, count in stats['rule_types'].items():
            percentage = (count / stats['total_rules']) * 100
            print(f"  {rule_type}: {count} ({percentage:.1f}%)")
        
        print("\n城市分布:")
        for city, count in sorted(stats['cities'].items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"  {city}: {count}")
        
        print("\n行为认定分布:")
        for behavior, count in sorted(stats['behaviors'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {behavior}: {count}")
        
        print("\n条件类型统计:")
        conditions = stats['common_conditions']
        print(f"  年龄限制: {conditions['age_restrictions']}")
        print(f"  性别限制: {conditions['gender_restrictions']}")
        print(f"  诊断条件: {conditions['diagnosis_conditions']}")
        print(f"  科室条件: {conditions['department_conditions']}")
        print(f"  数量阈值: {conditions['quantity_thresholds']}")
        
        # 导出结构化数据
        print("\n=== 导出结构化数据 ===")
        structured_data = parser.export_to_structured_data(rules)
        
        output_file = "parsed_rules.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(structured_data, f, ensure_ascii=False, indent=2)
        
        print(f"结构化数据已导出到: {output_file}")
        
        # 演示单个文件解析
        print("\n=== 单个文件解析演示 ===")
        sample_files = [f for f in os.listdir(sample_dir) if f.endswith('.sql')][:3]
        
        for filename in sample_files:
            file_path = os.path.join(sample_dir, filename)
            print(f"\n解析文件: {filename}")
            
            try:
                rule_info = parser.parse_file(file_path)
                
                # 获取分类解释
                with open(file_path, 'r', encoding='utf-8') as f:
                    sql_content = f.read()
                
                explanation = parser.explain_rule_classification(sql_content)
                
                print(f"  预测类型: {explanation['predicted_type']}")
                print(f"  置信度: {explanation['confidence']:.2f}")
                print("  分类依据:")
                for exp in explanation['explanations'][:3]:
                    print(f"    - {exp}")
                
                # 验证完整性
                completeness = parser.validate_rule_completeness(rule_info)
                print(f"  信息完整性: {'完整' if completeness['overall_complete'] else '不完整'}")
                
            except Exception as e:
                print(f"  解析失败: {e}")
        
        print("\n=== 解析完成 ===")
        
    except Exception as e:
        print(f"解析过程中出现错误: {e}")


def demo_specific_rule():
    """演示特定规则解析"""
    print("\n=== 特定规则解析演示 ===")
    
    parser = SQLRuleParser()
    
    # 重复收费规则示例
    duplicate_billing_sql = """
    -- ============================================================
    -- 序号: 109
    -- 规则名称: 合肥_住院_109_开展血液灌流，重复收取同时段血液透析费用。  
    -- 城市: 合肥
    -- 规则来源: 安徽省定点医药机构违法违规使用医保基金自查自纠问题清单(2025)
    -- 行为认定: 重复收费
    -- 医保名称1: 血液灌流
    -- 医保名称2(违规项): 血液透析
    -- ============================================================
    WITH tab1 AS (
      SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
      FROM 医保住院结算明细 B
      WHERE B.医保项目名称 IN ('血液灌流')
      INTERSECT
      SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
      FROM 医保住院结算明细 B
      WHERE B.医保项目名称 IN ('血液透析')
    )
    SELECT * FROM 医保住院结算明细 B
    JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
    JOIN tab1 C ON B.结算单据号 = C.结算单据号
    WHERE B.医保项目名称 IN ('血液灌流', '血液透析')
    """
    
    print("解析重复收费规则:")
    rule_info = parser.parse_content(duplicate_billing_sql)
    
    print(f"  规则名称: {rule_info.rule_name}")
    print(f"  规则类型: {rule_info.rule_type.value}")
    print(f"  城市: {rule_info.city}")
    print(f"  行为认定: {rule_info.behavior}")
    print(f"  医保项目: {rule_info.medical_items}")
    print(f"  违规项目: {rule_info.violation_items}")
    print(f"  时间条件: {rule_info.conditions.time_conditions}")
    
    # 获取分类解释
    explanation = parser.explain_rule_classification(duplicate_billing_sql)
    print(f"  分类置信度: {explanation['confidence']:.2f}")
    print("  分类依据:")
    for exp in explanation['explanations']:
        print(f"    - {exp}")


if __name__ == "__main__":
    main()
    demo_specific_rule()
