"""
深度SQL解析器核心模块
基于sqlglot的SQL解析和AST分析
"""

import sqlglot
from sqlglot import expressions as exp
from typing import Optional, Dict, Any, List
import logging

from .models import (
    ParseResult, RuleLogicIR, RuleType, DataSource,
    Condition, FieldReference, FieldType, OperatorType, LogicType,
    DuplicateBillingPattern, Aggregation, AggregationType
)


class DeepSQLParser:
    """深度SQL解析器"""
    
    def __init__(self, dialect: str = "postgres"):
        """
        初始化解析器
        
        Args:
            dialect: SQL方言 (postgres, oracle, mysql, clickhouse等)
        """
        self.dialect = dialect
        self.logger = logging.getLogger(__name__)
        
        # 支持的方言映射
        self.dialect_mapping = {
            "postgres": sqlglot.dialects.Postgres,
            "postgresql": sqlglot.dialects.Postgres,
            "oracle": sqlglot.dialects.Oracle,
            "mysql": sqlglot.dialects.MySQL,
            "clickhouse": sqlglot.dialects.ClickHouse,
            "standard": sqlglot.dialects.Dialect
        }
        
        # 医保相关字段映射
        self.field_type_mapping = {
            "患者年龄": FieldType.AGE,
            "年龄": FieldType.AGE,
            "患者性别": FieldType.GENDER,
            "性别": FieldType.GENDER,
            "出院诊断名称": FieldType.DIAGNOSIS,
            "入院诊断名称": FieldType.DIAGNOSIS,
            "诊断": FieldType.DIAGNOSIS,
            "科室": FieldType.DEPARTMENT,
            "开单科室": FieldType.DEPARTMENT,
            "执行科室": FieldType.DEPARTMENT,
            "医保项目名称": FieldType.MEDICAL_ITEM,
            "医保项目编码": FieldType.MEDICAL_ITEM,
            "项目名称": FieldType.MEDICAL_ITEM,
            "数量": FieldType.QUANTITY,
            "金额": FieldType.AMOUNT,
            "费用": FieldType.AMOUNT,
            "结算单据号": FieldType.SETTLEMENT_ID,
            "单据号": FieldType.SETTLEMENT_ID,
            "项目使用日期": FieldType.DATETIME,
            "使用日期": FieldType.DATETIME,
            "日期": FieldType.DATE
        }
    
    def parse(self, sql: str) -> ParseResult:
        """
        解析SQL语句
        
        Args:
            sql: SQL语句
            
        Returns:
            ParseResult: 解析结果
        """
        try:
            # 预处理SQL
            cleaned_sql = self._preprocess_sql(sql)
            
            # 解析为AST
            ast = self._parse_to_ast(cleaned_sql)
            if not ast:
                return ParseResult(
                    success=False,
                    error_message="SQL解析失败：无法生成AST"
                )
            
            # 分析AST并生成中间表示
            rule_ir = self._analyze_ast(ast, cleaned_sql)
            
            return ParseResult(
                success=True,
                rule_ir=rule_ir
            )
            
        except Exception as e:
            self.logger.error(f"SQL解析失败: {str(e)}")
            return ParseResult(
                success=False,
                error_message=f"解析错误: {str(e)}"
            )
    
    def _preprocess_sql(self, sql: str) -> str:
        """预处理SQL语句"""
        # 移除注释但保留元数据注释
        lines = sql.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if line.startswith('--'):
                # 保留包含元数据的注释
                if any(keyword in line for keyword in ['规则名称', '城市', '行为认定']):
                    cleaned_lines.append(line)
            elif line:
                cleaned_lines.append(line)

        cleaned_sql = '\n'.join(cleaned_lines)

        # 处理ClickHouse特殊语法
        if self.dialect.lower() in ['clickhouse']:
            cleaned_sql = self._preprocess_clickhouse_sql(cleaned_sql)

        return cleaned_sql

    def _preprocess_clickhouse_sql(self, sql: str) -> str:
        """预处理ClickHouse SQL语句"""
        import re

        # 将反引号替换为双引号（更标准的SQL标识符引用方式）
        # 但要小心不要替换字符串中的反引号

        # 简单的替换策略：将 `字段名` 替换为 "字段名"
        # 这个正则表达式匹配反引号包围的标识符
        pattern = r'`([^`]+)`'

        def replace_backticks(match):
            content = match.group(1)
            # 如果内容看起来像字段名（不包含空格或特殊字符），则替换
            if re.match(r'^[a-zA-Z_\u4e00-\u9fff][a-zA-Z0-9_\u4e00-\u9fff]*$', content):
                return f'"{content}"'
            else:
                return match.group(0)  # 保持原样

        return re.sub(pattern, replace_backticks, sql)
    
    def _parse_to_ast(self, sql: str) -> Optional[exp.Expression]:
        """将SQL解析为AST"""
        try:
            # 获取方言类
            dialect_class = self.dialect_mapping.get(self.dialect.lower(), sqlglot.dialects.Dialect)
            
            # 解析SQL
            parsed = sqlglot.parse_one(sql, dialect=dialect_class)
            return parsed
            
        except Exception as e:
            self.logger.error(f"AST解析失败: {str(e)}")
            return None
    
    def _analyze_ast(self, ast: exp.Expression, original_sql: str) -> RuleLogicIR:
        """分析AST并生成中间表示"""
        rule_ir = RuleLogicIR(
            rule_type=RuleType.UNKNOWN,
            original_sql=original_sql
        )

        # 设置当前解析上下文，用于诊断关键字提取
        self._current_rule_ir = rule_ir

        # 分析主查询结构
        if isinstance(ast, exp.Select):
            self._analyze_select_statement(ast, rule_ir)
        elif isinstance(ast, exp.With):
            self._analyze_with_statement(ast, rule_ir)
        elif isinstance(ast, exp.Intersect):
            # 直接处理INTERSECT操作
            self._analyze_intersect_pattern(ast, rule_ir)

        # 初步规则类型推断
        rule_ir.rule_type = self._infer_rule_type(rule_ir)

        # 清理上下文
        self._current_rule_ir = None

        return rule_ir
    
    def _analyze_select_statement(self, select_node: exp.Select, rule_ir: RuleLogicIR):
        """分析SELECT语句"""
        # 首先检查是否包含WITH子句
        with_clause = select_node.find(exp.With)
        if with_clause:
            self._analyze_with_statement(with_clause, rule_ir)

        # 分析SELECT列表中的聚合函数
        self._analyze_select_list(select_node, rule_ir)

        # 分析FROM子句
        if select_node.find(exp.From):
            self._analyze_from_clause(select_node.find(exp.From), rule_ir)

        # 分析JOIN子句
        for join in select_node.find_all(exp.Join):
            self._analyze_join_clause(join, rule_ir)

        # 分析WHERE子句
        if select_node.find(exp.Where):
            self._analyze_where_clause(select_node.find(exp.Where), rule_ir)

        # 分析GROUP BY和HAVING子句
        if select_node.find(exp.Group):
            self._analyze_group_clause(select_node.find(exp.Group), rule_ir)

        # 分析HAVING子句
        having_clause = select_node.find(exp.Having)
        if having_clause:
            self._analyze_having_clause(having_clause, rule_ir)
    
    def _analyze_with_statement(self, with_node: exp.With, rule_ir: RuleLogicIR):
        """分析WITH语句（CTE）"""
        # 分析CTE定义
        cte_info = {}
        for cte in with_node.expressions:
            if isinstance(cte, exp.CTE):
                cte_name = str(cte.alias) if cte.alias else f"cte_{len(cte_info)}"

                # 分析CTE中的查询
                cte_query = cte.this
                if isinstance(cte_query, exp.Intersect):
                    # 直接处理INTERSECT查询
                    self._analyze_intersect_pattern(cte_query, rule_ir)
                elif isinstance(cte_query, exp.Select):
                    # 检查SELECT查询中是否包含INTERSECT
                    if self._has_intersect_operation(cte_query):
                        self._analyze_intersect_pattern(cte_query, rule_ir)
                    else:
                        self._analyze_select_statement(cte_query, rule_ir)

                cte_info[cte_name] = cte

        # 分析主查询
        if with_node.this and isinstance(with_node.this, exp.Select):
            self._analyze_select_statement(with_node.this, rule_ir)

    def _has_intersect_operation(self, node: exp.Expression) -> bool:
        """检查是否包含INTERSECT操作"""
        # 检查当前节点是否为INTERSECT
        if isinstance(node, exp.Intersect):
            return True

        # 递归检查所有子节点
        for intersect in node.find_all(exp.Intersect):
            return True

        return False

    def _analyze_intersect_pattern(self, select_node: exp.Expression, rule_ir: RuleLogicIR):
        """分析INTERSECT模式（重复收费检测）"""
        if isinstance(select_node, exp.Intersect):
            # 这是一个INTERSECT操作
            left_query = select_node.left
            right_query = select_node.right

            # 提取左侧查询的医保项目
            left_items = self._extract_medical_items_from_query(left_query)
            # 提取右侧查询的医保项目
            right_items = self._extract_medical_items_from_query(right_query)

            # 创建重复收费模式
            if left_items or right_items:
                rule_ir.duplicate_pattern = DuplicateBillingPattern(
                    primary_items=left_items,
                    conflict_items=right_items,
                    time_precision=self._extract_time_precision_from_sql(rule_ir.original_sql),
                    detection_method="intersect_operation"
                )

                # 更新规则类型
                rule_ir.rule_type = RuleType.DUPLICATE_BILLING

        # 继续分析其他部分
        if hasattr(select_node, 'left') and select_node.left:
            if isinstance(select_node.left, exp.Select):
                self._analyze_select_statement(select_node.left, rule_ir)
        if hasattr(select_node, 'right') and select_node.right:
            if isinstance(select_node.right, exp.Select):
                self._analyze_select_statement(select_node.right, rule_ir)

    def _extract_medical_items_from_query(self, query: exp.Expression) -> List[str]:
        """从查询中提取医保项目名称"""
        items = []

        if isinstance(query, exp.Select):
            # 查找WHERE子句中的医保项目条件
            where_clause = query.find(exp.Where)
            if where_clause:
                items.extend(self._extract_medical_items_from_conditions(where_clause.this))

        return items

    def _extract_medical_items_from_conditions(self, expr: exp.Expression) -> List[str]:
        """从条件表达式中提取医保项目名称"""
        items = []

        if isinstance(expr, exp.EQ):
            # 检查是否为医保项目相关条件
            if (isinstance(expr.left, exp.Column) and
                str(expr.left.name) in ["医保项目名称", "医保项目编码"] and
                isinstance(expr.right, exp.Literal)):
                items.append(str(expr.right.this))
        elif isinstance(expr, exp.In):
            # 处理 IN 条件
            if (isinstance(expr.this, exp.Column) and
                str(expr.this.name) in ["医保项目名称", "医保项目编码"]):
                # 处理IN子句中的值列表
                for value_expr in expr.expressions:
                    if isinstance(value_expr, exp.Tuple):
                        # 处理元组形式: IN (value1, value2, ...)
                        for item in value_expr.expressions:
                            if isinstance(item, exp.Literal):
                                items.append(str(item.this))
                    elif isinstance(value_expr, exp.Literal):
                        # 处理单个值形式: IN ('value')
                        items.append(str(value_expr.this))
        elif isinstance(expr, exp.And):
            # 递归处理AND条件
            if expr.left:
                items.extend(self._extract_medical_items_from_conditions(expr.left))
            if expr.right:
                items.extend(self._extract_medical_items_from_conditions(expr.right))
        elif isinstance(expr, exp.Or):
            # 递归处理OR条件
            if expr.left:
                items.extend(self._extract_medical_items_from_conditions(expr.left))
            if expr.right:
                items.extend(self._extract_medical_items_from_conditions(expr.right))

        return items

    def _extract_time_precision(self, expr: exp.Expression) -> str:
        """提取时间精度信息"""
        # 在原始SQL中查找时间格式
        original_sql = str(expr).lower()
        if "hh24" in original_sql or "hh" in original_sql:
            return "hour"
        elif "mi" in original_sql:
            return "minute"

        # 查找时间格式化函数
        for func in expr.find_all(exp.Anonymous):
            if str(func.this).lower() == "to_char":
                if len(func.expressions) >= 2:
                    format_str = str(func.expressions[1])
                    if "hh24" in format_str.lower():
                        return "hour"
                    elif "mi" in format_str.lower():
                        return "minute"
                    else:
                        return "day"

        # 查找ToChar函数
        for func in expr.find_all(exp.ToChar):
            # 检查原始表达式字符串
            func_str = str(func).lower()
            if "hh24" in func_str or "hh" in func_str:
                return "hour"
            elif "mi" in func_str:
                return "minute"

        return "day"  # 默认按天

    def _extract_time_precision_from_sql(self, sql: str) -> str:
        """从原始SQL中提取时间精度信息"""
        sql_lower = sql.lower()
        if "hh24" in sql_lower or "hh" in sql_lower:
            return "hour"
        elif "mi" in sql_lower:
            return "minute"
        return "day"
    
    def _analyze_from_clause(self, from_node: exp.From, rule_ir: RuleLogicIR):
        """分析FROM子句"""
        if isinstance(from_node.this, exp.Table):
            table_name = str(from_node.this.name)
            alias = str(from_node.this.alias) if from_node.this.alias else table_name
            
            data_source = DataSource(
                table_name=table_name,
                alias=alias
            )
            rule_ir.data_sources.append(data_source)
    
    def _analyze_join_clause(self, join_node: exp.Join, rule_ir: RuleLogicIR):
        """分析JOIN子句"""
        if isinstance(join_node.this, exp.Table):
            table_name = str(join_node.this.name)
            alias = str(join_node.this.alias) if join_node.this.alias else table_name
            
            data_source = DataSource(
                table_name=table_name,
                alias=alias,
                join_type=join_node.kind or "INNER"
            )
            
            # 分析JOIN条件
            if join_node.on:
                join_conditions = self._extract_conditions_from_expression(join_node.on)
                data_source.join_conditions = join_conditions
            
            rule_ir.data_sources.append(data_source)
    
    def _analyze_where_clause(self, where_node: exp.Where, rule_ir: RuleLogicIR):
        """分析WHERE子句"""
        self.logger.info(f"分析WHERE子句: {where_node.this}")
        self.logger.info(f"WHERE子句类型: {type(where_node.this)}")
        conditions = self._extract_conditions_from_expression(where_node.this)
        self.logger.info(f"提取到 {len(conditions)} 个条件")
        rule_ir.conditions.extend(conditions)
    
    def _analyze_group_clause(self, group_node: exp.Group, rule_ir: RuleLogicIR):
        """分析GROUP BY子句"""
        # GROUP BY子句主要用于分组，这里暂时不需要特殊处理
        pass

    def _analyze_having_clause(self, having_node: exp.Having, rule_ir: RuleLogicIR):
        """分析HAVING子句"""
        if having_node.this:
            having_conditions = self._extract_conditions_from_expression(having_node.this)

            # 检查是否为数量阈值条件（只在规则类型未知时设置）
            if rule_ir.rule_type == RuleType.UNKNOWN:
                for condition in having_conditions:
                    if (condition.field.field_type == FieldType.QUANTITY and
                        condition.operator in [OperatorType.GREATER_THAN, OperatorType.GREATER_EQUAL]):
                        # 这可能是超量使用规则
                        rule_ir.rule_type = RuleType.EXCESSIVE_USAGE
                        break
                    elif ('SUM(' in condition.field.field_name and '数量' in condition.field.field_name and
                          condition.operator in [OperatorType.GREATER_THAN, OperatorType.GREATER_EQUAL]):
                        # SUM(数量) > 阈值 也是超量使用规则
                        rule_ir.rule_type = RuleType.EXCESSIVE_USAGE
                        break

            rule_ir.conditions.extend(having_conditions)

    def _analyze_select_list(self, select_node: exp.Select, rule_ir: RuleLogicIR):
        """分析SELECT列表中的聚合函数"""
        if not select_node.expressions:
            return

        for expr in select_node.expressions:
            if isinstance(expr, exp.Alias):
                # 处理别名表达式
                actual_expr = expr.this
                alias_name = str(expr.alias) if expr.alias else None
            else:
                actual_expr = expr
                alias_name = None

            # 检查是否为聚合函数
            aggregation = self._extract_aggregation_from_expression(actual_expr, alias_name)
            if aggregation:
                rule_ir.aggregations.append(aggregation)

    def _extract_aggregation_from_expression(self, expr: exp.Expression, alias: Optional[str] = None) -> Optional[Aggregation]:
        """从表达式中提取聚合函数"""
        from .models import Aggregation, AggregationType

        if isinstance(expr, exp.Sum):
            # SUM函数
            if expr.this and isinstance(expr.this, exp.Column):
                field_ref = self._extract_field_reference(expr.this)
                if field_ref:
                    return Aggregation(
                        function=AggregationType.SUM,
                        field=field_ref,
                        alias=alias
                    )
        elif isinstance(expr, exp.Count):
            # COUNT函数
            if expr.this:
                if isinstance(expr.this, exp.Column):
                    field_ref = self._extract_field_reference(expr.this)
                elif isinstance(expr.this, exp.Star):
                    # COUNT(*) 的情况
                    field_ref = FieldReference(
                        table_alias="",
                        field_name="*",
                        field_type=FieldType.OTHER,
                        original_expression="*"
                    )
                else:
                    field_ref = None

                if field_ref:
                    return Aggregation(
                        function=AggregationType.COUNT,
                        field=field_ref,
                        alias=alias
                    )
        elif isinstance(expr, exp.Avg):
            # AVG函数
            if expr.this and isinstance(expr.this, exp.Column):
                field_ref = self._extract_field_reference(expr.this)
                if field_ref:
                    return Aggregation(
                        function=AggregationType.AVG,
                        field=field_ref,
                        alias=alias
                    )
        elif isinstance(expr, exp.Max):
            # MAX函数
            if expr.this and isinstance(expr.this, exp.Column):
                field_ref = self._extract_field_reference(expr.this)
                if field_ref:
                    return Aggregation(
                        function=AggregationType.MAX,
                        field=field_ref,
                        alias=alias
                    )
        elif isinstance(expr, exp.Min):
            # MIN函数
            if expr.this and isinstance(expr.this, exp.Column):
                field_ref = self._extract_field_reference(expr.this)
                if field_ref:
                    return Aggregation(
                        function=AggregationType.MIN,
                        field=field_ref,
                        alias=alias
                    )

        return None
    
    def _extract_conditions_from_expression(self, expr: exp.Expression) -> List[Condition]:
        """从表达式中提取条件"""
        conditions = []

        self.logger.info(f"提取条件，表达式类型: {type(expr)}")
        self.logger.info(f"表达式内容: {expr}")

        # 注意：必须先检查具体类型（And、Or），再检查通用类型（Binary）
        # 因为And和Or都是Binary的子类
        if isinstance(expr, exp.And):
            # 递归处理AND条件
            self.logger.info("处理AND条件")
            if hasattr(expr, 'left') and expr.left:
                conditions.extend(self._extract_conditions_from_expression(expr.left))
            if hasattr(expr, 'right') and expr.right:
                conditions.extend(self._extract_conditions_from_expression(expr.right))
        elif isinstance(expr, exp.Or):
            # 递归处理OR条件
            self.logger.info("处理OR条件")
            if hasattr(expr, 'left') and expr.left:
                sub_conditions = self._extract_conditions_from_expression(expr.left)
                for cond in sub_conditions:
                    cond.logic_type = LogicType.OR
                conditions.extend(sub_conditions)
            if hasattr(expr, 'right') and expr.right:
                sub_conditions = self._extract_conditions_from_expression(expr.right)
                for cond in sub_conditions:
                    cond.logic_type = LogicType.OR
                conditions.extend(sub_conditions)
        elif isinstance(expr, exp.Not):
            # 处理NOT条件
            self.logger.info("处理NOT条件")
            negated_expr = expr.this
            if negated_expr:
                # 递归处理被否定的表达式
                negated_conditions = self._extract_conditions_from_expression(negated_expr)
                # 将每个条件的操作符转换为否定形式
                for condition in negated_conditions:
                    if condition.operator == OperatorType.IN:
                        condition.operator = OperatorType.NOT_IN
                    elif condition.operator == OperatorType.EQUAL:
                        condition.operator = OperatorType.NOT_EQUAL
                    elif condition.operator == OperatorType.LIKE:
                        condition.operator = OperatorType.NOT_LIKE
                    # 可以添加更多操作符的否定形式
                conditions.extend(negated_conditions)
        elif isinstance(expr, exp.Paren):
            # 处理括号表达式
            self.logger.info("处理括号表达式")
            inner_expr = expr.this
            if inner_expr:
                # 递归处理括号内的表达式
                inner_conditions = self._extract_conditions_from_expression(inner_expr)
                conditions.extend(inner_conditions)
        elif isinstance(expr, exp.In):
            # 处理IN条件
            self.logger.info("处理IN条件")
            condition = self._parse_in_condition(expr)
            if condition:
                self.logger.info(f"成功解析IN条件: {condition.field.field_name} {condition.operator.value} {condition.value}")
                conditions.append(condition)
            else:
                self.logger.warning("IN条件解析失败")
        elif isinstance(expr, exp.Binary):
            # 处理其他二元表达式（比较操作符等）
            self.logger.info("处理二元条件")
            condition = self._parse_binary_condition(expr)
            if condition:
                self.logger.info(f"成功解析二元条件: {condition.field.field_name} {condition.operator.value} {condition.value}")
                conditions.append(condition)
            else:
                self.logger.warning("二元条件解析失败")
        else:
            self.logger.warning(f"未知表达式类型: {type(expr)}")

        self.logger.info(f"提取到 {len(conditions)} 个条件")
        return conditions
    
    def _parse_binary_condition(self, binary_expr: exp.Binary) -> Optional[Condition]:
        """解析二元条件表达式"""
        try:
            # 检查左侧是否为聚合函数
            if isinstance(binary_expr.left, (exp.Sum, exp.Count, exp.Avg, exp.Max, exp.Min)):
                # 处理聚合函数条件，如 SUM(数量) > 2
                field_ref = self._extract_field_reference_from_aggregation(binary_expr.left)
                if not field_ref:
                    return None
            else:
                # 获取左侧字段
                field_ref = self._extract_field_reference(binary_expr.left)
                if not field_ref:
                    return None

            # 特殊处理STR_POSITION函数的诊断关键字提取
            if isinstance(binary_expr.left, exp.Anonymous):
                func_name = str(binary_expr.left.this).upper()
                if func_name == "STR_POSITION" and len(binary_expr.left.expressions) >= 2:
                    # 提取诊断关键字
                    keyword_expr = binary_expr.left.expressions[1]
                    if isinstance(keyword_expr, exp.Literal):
                        keyword = str(keyword_expr.this)
                        # 将关键字添加到当前解析上下文中
                        if hasattr(self, '_current_rule_ir') and self._current_rule_ir:
                            if keyword not in self._current_rule_ir.diagnosis_keywords:
                                self._current_rule_ir.diagnosis_keywords.append(keyword)
                                self.logger.info(f"提取到诊断关键字: {keyword}")

            # 获取操作符
            operator = self._map_operator(binary_expr)
            if not operator:
                return None

            # 获取右侧值
            value = self._extract_value(binary_expr.right)

            return Condition(
                field=field_ref,
                operator=operator,
                value=value
            )

        except Exception as e:
            self.logger.warning(f"解析二元条件失败: {str(e)}")
            self.logger.warning(f"二元表达式: {binary_expr}")
            self.logger.warning(f"左侧表达式类型: {type(binary_expr.left)}")
            self.logger.warning(f"左侧表达式内容: {binary_expr.left}")
            return None

    def _parse_in_condition(self, in_expr: exp.In) -> Optional[Condition]:
        """解析IN条件表达式"""
        try:
            # 获取左侧字段
            field_ref = self._extract_field_reference(in_expr.this)
            if not field_ref:
                return None

            # 获取IN列表中的值
            values = []
            if hasattr(in_expr, 'expressions') and in_expr.expressions:
                for expr in in_expr.expressions:
                    value = self._extract_value(expr)
                    if value is not None:
                        values.append(value)

            if not values:
                return None

            return Condition(
                field=field_ref,
                operator=OperatorType.IN,
                value=values  # IN条件的值是一个列表
            )

        except Exception as e:
            self.logger.warning(f"解析IN条件失败: {str(e)}")
            return None

    def _extract_field_reference_from_aggregation(self, agg_expr: exp.Expression) -> Optional[FieldReference]:
        """从聚合函数中提取字段引用"""
        if hasattr(agg_expr, 'this') and isinstance(agg_expr.this, exp.Column):
            # 获取聚合函数内的字段
            inner_field = self._extract_field_reference(agg_expr.this)
            if inner_field:
                # 创建一个表示聚合结果的字段引用
                return FieldReference(
                    table_alias=inner_field.table_alias,
                    field_name=f"{type(agg_expr).__name__.upper()}({inner_field.field_name})",
                    field_type=inner_field.field_type,
                    original_expression=str(agg_expr)
                )
        return None
    
    def _extract_field_reference(self, expr: exp.Expression) -> Optional[FieldReference]:
        """提取字段引用"""
        if isinstance(expr, exp.Column):
            # 获取表别名
            table_alias = ""
            if expr.table:
                table_alias = str(expr.table)

            # 获取字段名
            field_name = str(expr.name) if expr.name else str(expr.this)

            # 清理字段名（移除引号和表前缀）
            clean_field_name = field_name
            if '.' in clean_field_name:
                clean_field_name = clean_field_name.split('.')[-1]

            # 移除引号
            if clean_field_name.startswith('"') and clean_field_name.endswith('"'):
                clean_field_name = clean_field_name[1:-1]
            if clean_field_name.startswith('`') and clean_field_name.endswith('`'):
                clean_field_name = clean_field_name[1:-1]

            # 映射字段类型
            field_type = self.field_type_mapping.get(clean_field_name, FieldType.OTHER)

            return FieldReference(
                table_alias=table_alias,
                field_name=clean_field_name,  # 使用清理后的字段名
                field_type=field_type,
                original_expression=str(expr)
            )
        elif isinstance(expr, exp.Anonymous):
            # 处理函数调用，如 STR_POSITION(a.出院诊断名称, '癌')
            func_name = str(expr.this).upper()
            if func_name == "STR_POSITION" and len(expr.expressions) >= 2:
                # 提取第一个参数作为字段引用
                field_expr = expr.expressions[0]
                if isinstance(field_expr, exp.Column):
                    field_ref = self._extract_field_reference(field_expr)
                    if field_ref:
                        # 修改字段名以表示这是一个函数调用
                        field_ref.original_expression = str(expr)
                        return field_ref

        return None
    
    def _map_operator(self, binary_expr: exp.Binary) -> Optional[OperatorType]:
        """映射操作符"""
        operator_mapping = {
            exp.EQ: OperatorType.EQUAL,
            exp.NEQ: OperatorType.NOT_EQUAL,
            exp.GT: OperatorType.GREATER_THAN,
            exp.LT: OperatorType.LESS_THAN,
            exp.GTE: OperatorType.GREATER_EQUAL,
            exp.LTE: OperatorType.LESS_EQUAL,
            exp.In: OperatorType.IN,
            exp.Like: OperatorType.LIKE,
            exp.ILike: OperatorType.LIKE,  # PostgreSQL的ILIKE
        }

        return operator_mapping.get(type(binary_expr))
    
    def _extract_value(self, expr: exp.Expression) -> Any:
        """提取值"""
        if isinstance(expr, exp.Literal):
            return expr.this
        elif isinstance(expr, exp.Tuple):
            return [self._extract_value(item) for item in expr.expressions]
        else:
            return str(expr)
    
    def _infer_rule_type(self, rule_ir: RuleLogicIR) -> RuleType:
        """推断规则类型"""
        # 如果已经设置了规则类型，直接返回
        if rule_ir.rule_type != RuleType.UNKNOWN:
            return rule_ir.rule_type

        # 优先级1：检查是否有聚合函数和HAVING条件（超量使用规则）
        if rule_ir.aggregations:
            for condition in rule_ir.conditions:
                if ('SUM(' in condition.field.field_name and
                    condition.operator in [OperatorType.GREATER_THAN, OperatorType.GREATER_EQUAL]):
                    return RuleType.EXCESSIVE_USAGE

        # 优先级2：检查是否有重复收费模式
        if rule_ir.duplicate_pattern:
            return RuleType.DUPLICATE_BILLING

        # 优先级3：检查特定字段类型的条件
        age_conditions = 0
        gender_conditions = 0

        for condition in rule_ir.conditions:
            if condition.field.field_type == FieldType.AGE:
                age_conditions += 1
            elif condition.field.field_type == FieldType.GENDER:
                gender_conditions += 1

        # 如果主要是年龄条件，判断为年龄限制
        if age_conditions > 0 and age_conditions >= gender_conditions:
            return RuleType.AGE_RESTRICTION

        # 如果主要是性别条件，判断为性别限制
        if gender_conditions > 0:
            return RuleType.GENDER_RESTRICTION

        # 优先级4：检查是否为病案提取规则
        # 病案提取规则的特征：
        # 1. 没有聚合函数（或聚合函数很少）
        # 2. 条件相对简单，主要用于筛选特定项目
        # 3. 有医保项目相关的条件（IN、LIKE、等值条件）
        if len(rule_ir.aggregations) <= 1 and not rule_ir.duplicate_pattern:
            # 检查是否有医保项目相关的筛选条件
            for condition in rule_ir.conditions:
                if condition.field.field_type == FieldType.MEDICAL_ITEM:
                    # IN条件：筛选多个特定编码/名称
                    if (condition.operator == OperatorType.IN and
                        isinstance(condition.value, list) and
                        len(condition.value) >= 1):
                        return RuleType.CASE_EXTRACTION

                    # LIKE条件：模糊匹配特定编码/名称
                    if condition.operator == OperatorType.LIKE:
                        return RuleType.CASE_EXTRACTION

                    # 等值条件：精确匹配特定编码/名称
                    if condition.operator == OperatorType.EQUAL:
                        return RuleType.CASE_EXTRACTION

            # 如果没有聚合函数，且条件数量较少（通常是简单筛选），也可能是病案提取
            if len(rule_ir.aggregations) == 0 and len(rule_ir.conditions) <= 3:
                return RuleType.CASE_EXTRACTION

        # 优先级5：基于数据源数量进行推断（多表关联可能是重复收费）
        if len(rule_ir.data_sources) >= 3:  # 提高阈值，避免误判
            return RuleType.DUPLICATE_BILLING

        return RuleType.UNKNOWN
