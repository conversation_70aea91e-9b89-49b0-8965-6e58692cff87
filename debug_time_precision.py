"""
调试时间精度检测问题
"""

import sqlglot
from sqlglot import expressions as exp

sql = """
SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
FROM 医保住院结算明细 WHERE 医保项目名称 = 'A'
"""

# 解析SQL
ast = sqlglot.parse_one(sql)
print("AST结构:")
print(ast)

# 查找所有函数调用
functions = list(ast.find_all(exp.Anonymous))
print(f"\n找到的Anonymous函数数量: {len(functions)}")

for i, func in enumerate(functions):
    print(f"函数 {i+1}:")
    print(f"  类型: {type(func)}")
    print(f"  this: {func.this}")
    print(f"  expressions: {func.expressions}")
    if func.expressions:
        for j, expr in enumerate(func.expressions):
            print(f"    参数 {j+1}: {expr} (类型: {type(expr)})")

# 查找其他类型的函数
other_funcs = list(ast.find_all(exp.Func))
print(f"\n找到的Func函数数量: {len(other_funcs)}")

for i, func in enumerate(other_funcs):
    print(f"函数 {i+1}:")
    print(f"  类型: {type(func)}")
    print(f"  内容: {func}")

# 查找所有表达式类型
all_expressions = list(ast.find_all(exp.Expression))
func_expressions = [e for e in all_expressions if 'to_char' in str(e).lower()]
print(f"\n包含to_char的表达式: {len(func_expressions)}")
for expr in func_expressions:
    print(f"  {type(expr)}: {expr}")
