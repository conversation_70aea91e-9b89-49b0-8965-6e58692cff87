"""
测试简单的病案提取规则识别
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_simple_like_condition():
    """测试LIKE条件的病案提取"""
    print("=== 测试LIKE条件病案提取 ===")
    
    sql_content = """
    -- 规则名称: 血小板减少症用药限制
    -- 城市: 测试城市
    -- 行为认定: 病案提取
    SELECT A.`病案号`, A.`结算单据号`, B.`医保项目编码`, B.`医保项目名称`
    FROM ZZS_YB_ZDYFY_9LY.`医保住院结算明细` B
    JOIN ZZS_YB_ZDYFY_9LY.`医保住院结算主单` A ON A.`结算单据号` = B.`结算单据号`
    WHERE B.`医保项目编码` like '%XB02BXC117B002010101313%'
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"规则类型: {rule_info.get('rule_type', 'N/A')}")
            print(f"条件数量: {deep_analysis.get('conditions_count', 'N/A')}")
            print(f"医保项目: {rule_info.get('medical_items', [])}")
            
            # 验证是否识别为病案提取
            if rule_info.get('rule_type') == '病案提取':
                print("✅ 正确识别为病案提取")
                return True
            else:
                print(f"❌ 规则类型识别错误: {rule_info.get('rule_type')}")
                return False
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


def test_simple_equal_condition():
    """测试等值条件的病案提取"""
    print("\n=== 测试等值条件病案提取 ===")
    
    sql_content = """
    -- 规则名称: 特定药物病案提取
    -- 城市: 测试城市
    -- 行为认定: 病案提取
    SELECT A.`病案号`, A.`结算单据号`, B.`医保项目名称`
    FROM ZZS_YB_ZDYFY_9LY.`医保门诊结算明细` B
    JOIN ZZS_YB_ZDYFY_9LY.`医保门诊结算主单` A ON A.`结算单据号` = B.`结算单据号`
    WHERE B.`医保项目名称` = '注射用福沙匹坦双葡甲胺'
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"规则类型: {rule_info.get('rule_type', 'N/A')}")
            print(f"条件数量: {deep_analysis.get('conditions_count', 'N/A')}")
            print(f"医保项目: {rule_info.get('medical_items', [])}")
            
            # 验证是否识别为病案提取
            if rule_info.get('rule_type') == '病案提取':
                print("✅ 正确识别为病案提取")
                return True
            else:
                print(f"❌ 规则类型识别错误: {rule_info.get('rule_type')}")
                return False
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


def test_simple_in_condition():
    """测试IN条件的病案提取"""
    print("\n=== 测试IN条件病案提取 ===")
    
    sql_content = """
    -- 规则名称: 多个编码病案提取
    -- 城市: 测试城市
    -- 行为认定: 病案提取
    SELECT A.`病案号`, A.`结算单据号`, B.`医保项目编码`
    FROM ZZS_YB_ZDYFY_9LY.`医保门诊结算明细` B
    JOIN ZZS_YB_ZDYFY_9LY.`医保门诊结算主单` A ON A.`结算单据号` = B.`结算单据号`
    WHERE B.`医保项目编码` IN ('CODE1', 'CODE2', 'CODE3')
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"规则类型: {rule_info.get('rule_type', 'N/A')}")
            print(f"条件数量: {deep_analysis.get('conditions_count', 'N/A')}")
            print(f"医保项目: {rule_info.get('medical_items', [])}")
            
            # 验证是否识别为病案提取
            if rule_info.get('rule_type') == '病案提取':
                print("✅ 正确识别为病案提取")
                return True
            else:
                print(f"❌ 规则类型识别错误: {rule_info.get('rule_type')}")
                return False
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


if __name__ == "__main__":
    print("开始测试简单病案提取规则识别...\n")
    
    tests = [
        ("LIKE条件病案提取", test_simple_like_condition),
        ("等值条件病案提取", test_simple_equal_condition),
        ("IN条件病案提取", test_simple_in_condition)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有简单病案提取规则都能正确识别！")
        print("\n📋 验证的规则类型:")
        print("✅ LIKE条件筛选特定编码")
        print("✅ 等值条件筛选特定名称")
        print("✅ IN条件筛选多个编码")
    else:
        print("⚠️  仍有问题需要解决。")
