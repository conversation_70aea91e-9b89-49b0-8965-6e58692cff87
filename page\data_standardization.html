<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据标准化</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
        }
        nav {
            margin-bottom: 2rem;
        }

        nav a {
            color: var(--primary);
            text-decoration: none;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: var(--transition);
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav a:hover {
            background-color: rgba(0, 120, 212, 0.1);
        }
        .upload-area {
            border: 2px dashed #2980b9;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
        }
        .upload-area:hover {
            background-color: #f0f8ff;
        }
        .file-info {
            margin-top: 10px;
        }
        .button {
            padding: 10px 15px;
            background-color: #2980b9;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        .button:hover {
            background-color: #3498db;
        }
        table {
            width: 70%; /* 设置表格宽度为70% */
            border-collapse: collapse;
            margin-top: 20px;
            table-layout: fixed; /* 使用固定布局以保持一致的列宽 */
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            word-wrap: break-word; /* 允许长单词换行 */
            white-space: normal; /* 允许单元格内文本换行 */
            height: auto; /* 允许行高根据内容调整 */
            min-height: 30px; /* 设置行的最小高度 */
        }
        th {
            background-color: #f2f2f2;
        }
        .editable {
            background-color: #fff;
        }
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: pointer;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 120px;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%; /* 在工具提示上方定位 */
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        .loading {
            display: none;
            margin-top: 20px;
        }
        .tabs {
            display: flex;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px;
            border: 1px solid #ddd;
            margin-right: 5px;
            background-color: #f2f2f2;
        }
        .tab.active {
            background-color: #2980b9;
            color: white;
        }
        .split-view {
            display: flex;
        }
        .left-column {
            width: 65%; /* 设置左侧列宽度为70% */
            padding: 10px;
            border-right: 1px solid #ddd;
        }
        .right-column {
            width: 35%; /* 设置右侧列宽度为30% */
            padding: 10px;
        }
        .search-field {
            margin-bottom: 10px;
        }
        .sheet-select {
            margin-bottom: 20px;
        }
        .target-table-select {
            margin-bottom: 20px;
            align-items: center;
        }       
        .db-selection {
            margin-bottom: 15px;
        }
        
        .db-select {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .field-list {
            border: 1px solid #ddd;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .field-item {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .field-item:hover {
            background-color: #f5f5f5;
        }
        
        .field-item .field-name {
            font-weight: bold;
        }
        
        .field-item .field-type {
            color: #666;
            font-size: 12px;
        }
        
        .search-field {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .database-connection,
        .database-tables,
        .field-mapping {
            margin: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
        }

        .field-mapping {
            display: flex;
            gap: 20px;
        }

        .current-sheet,
        .database-fields {
            flex: 1;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 8px;
            border: 1px solid #ddd;
        }

        tr.selected {
            background-color: #e6f3ff;
        }

        tr:hover {
            background-color: #f5f5f5;
            cursor: pointer;
        }

        .db-connection {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .form-group input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .button {
            background-color: #2980b9;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .button:hover {
            background-color: #3498db;
        }

        .field-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .field-info {
            display: flex;
            gap: 10px;
            color: #666;
            font-size: 0.9em;
        }

        .field-type {
            color: #2980b9;
        }

        .field-comment {
            color: #7f8c8d;
        }

        .search-field {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .db-select {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .connection-status {
            display: flex;
            align-items: center;
            margin-top: 10px;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .status-text {
            margin-right: 10px;
            color: #28a745;
            font-weight: bold;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }

        .status-indicator.connected {
            background-color: #28a745;
            box-shadow: 0 0 5px #28a745;
        }

        .tables-section {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
        }

        .tables-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .search-input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }

        .tables-container {
            margin-top: 10px;
        }

        .db-select {
            width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
        }

        .db-select option {
            padding: 8px;
            cursor: pointer;
        }

        .db-select option:hover {
            background-color: #f0f0f0;
        }

        .button {
            background-color: #2980b9;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .button.connected {
            background-color: #dc3545;
        }

        .button:hover {
            background-color: #3498db;
        }

        .button.connected:hover {
            background-color: #c82333;
        }

        .target-table-selection {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .field-structure-table {
            width: 100%;
            margin-top: 20px;
            border-collapse: collapse;
        }

        .field-structure-table th,
        .field-structure-table td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: left;
        }

        .field-structure-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .field-structure-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .field-structure-table tr:hover {
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <nav>
        <a href="{{ url_for('index') }}">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M9.78 12.78a.75.75 0 01-1.06 0L4.47 8.53a.75.75 0 010-1.06l4.25-4.25a.75.75 0 011.06 1.06L6.06 8l3.72 3.72a.75.75 0 010 1.06z"/>
            </svg>
            返回主页
        </a>
    </nav>
        <h2>数据标准化</h2>
        <div class="upload-area" id="upload-area">
            拖拽文件到此处或点击上传标准Excel文档
            <input type="file" id="file-input" style="display: none;" accept=".xlsx, .xls">
        </div>
        <div class="file-info" id="file-info"></div>
        <button class="button" id="remove-file" style="display: none;">移除文件</button>

        <div class="sheet-select" id="sheet-select" style="display: none;">
            <label for="sheet-dropdown">选择来源表:</label>
            <select id="sheet-dropdown"></select>
            <label for="target-table-select">选择创建表:</label>

            <select  class="target-table-select" id="target-table-select">
                <option value="">请选择...</option>
                <option value="settlement_zy_main">医保住院结算主单</option>
                <option value="settlement_mz_main">医保门诊结算主单</option>
                <option value="settlement_zy_detail">医保住院结算明细</option>
                <option value="settlement_mz_detail">医保门诊结算明细</option>
            </select>
        </div>
        <div class="split-view" style="display: none;">
            <div class="left-column" id="sheet-content">
                <div style="display: flex; justify-content: space-between; align-items: center;"> <!-- Flex container for heading and button -->
                    <h3>当前工作表内容</h3>
                    <button class="button" id="auto-match">自动匹配</button> <!-- Move button here -->
                </div>
                <table id="current-sheet-table" style="width: 100%;"> <!-- 设置表格宽度为100% -->
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>字段</th>
                            <th>名称</th>
                            <th>数据类型</th>
                            <th>主键</th>
                            <th>非空</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 行将在此动态添加 -->
                    </tbody>
                </table>
                <div style="text-align: right; margin-top: 10px;"> <!-- Align button to the right -->
                    <button class="button" id="auto-match">自动匹配</button>
                </div>
            </div>

            <div class="right-column">

            
                    <div id="table-structure-display" style="display: none;">
                        <h3>数据库表字段结构</h3>
                        <table class="field-structure-table">
                            <thead>
                                <tr>
                                    <th>字段名</th>
                                    <th>类型</th>
                                    <th>必填</th>
                                    <th>主键</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody id="field-structure-body">
                                <!-- 字段结构将通过 JavaScript 动态填充 -->
                            </tbody>
                        </table>
                    </div>
           
            </div>
    </div>

        <div class="export-options" style="display: none;">
            <h3>导出选项</h3>
            <label for="export-method">选择导出方式:</label>
            <select id="export-method">
                <option value="csv">CSV导出</option>
                <option value="data-pump">数据泵导出</option>
            </select>
            <div id="date-range" style="display: none;">
                <label for="start-date">开始日期:</label>
                <input type="date" id="start-date">
                <label for="end-date">结束日期:</label>
                <input type="date" id="end-date">
            </div>
            <div class="tooltip">?
                <span class="tooltiptext">CSV导出适用于选择的日期范围，数据泵导出为完整数据。</span>
            </div>
            <button class="button" id="export-data">导出数据</button>
            <div class="loading" id="loading">正在导出数据，请稍候...</div>
        </div>

    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js"></script> <!-- 引入XLSX库 -->
    <script>
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('file-input');
        const fileInfo = document.getElementById('file-info');
        const removeFileButton = document.getElementById('remove-file');
        const sheetSelect = document.getElementById('sheet-select');
        const sheetDropdown = document.getElementById('sheet-dropdown');
        const splitView = document.querySelector('.split-view');
        const currentSheetTable = document.getElementById('current-sheet-table');
        const systemFieldsList = document.getElementById('system-fields-list');
        const autoMatchButton = document.getElementById('auto-match');
        const exportOptions = document.querySelector('.export-options');
        const exportMethod = document.getElementById('export-method');
        const dateRange = document.getElementById('date-range');
        const loadingIndicator = document.getElementById('loading');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#e0e0e0';
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.backgroundColor = '';
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.backgroundColor = '';
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFileUpload(file);
            }
        });

        function handleFileUpload(file) {
            fileInfo.innerText = `已上传文件: ${file.name}`;
            removeFileButton.style.display = 'block';
            splitView.style.display = 'flex';
            exportOptions.style.display = 'block';
            sheetSelect.style.display = 'block'; // 显示工作表选择下拉框

            const reader = new FileReader();
            reader.onload = (e) => {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                populateSheetDropdown(workbook.SheetNames, workbook); // 用工作表名称填充下拉框
            };
            reader.readAsArrayBuffer(file);
        }

        function populateSheetDropdown(sheetNames, workbook) {
            sheetDropdown.innerHTML = ''; // 清空现有选项
            sheetNames.forEach((sheet, index) => {
                const option = document.createElement('option');
                option.value = index;
                option.innerText = sheet;
                sheetDropdown.appendChild(option);
            });
            sheetDropdown.onchange = () => selectSheet(sheetDropdown.value, workbook); // 更新选择时
            selectSheet(0, workbook); // 默认选择第一个工作表
        }

        function selectSheet(index, workbook) {
            const sheetName = workbook.SheetNames[index];
            const worksheet = workbook.Sheets[sheetName];
            populateCurrentSheetTable(worksheet); // 根据选择的工作表填充表格
        }

        function populateCurrentSheetTable(worksheet) {
            const tbody = currentSheetTable.querySelector('tbody');
            tbody.innerHTML = ''; // 清空现有行

            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }); // 将工作表转换为JSON
            jsonData.forEach((row, rowIndex) => {
                if (rowIndex === 0 || rowIndex === 1) return; // 跳过前两行（标题和程序字段名）

                // Check if the row is empty
                const isEmptyRow = row.every(cell => cell === undefined || cell === '');
                if (isEmptyRow) return; // 如果整行都是空值，则跳过

                const newRow = document.createElement('tr');
                
                // Ensure each cell corresponds to the correct column, including empty values
                for (let i = 0; i < 7; i++) { // Assuming there are 7 columns
                    const newCell = document.createElement('td');
                    newCell.innerText = row[i] !== undefined ? row[i] : ''; // 显示对应列的值，空值时显示空值
                    newRow.appendChild(newCell);
                }
                
                tbody.appendChild(newRow);
            });
        }

        function populateSystemFields() {
            // 模拟系统字段以供演示
            const systemFields = ['系统字段1', '系统字段2', '系统字段3'];
            systemFields.forEach(field => {
                const li = document.createElement('li');
                li.innerText = field;
                systemFieldsList.appendChild(li);
            });
        }

        autoMatchButton.addEventListener('click', async () => {
            const database = dbSchemaSelect.value;
            const table = dbTableSelect.value;
            
            if (!database || !table) {
                alert('请先选择数据库和数据表');
                return;
            }

            try {
                // 获取数据库字段
                const response = await fetch(`/api/fields?database=${database}&table=${table}`);
                const dbFields = await response.json();
                
                // 获取当前工作表所有行
                const rows = document.querySelectorAll('#current-sheet-table tbody tr');
                
                // 遍历每一行进行匹配
                rows.forEach(row => {
                    const excelFieldName = row.cells[2].textContent.trim().toLowerCase(); // 获取名称列的值
                    
                    // 查找匹配的数据库字段
                    const matchedField = dbFields.find(field => 
                        field.name.toLowerCase() === excelFieldName ||
                        field.name.toLowerCase().replace(/_/g, '') === excelFieldName.replace(/\s/g, '')
                    );
                    
                    if (matchedField) {
                        // 更新字段名称
                        row.cells[1].textContent = matchedField.name;
                        // 更新数据类型
                        row.cells[3].textContent = matchedField.type;
                        // 标记已匹配
                        row.style.backgroundColor = '#e6ffe6';
                    }
                });
                
                alert('自动匹配完成！');
            } catch (error) {
                console.error('自动匹配失败:', error);
                alert('自动匹配过程中发生错误');
            }
        });

        document.querySelector('#current-sheet-table tbody').addEventListener('click', (e) => {
            const tr = e.target.closest('tr');
            if (tr) {
                // 移除其他行的选中状态
                document.querySelectorAll('#current-sheet-table tbody tr').forEach(row => {
                    row.classList.remove('selected');
                });
                // 添加选中状态
                tr.classList.add('selected');
            }
        });

        // 添加表格样式
        const style = document.createElement('style');
        style.textContent = `
            #current-sheet-table tbody tr.selected {
                background-color: #e6f3ff;
            }
            #current-sheet-table tbody tr:hover {
                background-color: #f5f5f5;
                cursor: pointer;
            }
        `;
        document.head.appendChild(style);

        exportMethod.addEventListener('change', function() {
            if (this.value === 'csv') {
                dateRange.style.display = 'block';
            } else {
                dateRange.style.display = 'none';
            }
        });

        document.getElementById('export-data').addEventListener('click', function() {
            loadingIndicator.style.display = 'block';
            setTimeout(() => {
                loadingIndicator.style.display = 'none';
                alert('数据导出成功！');
            }, 2000); // 模拟导出时间
        });

        // 获取数据库和表的引用
        const dbSchemaSelect = document.getElementById('db-schema');
        const dbTableSelect = document.getElementById('db-table');
        const fieldSearchInput = document.getElementById('field-search');
        const tableFieldsList = document.getElementById('table-fields-list');

        // 加载数据库列表
        async function loadDatabases() {
            try {
                const response = await fetch('/api/databases');
                const databases = await response.json();
                
                dbSchemaSelect.innerHTML = '<option value="">选择数据库...</option>';
                databases.forEach(db => {
                    const option = document.createElement('option');
                    option.value = db.name;
                    option.textContent = db.name;
                    dbSchemaSelect.appendChild(option);
                });
            } catch (error) {
                console.error('加载数据库列表失败:', error);
            }
        }

        // 加载数据表列表
        async function loadTables(database) {
            try {
                const response = await fetch(`/api/tables?database=${database}`);
                const tables = await response.json();
                
                dbTableSelect.innerHTML = '<option value="">选择数据表...</option>';
                tables.forEach(table => {
                    const option = document.createElement('option');
                    option.value = table.name;
                    option.textContent = table.name;
                    dbTableSelect.appendChild(option);
                });
            } catch (error) {
                console.error('加载数据表列表失败:', error);
            }
        }

        // 加载表字段
        async function loadTableFields(database, table) {
            try {
                const response = await fetch(`/api/fields?database=${database}&table=${table}`);
                const fields = await response.json();
                
                displayTableFields(fields);
            } catch (error) {
                console.error('加载表字段失败:', error);
            }
        }

        // 显示表字段
        function displayTableFields(fields) {
            tableFieldsList.innerHTML = '';
            fields.forEach(field => {
                const fieldItem = document.createElement('div');
                fieldItem.className = 'field-item';
                fieldItem.innerHTML = `
                    <div class="field-name">${field.name}</div>
                    <div class="field-type">${field.type}</div>
                `;
                fieldItem.onclick = () => handleFieldSelection(field);
                tableFieldsList.appendChild(fieldItem);
            });
        }

        // 处理字段搜索
        function handleFieldSearch(searchText) {
            const fieldItems = tableFieldsList.getElementsByClassName('field-item');
            Array.from(fieldItems).forEach(item => {
                const fieldName = item.querySelector('.field-name').textContent.toLowerCase();
                if (fieldName.includes(searchText.toLowerCase())) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // 处理字段选择
        function handleFieldSelection(field) {
            // 获取当前选中的行
            const selectedRow = document.querySelector('#current-sheet-table tbody tr.selected');
            if (selectedRow) {
                // 更新字段名称
                selectedRow.cells[1].textContent = field.name;
                selectedRow.classList.remove('selected');
            }
        }

        // 事件监听器
        dbSchemaSelect.addEventListener('change', (e) => {
            if (e.target.value) {
                loadTables(e.target.value);
            }
        });

        dbTableSelect.addEventListener('change', (e) => {
            if (e.target.value && dbSchemaSelect.value) {
                loadTableFields(dbSchemaSelect.value, e.target.value);
            }
        });

        fieldSearchInput.addEventListener('input', (e) => {
            handleFieldSearch(e.target.value);
        });

        // 初始加载数据库列表
        loadDatabases();

        document.addEventListener('DOMContentLoaded', function() {
            const dbConnectionForm = document.getElementById('db-connection-form');
            const dbSchemaSelect = document.getElementById('db-schema');
            const dbTableSelect = document.getElementById('db-table');
            const databaseFields = document.querySelector('.database-fields');
            const autoMatchBtn = document.getElementById('auto-match-btn');
            
            // 数据库连接表单提交
            dbConnectionForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const formData = new FormData(dbConnectionForm);
                try {
                    // 先获取数据库列表
                    const response = await fetch('/get_databases', {
                        method: 'POST',
                        body: formData
                    });
                    const data = await response.json();
                    
                    if (data.success) {
                        // 更新数据库选择下拉框
                        dbSchemaSelect.innerHTML = '<option value="">选择数据库...</option>' +
                            data.databases.map(db => 
                                `<option value="${db}">${db}</option>`
                            ).join('');
                        dbSchemaSelect.style.display = 'block';
                        
                        // 清空并隐藏表选择
                        dbTableSelect.innerHTML = '<option value="">选择数据表...</option>';
                        dbTableSelect.style.display = 'none';
                    } else {
                        alert('获取数据库列表失败: ' + data.error);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('连接数据库时发生错误');
                }
            });
            
            // 数据库选择变更事件
            dbSchemaSelect.addEventListener('change', async (e) => {
                const selectedDb = e.target.value;
                if (!selectedDb) {
                    dbTableSelect.style.display = 'none';
                    return;
                }
                
                const formData = new FormData(dbConnectionForm);
                formData.append('selected_db', selectedDb);
                
                try {
                    const response = await fetch('/get_db_tables', {
                        method: 'POST',
                        body: formData
                    });
                    const data = await response.json();
                    
                    if (data.success) {
                        // 更新表选择下拉框
                        dbTableSelect.innerHTML = '<option value="">选择数据表...</option>' +
                            data.tables.map(table => 
                                `<option value="${table}">${table}</option>`
                            ).join('');
                        dbTableSelect.style.display = 'block';
                    } else {
                        alert('获取表列表失败: ' + data.error);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('获取表列表时发生错误');
                }
            });
            
            // 表选择变更事件
            dbTableSelect.addEventListener('change', async (e) => {
                const tableName = e.target.value;
                if (!tableName) {
                    databaseFields.style.display = 'none';
                    return;
                }
                
                const formData = new FormData(dbConnectionForm);
                formData.append('selected_db', dbSchemaSelect.value);
                formData.append('table_name', tableName);
                
                try {
                    const response = await fetch('/get_table_fields', {
                        method: 'POST',
                        body: formData
                    });
                    const data = await response.json();
                    
                    if (data.success) {
                        databaseFields.style.display = 'block';
                        updateDatabaseFieldsTable(data.fields);
                    } else {
                        alert('获取表字段失败: ' + data.error);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('获取表字段时发生错误');
                }
            });
            
            // 更新数据库字段表格
            function updateDatabaseFieldsTable(fields) {
                const tbody = document.querySelector('#database-fields-table tbody');
                tbody.innerHTML = fields.map(field => `
                    <tr data-field-name="${field.name}">
                        <td>${field.name}</td>
                        <td>${field.type}</td>
                        <td>${field.required ? '是' : '否'}</td>
                        <td>${field.primary_key ? '是' : '否'}</td>
                        <td>${field.comment}</td>
                    </tr>
                `).join('');
            }
            
            // 自动匹配功能
            autoMatchBtn.addEventListener('click', () => {
                const currentFields = Array.from(document.querySelectorAll('#current-sheet-table tbody tr'));
                const dbFields = Array.from(document.querySelectorAll('#database-fields-table tbody tr'));
                
                currentFields.forEach(currentRow => {
                    const currentName = currentRow.cells[2].textContent.trim().toLowerCase();
                    
                    // 查找匹配的数据库字段
                    const matchedField = dbFields.find(dbRow => {
                        const dbName = dbRow.getAttribute('data-field-name').toLowerCase();
                        return dbName === currentName || 
                               dbName.replace(/_/g, '') === currentName.replace(/\s/g, '');
                    });
                    
                    if (matchedField) {
                        // 更新字段信息
                        currentRow.cells[1].textContent = matchedField.getAttribute('data-field-name');
                        currentRow.cells[3].textContent = matchedField.cells[1].textContent; // 数据类型
                        currentRow.style.backgroundColor = '#e6ffe6';
                    }
                });
                
                alert('自动匹配完成！');
            });
        });

        let isConnected = false;


        async function loadDatabaseTables(username, password, dsn) {
            try {
                const response = await fetch('/get_db_tables', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        db_username: username,
                        db_password: password,
                        db_dsn: dsn
                    })
                });
                
                const data = await response.json();
                if (data.success) {
                    const tableSelect = document.getElementById('db-table');
                    tableSelect.innerHTML = data.tables.map(table => 
                        `<option value="${table}">${table}</option>`
                    ).join('');
                } else {
                    alert('获取表列表失败: ' + data.error);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('获取表列表时发生错误');
            }
        }

        // 定义表结构数据
        const tableStructures = {
            'settlement_zy_main': {
                name: '医保住院结算主单',
                fields: {
                    '病案关联字段': { type: 'VARCHAR2(30)', required: true },
                    '结算单据号': { type: 'VARCHAR2(30)', required: true },
                    '医疗机构编码': { type: 'VARCHAR2(30)', required: true },
                    '医疗机构名称': { type: 'VARCHAR2(500)', required: true },
                    '医保结算等级': { type: 'VARCHAR2(6)', required: false },
                    '统筹区域编码': { type: 'VARCHAR2(6)', required: false },
                    '统筹区域名称': { type: 'VARCHAR2(6)', required: false },
                    '结算日期': { type: 'DATE', required: false },
                    '结算年份': { type: 'VARCHAR2(4)', required: false },
                    '结算月份': { type: 'VARCHAR2(6)', required: false },
                    '住院号': { type: 'VARCHAR2(30)', required: false },
                    '个人编码': { type: 'VARCHAR2(30)', required: true },
                    '患者社会保障号码': { type: 'VARCHAR2(50)', required: true },
                    '病案号': { type: 'VARCHAR2(30)', required: false },
                    '险种类型': { type: 'VARCHAR2(6)', required: false },
                    '人员类型': { type: 'VARCHAR2(6)', required: true },
                    '入院科室': { type: 'VARCHAR2(200)', required: false },
                    '转科科室': { type: 'VARCHAR2(20)', required: false },
                    '出院科室': { type: 'VARCHAR2(100)', required: false },
                    '主诊医师编码': { type: 'VARCHAR2(150)', required: false },
                    '主诊医师姓名': { type: 'VARCHAR2(150)', required: false },
                    '患者姓名': { type: 'VARCHAR2(50)', required: true },
                    '患者性别': { type: 'VARCHAR2(6)', required: false },
                    '患者出生日期': { type: 'DATE', required: false },
                    '患者年龄': { type: 'NUMBER(4,1)', required: false },
                    '患者所在单位': { type: 'VARCHAR2(200)', required: false },
                    '患者床位号': { type: 'VARCHAR2(30)', required: false },
                    '医疗类别': { type: 'VARCHAR2(6)', required: true },
                    '异地标志': { type: 'VARCHAR2(6)', required: false },
                    '入院日期': { type: 'DATE', required: true },
                    '出院日期': { type: 'DATE', required: true },
                    '住院天数': { type: 'NUMBER(16)', required: false },
                    '离院方式': { type: 'VARCHAR2(10)', required: false },
                    '上一次出院日期': { type: 'VARCHAR2(6)', required: false },
                    '是否有31天内再住院计划': { type: 'VARCHAR2(6)', required: false },
                    '医疗总费用': { type: 'NUMBER(16,2)', required: true },
                    '基本统筹支付': { type: 'NUMBER(16,2)', required: true },
                    '大病保险': { type: 'NUMBER(16,2)', required: true },
                    '医疗救助': { type: 'NUMBER(16,2)', required: true },
                    '公务员医疗补助': { type: 'NUMBER(16,2)', required: true },
                    '大额补充': { type: 'NUMBER(16,2)', required: true },
                    '企业补充': { type: 'NUMBER(16,2)', required: false },
                    '个人现金支付': { type: 'NUMBER(16,2)', required: true },
                    '个人账户支付': { type: 'NUMBER(16,2)', required: true },
                    '个人自付': { type: 'NUMBER(16,2)', required: true },
                    '个人自费': { type: 'NUMBER(16,2)', required: true },
                    '医保范围内金额': { type: 'NUMBER(16,2)', required: true },
                    '入院诊断编码': { type: 'VARCHAR2(4000)', required: false },
                    '入院诊断名称': { type: 'VARCHAR2(4000)', required: false },
                    '出院诊断编码': { type: 'VARCHAR2(4000)', required: false },
                    '出院诊断名称': { type: 'VARCHAR2(4000)', required: false },
                    '医保支付方式': { type: 'VARCHAR2(20)', required: false },
                    '入院科别': { type: 'VARCHAR2(20)', required: false },
                    '出院科别': { type: 'VARCHAR2(20)', required: false },
                    '全部诊断名称': { type: 'VARCHAR2(4000)', required: false },
                    '全部诊断编码': { type: 'VARCHAR2(4000)', required: false },
                    '全部手术名称': { type: 'VARCHAR2(4000)', required: false },
                    '全部手术编码': { type: 'VARCHAR2(4000)', required: false }
                }
            }
        };

        // 监听表选择变化
        document.getElementById('target-table-select').addEventListener('change', function(e) {
            const selectedTable = e.target.value;
            const databaseFieldsTable = document.getElementById('database-fields-table');
            const tableStructureDisplay = document.getElementById('table-structure-display');
            
            if (!selectedTable) {
                databaseFieldsTable.querySelector('tbody').innerHTML = '';
                tableStructureDisplay.style.display = 'none';
                return;
            }
            
            const tableStructure = tableStructures[selectedTable];
            if (tableStructure) {
                // 更新数据库字段表格
                const tbody = databaseFieldsTable.querySelector('tbody');
                tbody.innerHTML = ''; // 清空现有内容
                
                // 填充字段结构
                Object.entries(tableStructure.fields).forEach(([fieldName, fieldInfo]) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${fieldName}</td>
                        <td>${fieldInfo.type}</td>
                        <td>${fieldInfo.required ? '是' : '否'}</td>
                        <td>${fieldName === '病案关联字段' ? '是' : '否'}</td>
                        <td></td>
                    `;
                    tbody.appendChild(row);
                });
                
                // 显示表格
                databaseFieldsTable.style.display = 'table';
                tableStructureDisplay.style.display = 'block';
            }
        });
    </script>
</body>
</html>