
SELECT
  A.病案号,
  A.结算单据号,
  A.医疗机构编码,
  A.医疗机构名称,
  A.结算日期,
  A.住院号,
  A.个人编码,
  A.患者社会保障号码,
  A.身份证号,
  A.险种类型,
  A.入院科室,
  A.出院科室,
  A.主诊医师姓名,
  A.患者姓名,
  A.患者年龄,
  A.异地标志,
  A.入院日期,
  A.出院日期,
  DATEDIFF(a.出院日期,a.入院日期,'DAY') +1 as 住院天数,
  A.医疗总费用,
  A.基本统筹支付,
  A.个人自付,
  A.个人自费,
  A.符合基本医疗保险的费用,
  A.入院诊断编码,
  A.入院诊断名称,
  A.出院诊断编码,
  A.出院诊断名称,
  A.主手术及操作编码,
  A.主手术及操作名称,
  A.其他手术及操作编码,
  A.其他手术及操作名称,
  B.开单科室名称,
  B.执行科室名称,
  B.开单医师姓名,
  B.费用类别,
  B.项目使用日期,
  B.结算日期,
  B.医院项目编码,
  B.医院项目名称,
  B.医保项目编码,
  B.医保项目名称,
  B.规格,
  B.单价,
  B.支付类别,
  B.报销比例,
  B.自付比例,
  A.支付地点类别,
  --B.记账流水号,
  SUM(b.数量) AS 使用数量,
  SUM(b.金额) AS 使用金额,
  SUM(b.医保范围内金额) AS 医保范围内总金额
FROM
  ZYMX b
  join ZYZD a on a.结算单据号 = b.结算单据号
WHERE
  b.医保项目名称 in ({医保名称1})  
GROUP BY
   A.病案号,
  A.结算单据号,
  A.医疗机构编码,
  A.医疗机构名称,
  A.结算日期,
  A.住院号,
  A.个人编码,
  A.患者社会保障号码,
  A.身份证号,
  A.险种类型,
  A.入院科室,
  A.出院科室,
  A.主诊医师姓名,
  A.患者姓名,
  A.患者年龄,
  A.异地标志,
  A.入院日期,
  A.出院日期,
  A.医疗总费用,
  A.基本统筹支付,
  A.个人自付,
  A.个人自费,
  A.符合基本医疗保险的费用,
  A.入院诊断编码,
  A.入院诊断名称,
  A.出院诊断编码,
  A.出院诊断名称,
  A.主手术及操作编码,
  A.主手术及操作名称,
  A.其他手术及操作编码,
  A.其他手术及操作名称,
  B.开单科室名称,
  B.执行科室名称,
  B.开单医师姓名,
  B.费用类别,
  B.项目使用日期,
  B.结算日期,
  B.医院项目编码,
  B.医院项目名称,
  B.医保项目编码,
  B.医保项目名称,
  B.规格,
  B.单价,
  B.支付类别,
  B.报销比例,
  B.自付比例,
  A.支付地点类别
  --B.记账流水号
ORDER BY
  A.患者姓名,
  B.项目使用日期;
