#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Gemini多密钥测试
"""

import requests
import json

# 测试配置
base_url = 'http://localhost:5001'

def test_key_status():
    """测试获取密钥状态"""
    print('=== 测试获取密钥状态 ===')
    
    try:
        response = requests.get(f'{base_url}/api/gemini/keys/status', timeout=10)
        print(f'状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                status = data['data']
                print(f"✅ 密钥状态获取成功")
                print(f"总密钥数: {status['total_keys']}")
                print(f"可用密钥数: {status['available_keys']}")
                print(f"总使用次数: {status['total_usage']}")
                return True
            else:
                print(f"❌ 获取失败: {data.get('error')}")
        else:
            print(f"❌ API错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return False

def test_gemini_call():
    """测试Gemini API调用"""
    print('\n=== 测试Gemini API调用 ===')
    
    test_data = {
        'rule_content': '呼吸机辅助呼吸、遥测心电监护、连续性血液净化等计价单位为小时，医院计费每日总时长大于24小时。',
        'rule_name': '呼吸机辅助呼吸超日限制',
        'behavior_type': '超标准收费',
        'city': '北京'
    }
    
    try:
        print("发送请求...")
        response = requests.post(
            f'{base_url}/api/rules/intelligent-get-medical-names',
            json=test_data,
            timeout=60
        )
        
        print(f'状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Gemini API调用成功")
                print(f"医保名称1: {data.get('medical_name1', '')}")
                print(f"医保名称2: {data.get('medical_name2', '')}")
                return True
            else:
                print(f"❌ API调用失败: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return False

def main():
    """主测试函数"""
    print('=' * 50)
    print('Gemini多密钥功能简单测试')
    print('=' * 50)
    
    # 测试密钥状态
    if test_key_status():
        # 测试API调用
        test_gemini_call()
        
        # 再次检查状态
        print('\n=== 调用后的密钥状态 ===')
        test_key_status()
    
    print('\n测试完成')

if __name__ == "__main__":
    main()
