<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医院个性化规则推荐系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        /* 全局缩放到90% */
        body {
            transform: scale(0.9);
            transform-origin: top left;
            width: 111.11%; /* 100% / 0.9 = 111.11% */
            margin: 0;
            padding: 0;
            min-height: auto;
        }

        .container-fluid {
            width: 100%;
            max-width: none;
            min-height: auto;
        }

        /* 确保页面内容不会产生多余的空白 */
        html, body {
            overflow-x: hidden;
        }

        .hospital-card {
            transition: transform 0.2s;
        }
        .hospital-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .match-score {
            font-weight: bold;
        }
        .match-high { color: #28a745; }
        .match-medium { color: #ffc107; }
        .match-low { color: #dc3545; }
        .rule-tag {
            display: inline-block;
            padding: 2px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        .tag-recommended { background-color: #e3f2fd; color: #1976d2; }
        .tag-adopted { background-color: #e8f5e8; color: #2e7d32; }
        .tag-ignored { background-color: #fafafa; color: #757575; }

        /* 同规则ID分组样式 */
        .same-rule-group {
            position: relative;
        }

        /* 为同规则ID的记录添加左侧颜色条 */
        .same-rule-group::before {
            content: '';
            position: absolute;
            left: -3px;
            top: 0;
            bottom: 0;
            width: 4px;
            border-radius: 0 2px 2px 0;
        }

        /* 不同规则ID使用不同颜色 */
        .rule-color-1::before { background: linear-gradient(to bottom, #e3f2fd, #1976d2); }
        .rule-color-2::before { background: linear-gradient(to bottom, #f3e5f5, #7b1fa2); }
        .rule-color-3::before { background: linear-gradient(to bottom, #e8f5e8, #388e3c); }
        .rule-color-4::before { background: linear-gradient(to bottom, #fff3e0, #f57c00); }
        .rule-color-5::before { background: linear-gradient(to bottom, #fce4ec, #c2185b); }
        .rule-color-6::before { background: linear-gradient(to bottom, #e0f2f1, #00796b); }

        /* 同规则ID的背景色 */
        .rule-color-1 .card-body { background: linear-gradient(to right, rgba(25, 118, 210, 0.03), transparent); }
        .rule-color-2 .card-body { background: linear-gradient(to right, rgba(123, 31, 162, 0.03), transparent); }
        .rule-color-3 .card-body { background: linear-gradient(to right, rgba(56, 142, 60, 0.03), transparent); }
        .rule-color-4 .card-body { background: linear-gradient(to right, rgba(245, 124, 0, 0.03), transparent); }
        .rule-color-5 .card-body { background: linear-gradient(to right, rgba(194, 24, 91, 0.03), transparent); }
        .rule-color-6 .card-body { background: linear-gradient(to right, rgba(0, 121, 107, 0.03), transparent); }

        /* 已选择同规则ID其他记录的样式 */
        .rule-disabled {
            opacity: 0.6;
            background-color: #f8f9fa !important;
        }

        .rule-disabled .card-body {
            background: linear-gradient(to right, rgba(108, 117, 125, 0.1), transparent) !important;
        }

        /* 规则ID标识 */
        .rule-id-badge {
            font-size: 0.75rem;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 500;
        }

        /* 分组标题样式 */
        .alert-info {
            border-left: 4px solid #0dcaf0;
        }

        .alert-light {
            border-left: 4px solid #6c757d;
        }

        /* 规则卡片悬停效果 */
        .rule-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .rule-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        /* 规则状态更新动画 */
        .rule-card.updating {
            background-color: #e8f5e8 !important;
            transition: background-color 0.3s ease;
        }

        .rule-card.highlighted {
            box-shadow: 0 0 20px rgba(13, 202, 240, 0.5) !important;
            transition: box-shadow 0.5s ease;
        }

        /* 过滤按钮样式 */
        .filter-badge {
            transition: all 0.2s ease;
            user-select: none;
        }

        .filter-badge:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* 分组过渡动画 */
        .duplicate-group {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .duplicate-group[style*="display: none"] {
            opacity: 0;
            transform: scale(0.95);
        }

        /* 医保项目名称高亮样式 */
        mark.bg-primary {
            border-radius: 3px;
            padding: 2px 4px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        mark.bg-success {
            border-radius: 3px;
            padding: 2px 4px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        mark.bg-warning {
            border-radius: 3px;
            padding: 2px 4px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        /* 交叉字段重复项目的高亮样式 */
        mark.bg-info {
            border-radius: 3px;
            padding: 2px 4px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        mark.bg-dark {
            border-radius: 3px;
            padding: 2px 4px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        mark.bg-secondary {
            border-radius: 3px;
            padding: 2px 4px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="bi bi-hospital"></i> 医院个性化规则推荐系统</h2>
                <p class="text-muted">基于医院收费数据，智能推荐适用的飞检规则</p>
            </div>
        </div>

        <!-- 功能导航 -->
        <div class="row mb-4">
            <div class="col-12">
                <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="hospital-tab" data-bs-toggle="tab" data-bs-target="#hospital-panel" type="button" role="tab">
                            <i class="bi bi-building"></i> 医院管理
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="data-tab" data-bs-toggle="tab" data-bs-target="#data-panel" type="button" role="tab">
                            <i class="bi bi-upload"></i> 收费数据导入
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="rules-tab" data-bs-toggle="tab" data-bs-target="#rules-panel" type="button" role="tab">
                            <i class="bi bi-magic"></i> 规则推荐
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis-panel" type="button" role="tab">
                            <i class="bi bi-graph-up"></i> 数据分析
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 标签页内容 -->
        <div class="tab-content" id="mainTabContent">
            <!-- 医院管理面板 -->
            <div class="tab-pane fade show active" id="hospital-panel" role="tabpanel">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h4>医院列表</h4>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-primary" onclick="addHospital()">
                            <i class="bi bi-plus"></i> 添加医院
                        </button>
                    </div>
                </div>

                <!-- 医院列表 -->
                <div class="row" id="hospitalList">
                    <!-- 医院卡片将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 收费数据导入面板 -->
            <div class="tab-pane fade" id="data-panel" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-upload"></i> 医院收费数据导入</h5>
                            </div>
                            <div class="card-body">
                                <form id="uploadForm" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="hospitalSelect" class="form-label">选择医院</label>
                                        <select class="form-select" id="hospitalSelect" required>
                                            <option value="">请选择医院</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="dataFile" class="form-label">收费数据文件</label>
                                        <input type="file" class="form-control" id="dataFile" accept=".xlsx,.xls,.csv" required>
                                        <div class="form-text">支持Excel和CSV格式，文件应包含：医保项目编码、医保项目名称、收费金额、收费次数等字段</div>
                                    </div>
                                    <button type="submit" class="btn btn-success">
                                        <i class="bi bi-upload"></i> 上传数据
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6>数据格式说明</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>必需字段：</strong></p>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success"></i> 医保项目编码</li>
                                    <li><i class="bi bi-check-circle text-success"></i> 医保项目名称</li>
                                    <li><i class="bi bi-check-circle text-success"></i> 收费金额</li>
                                    <li><i class="bi bi-check-circle text-success"></i> 收费次数</li>
                                </ul>
                                <p><strong>可选字段：</strong></p>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-info-circle text-info"></i> 科室名称</li>
                                    <li><i class="bi bi-info-circle text-info"></i> 医生姓名</li>
                                    <li><i class="bi bi-info-circle text-info"></i> 患者年龄</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据预览区域 -->
                <div class="row mt-4" id="dataPreviewSection" style="display: none;">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>数据预览</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="dataPreviewTable">
                                        <thead></thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-primary" onclick="confirmUpload()">
                                        <i class="bi bi-check"></i> 确认导入
                                    </button>
                                    <button class="btn btn-secondary" onclick="cancelUpload()">
                                        <i class="bi bi-x"></i> 取消
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 规则推荐面板 -->
            <div class="tab-pane fade" id="rules-panel" role="tabpanel">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h4>规则推荐</h4>
                    </div>
                    <div class="col-md-6 text-end">
                        <select class="form-select d-inline-block w-auto me-2" id="ruleHospitalSelect">
                            <option value="">选择医院</option>
                        </select>
                        <button class="btn btn-success" onclick="generateRecommendations()">
                            <i class="bi bi-magic"></i> 生成推荐
                        </button>
                        <button class="btn btn-warning me-2" id="duplicateAnalysisBtn" onclick="showDuplicateAnalysis()" style="display: none;">
                            <i class="bi bi-search"></i> 重复规则审查
                        </button>
                        <button class="btn btn-info me-2" id="testDuplicateBtn" onclick="testDuplicateAPI()" style="display: none;">
                            <i class="bi bi-bug"></i> 测试API
                        </button>
                    </div>
                </div>

                <!-- 过滤条件 -->
                <div class="card mb-3" id="filterSection" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-funnel"></i> 过滤条件
                            <button class="btn btn-outline-secondary btn-sm float-end" onclick="clearFilters()">
                                <i class="bi bi-arrow-clockwise"></i> 清除过滤
                            </button>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="filterCity" class="form-label">城市</label>
                                <select class="form-select form-select-sm" id="filterCity" onchange="applyFilters()">
                                    <option value="">全部城市</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterSource" class="form-label">规则来源</label>
                                <select class="form-select form-select-sm" id="filterSource" onchange="applyFilters()">
                                    <option value="">全部来源</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="filterType" class="form-label">类型</label>
                                <select class="form-select form-select-sm" id="filterType" onchange="applyFilters()">
                                    <option value="">全部类型</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="filterRuleCategory" class="form-label">规则类型</label>
                                <select class="form-select form-select-sm" id="filterRuleCategory" onchange="applyFilters()">
                                    <option value="">全部规则类型</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="filterRuleName" class="form-label">规则名称</label>
                                <input type="text" class="form-control form-control-sm" id="filterRuleName"
                                       placeholder="输入规则名称关键词" onkeyup="applyFilters()">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-3">
                                <label for="filterStatus" class="form-label">状态</label>
                                <select class="form-select form-select-sm" id="filterStatus" onchange="applyFilters()">
                                    <option value="">全部状态</option>
                                    <option value="推荐">推荐</option>
                                    <option value="已采用">已采用</option>
                                    <option value="已忽略">已忽略</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filterMatchScore" class="form-label">匹配度</label>
                                <select class="form-select form-select-sm" id="filterMatchScore" onchange="applyFilters()">
                                    <option value="">全部匹配度</option>
                                    <option value="high">高匹配度 (≥80%)</option>
                                    <option value="medium">中匹配度 (50-79%)</option>
                                    <option value="low">低匹配度 (<50%)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">显示统计</label>
                                <div class="small text-muted" id="filterStats">
                                    总计：0 条规则
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 推荐结果 -->
                <div id="recommendationsSection">
                    <!-- 推荐结果将通过JavaScript动态生成 -->
                </div>

                <!-- 批量操作工具栏 -->
                <div id="batchToolbar" class="card mt-3" style="display: none;">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <span id="selectedCount">已选择 0 条规则</span>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-success me-2" onclick="batchAdoptRules()">
                                    <i class="bi bi-check-circle"></i> 批量采用
                                </button>
                                <button class="btn btn-secondary me-2" onclick="batchIgnoreRules()">
                                    <i class="bi bi-x-circle"></i> 批量忽略
                                </button>

                                <button class="btn btn-outline-secondary" onclick="clearSelection()">
                                    <i class="bi bi-arrow-clockwise"></i> 清除选择
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据分析面板 -->
            <div class="tab-pane fade" id="analysis-panel" role="tabpanel">
                <div id="hospitalDataSection">
                    <!-- 医院数据将通过JavaScript动态生成 -->
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> 请从医院管理页面点击"查看数据"来查看具体医院的收费数据分析
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加医院模态框 -->
    <div class="modal fade" id="hospitalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加医院</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="hospitalForm">
                        <div class="mb-3">
                            <label for="hospitalName" class="form-label">医院名称</label>
                            <input type="text" class="form-control" id="hospitalName" required>
                        </div>
                        <div class="mb-3">
                            <label for="hospitalCode" class="form-label">医院编码</label>
                            <input type="text" class="form-control" id="hospitalCode">
                        </div>
                        <div class="mb-3">
                            <label for="hospitalCity" class="form-label">所在城市</label>
                            <select class="form-select" id="hospitalCity" required>
                                <option value="">请选择城市</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="hospitalLevel" class="form-label">医院等级</label>
                            <select class="form-select" id="hospitalLevel">
                                <option value="">请选择等级</option>
                                <option value="三甲">三甲</option>
                                <option value="三乙">三乙</option>
                                <option value="二甲">二甲</option>
                                <option value="二乙">二乙</option>
                                <option value="一甲">一甲</option>
                                <option value="一乙">一乙</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveHospital()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // 全局变量
        let hospitals = [];
        let currentUploadData = null;
        let hospitalModal;

        // 页面初始化
        $(document).ready(function() {
            hospitalModal = new bootstrap.Modal(document.getElementById('hospitalModal'));
            loadHospitals();
            loadCities();

            // 绑定文件上传事件
            $('#uploadForm').on('submit', handleFileUpload);
        });

        // 显示提示消息
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3`;
            toast.style.zIndex = '1050';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // 加载医院列表
        function loadHospitals() {
            fetch('/api/hospitals')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        hospitals = data.hospitals;
                        renderHospitalList();
                        updateHospitalSelects();
                    } else {
                        showToast('加载医院列表失败: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('加载医院列表失败:', error);
                    showToast('加载医院列表失败', 'error');
                });
        }

        // 渲染医院列表
        function renderHospitalList() {
            const container = document.getElementById('hospitalList');
            container.innerHTML = '';

            hospitals.forEach(hospital => {
                const card = document.createElement('div');
                card.className = 'col-md-4 mb-3';
                card.innerHTML = `
                    <div class="card hospital-card">
                        <div class="card-body">
                            <h5 class="card-title">${hospital.医院名称}</h5>
                            <p class="card-text">
                                <small class="text-muted">
                                    <i class="bi bi-geo-alt"></i> ${hospital.所在城市 || '未设置'}
                                    <br>
                                    <i class="bi bi-award"></i> ${hospital.医院等级 || '未设置'}
                                    <br>
                                    <i class="bi bi-hash"></i> ${hospital.医院编码 || '未设置'}
                                </small>
                            </p>
                            <div class="mb-2" id="adoptedRules_${hospital.医院ID}">
                                <small class="text-muted">正在加载已采用规则...</small>
                            </div>
                            <div class="btn-group w-100">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewHospitalData(${hospital.医院ID})">
                                    <i class="bi bi-eye"></i> 查看数据
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="generateHospitalRules(${hospital.医院ID})">
                                    <i class="bi bi-magic"></i> 生成规则
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="viewAdoptedRules(${hospital.医院ID})">
                                    <i class="bi bi-check-circle"></i> 已采用
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="viewAllRules(${hospital.医院ID})">
                                    <i class="bi bi-list-ul"></i> 所有规则
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="deleteHospital(${hospital.医院ID})">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                container.appendChild(card);

                // 加载该医院的已采用规则
                loadHospitalAdoptedRules(hospital.医院ID);
            });
        }

        // 更新医院选择下拉框
        function updateHospitalSelects() {
            const selects = ['hospitalSelect', 'ruleHospitalSelect'];
            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                select.innerHTML = '<option value="">请选择医院</option>';
                hospitals.forEach(hospital => {
                    const option = document.createElement('option');
                    option.value = hospital.医院ID;
                    option.textContent = hospital.医院名称;
                    select.appendChild(option);
                });
            });
        }

        // 加载城市列表
        function loadCities() {
            fetch('/api/city_types')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const citySelect = document.getElementById('hospitalCity');
                        citySelect.innerHTML = '<option value="">请选择城市</option>';
                        data.types.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city;
                            option.textContent = city;
                            citySelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载城市列表失败:', error);
                });
        }

        // 添加医院
        function addHospital() {
            document.getElementById('hospitalForm').reset();
            hospitalModal.show();
        }

        // 保存医院
        function saveHospital() {
            const form = document.getElementById('hospitalForm');
            const formData = new FormData(form);

            const hospitalData = {
                医院名称: document.getElementById('hospitalName').value,
                医院编码: document.getElementById('hospitalCode').value,
                所在城市: document.getElementById('hospitalCity').value,
                医院等级: document.getElementById('hospitalLevel').value
            };

            fetch('/api/hospitals', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(hospitalData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('医院添加成功');
                    hospitalModal.hide();
                    loadHospitals();
                } else {
                    showToast('添加失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('添加医院失败:', error);
                showToast('添加医院失败', 'error');
            });
        }

        // 删除医院
        function deleteHospital(hospitalId) {
            if (confirm('确定要删除这家医院吗？这将同时删除相关的收费数据和规则推荐。')) {
                fetch(`/api/hospitals/${hospitalId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('医院删除成功');
                        loadHospitals();
                    } else {
                        showToast('删除失败: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('删除医院失败:', error);
                    showToast('删除医院失败', 'error');
                });
            }
        }

        // 处理文件上传
        function handleFileUpload(event) {
            event.preventDefault();

            const hospitalId = document.getElementById('hospitalSelect').value;
            const file = document.getElementById('dataFile').files[0];

            if (!hospitalId || !file) {
                showToast('请选择医院和上传文件', 'warning');
                return;
            }

            const formData = new FormData();
            formData.append('hospital_id', hospitalId);
            formData.append('file', file);

            showToast('正在处理文件，请稍候...', 'info');

            fetch('/api/hospital-data/preview', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentUploadData = data.full_data || data.data; // 使用完整数据
                    showDataPreview(data.data); // 只预览前100条

                    // 显示数据统计信息
                    if (data.total_count > data.preview_count) {
                        showToast(`文件包含 ${data.total_count} 条记录，预览显示前 ${data.preview_count} 条`, 'info');
                    }
                } else {
                    showToast('文件处理失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('文件上传失败:', error);
                showToast('文件上传失败', 'error');
            });
        }

        // 显示数据预览
        function showDataPreview(data) {
            if (!data || data.length === 0) {
                showToast('文件中没有有效数据', 'warning');
                return;
            }

            const section = document.getElementById('dataPreviewSection');
            const table = document.getElementById('dataPreviewTable');
            const thead = table.querySelector('thead');
            const tbody = table.querySelector('tbody');

            // 清空表格
            thead.innerHTML = '';
            tbody.innerHTML = '';

            // 创建表头
            const headers = Object.keys(data[0]);
            const headerRow = document.createElement('tr');
            headers.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);

            // 创建数据行（只显示前10行）
            const previewData = data.slice(0, 10);
            previewData.forEach(row => {
                const tr = document.createElement('tr');
                headers.forEach(header => {
                    const td = document.createElement('td');
                    td.textContent = row[header] || '';
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });

            // 显示预览区域
            section.style.display = 'block';
            section.scrollIntoView({ behavior: 'smooth' });
        }

        // 确认上传
        function confirmUpload() {
            if (!currentUploadData) {
                showToast('没有待上传的数据', 'warning');
                return;
            }

            const hospitalId = document.getElementById('hospitalSelect').value;

            fetch('/api/hospital-data/import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    hospital_id: hospitalId,
                    data: currentUploadData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`数据导入成功！共导入 ${data.imported_count} 条记录`);
                    cancelUpload();
                    document.getElementById('uploadForm').reset();
                } else {
                    showToast('数据导入失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('数据导入失败:', error);
                showToast('数据导入失败', 'error');
            });
        }

        // 取消上传
        function cancelUpload() {
            currentUploadData = null;
            document.getElementById('dataPreviewSection').style.display = 'none';
        }

        // 查看医院数据
        function viewHospitalData(hospitalId) {
            // 切换到数据分析标签页并显示该医院的数据
            const analysisTab = new bootstrap.Tab(document.getElementById('analysis-tab'));
            analysisTab.show();
            loadHospitalAnalysis(hospitalId);
        }

        // 生成医院规则推荐
        function generateHospitalRules(hospitalId) {
            // 切换到规则推荐标签页
            const rulesTab = new bootstrap.Tab(document.getElementById('rules-tab'));
            rulesTab.show();

            // 设置医院选择
            document.getElementById('ruleHospitalSelect').value = hospitalId;

            // 生成推荐
            generateRecommendations();
        }

        // 生成规则推荐
        function generateRecommendations() {
            const hospitalId = document.getElementById('ruleHospitalSelect').value;
            if (!hospitalId) {
                showToast('请先选择医院', 'warning');
                return Promise.reject('未选择医院');
            }

            const section = document.getElementById('recommendationsSection');
            section.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在分析医院数据，生成规则推荐...</p></div>';

            // 清理之前的数据
            originalRecommendations = [];
            filteredRecommendations = [];
            selectedRules.clear();

            // 先加载全局已采用规则ID，然后生成推荐
            return loadGlobalAdoptedRuleIds().then(() => {
                return fetch('/api/hospital-rules/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ hospital_id: hospitalId })
                });
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示推荐数量信息
                    if (data.total_count && data.returned_count) {
                        console.log(`生成推荐完成：总共找到 ${data.total_count} 条规则，显示 ${data.returned_count} 条`);
                    }
                    renderRecommendations(data.recommendations, data.total_count, data.returned_count);
                    return data.recommendations;
                } else {
                    section.innerHTML = `<div class="alert alert-danger">生成推荐失败: ${data.error}</div>`;
                    throw new Error(data.error);
                }
            })
            .catch(error => {
                console.error('生成推荐失败:', error);
                section.innerHTML = '<div class="alert alert-danger">生成推荐失败，请稍后重试</div>';
                throw error;
            });
        }

        // 全局变量存储选中的规则和原始推荐数据
        let selectedRules = new Set();
        let originalRecommendations = [];
        let filteredRecommendations = [];
        let globalAdoptedRuleIds = new Set(); // 全局已采用规则ID集合
        let currentFilterType = 'all'; // 'all', 'duplicate', 'clean'
        let baseFilteredRecommendations = []; // 统计按钮过滤后的基础数据

        // 获取全局已采用规则ID
        function loadGlobalAdoptedRuleIds() {
            return fetch('/api/global-adopted-rule-ids')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.rule_ids) {
                        globalAdoptedRuleIds = new Set(data.rule_ids);
                        console.log(`已加载全局已采用规则ID: ${globalAdoptedRuleIds.size} 个`);
                    } else {
                        globalAdoptedRuleIds = new Set();
                        console.log('未获取到全局已采用规则ID');
                    }
                })
                .catch(error => {
                    console.error('获取全局已采用规则ID失败:', error);
                    globalAdoptedRuleIds = new Set();
                });
        }

        // 渲染推荐结果
        function renderRecommendations(recommendations, totalCount, returnedCount) {
            const section = document.getElementById('recommendationsSection');

            if (!recommendations || recommendations.length === 0) {
                section.innerHTML = '<div class="alert alert-info">暂无适用的规则推荐，请确保已上传医院收费数据</div>';
                document.getElementById('batchToolbar').style.display = 'none';
                document.getElementById('filterSection').style.display = 'none';
                return;
            }

            // 对规则进行分组和排序
            const groupedAndSortedRecommendations = groupAndSortRecommendations(recommendations);

            // 保存原始数据和数量信息
            originalRecommendations = groupedAndSortedRecommendations;
            filteredRecommendations = groupedAndSortedRecommendations;

            // 显示数量信息
            if (totalCount && returnedCount) {
                const countInfo = document.createElement('div');
                countInfo.className = 'alert alert-info mb-3';
                countInfo.innerHTML = `
                    <i class="bi bi-info-circle"></i>
                    规则推荐生成完成：共找到 <strong>${totalCount}</strong> 条匹配规则，当前显示 <strong>${returnedCount}</strong> 条
                    ${totalCount > returnedCount ? `<br><small class="text-muted">如需查看更多规则，请使用过滤条件进行筛选</small>` : ''}
                `;
                section.appendChild(countInfo);
            }

            // 显示过滤器并初始化过滤选项
            document.getElementById('filterSection').style.display = 'block';
            initializeFilters(recommendations);

            renderFilteredRecommendations(filteredRecommendations);

            // 确保批量操作工具栏为推荐状态
            resetBatchToolbarForRecommendations();
        }

        // 分页相关变量
        let currentPage = 1;
        let pageSize = 10; // 每页显示10条规则
        let allFilteredRecommendations = [];

        // 渲染过滤后的推荐结果
        function renderFilteredRecommendations(recommendations) {
            allFilteredRecommendations = recommendations;
            currentPage = 1; // 重置到第一页
            renderCurrentPage();
        }

        // 渲染当前页
        function renderCurrentPage() {
            const section = document.getElementById('recommendationsSection');

            if (!allFilteredRecommendations || allFilteredRecommendations.length === 0) {
                section.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        暂无推荐规则，请先生成推荐或调整筛选条件。
                    </div>
                `;
                return;
            }

            // 获取医院信息用于显示
            const hospitalSelect = document.getElementById('ruleHospitalSelect');
            const hospitalName = hospitalSelect.options[hospitalSelect.selectedIndex]?.text || '未选择医院';

            // 计算分页
            const totalPages = Math.ceil(allFilteredRecommendations.length / pageSize);
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, allFilteredRecommendations.length);
            const currentRecommendations = allFilteredRecommendations.slice(startIndex, endIndex);

            // 计算统计信息
            const recommendedRules = originalRecommendations.filter(rule => rule.状态 === '推荐');
            const duplicateWarningRules = recommendedRules.filter(rule =>
                rule.重复已采用规则 && rule.重复已采用规则.length > 0
            );
            const cleanRecommendedRules = recommendedRules.filter(rule =>
                !rule.重复已采用规则 || rule.重复已采用规则.length === 0
            );

            let html = `
                <div class="alert alert-primary mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-hospital"></i>
                            <strong>当前查看医院：${hospitalName}</strong>
                            <span class="badge bg-primary ms-2">${allFilteredRecommendations.length} 条规则</span>
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-warning btn-sm"
                                    onclick="filterDuplicateWarningRules()"
                                    title="显示有重复筛查警告的推荐规则">
                                <i class="bi bi-exclamation-triangle"></i>
                                重复筛查警告 <span class="badge bg-light text-dark">${duplicateWarningRules.length}</span>
                            </button>
                            <button type="button" class="btn btn-info btn-sm"
                                    onclick="filterCleanRecommendedRules()"
                                    title="显示没有重复筛查警告的推荐规则">
                                <i class="bi bi-lightbulb"></i>
                                无重复推荐 <span class="badge bg-light text-dark">${cleanRecommendedRules.length}</span>
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm"
                                    onclick="showAllRules()"
                                    title="显示所有规则">
                                <i class="bi bi-list"></i>
                                显示全部
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">推荐规则列表 (第 ${currentPage}/${totalPages} 页，共 ${allFilteredRecommendations.length} 条)</h5>
                        <div>
                            <button class="btn btn-outline-primary btn-sm me-2" onclick="selectAllRules()">
                                <i class="bi bi-check-square"></i> 全选
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                <i class="bi bi-square"></i> 清除
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 分页控件 -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <select class="form-select form-select-sm" style="width: auto; display: inline-block;" onchange="changePageSize(this.value)">
                            <option value="10" ${pageSize === 10 ? 'selected' : ''}>每页 10 条</option>
                            <option value="20" ${pageSize === 20 ? 'selected' : ''}>每页 20 条</option>
                            <option value="50" ${pageSize === 50 ? 'selected' : ''}>每页 50 条</option>
                            <option value="100" ${pageSize === 100 ? 'selected' : ''}>每页 100 条</option>
                        </select>
                    </div>
                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                                <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;">上一页</a>
                            </li>
                            ${generatePageNumbers(currentPage, totalPages)}
                            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                                <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>

                <div class="row">
            `;

            currentRecommendations.forEach((rec, index) => {
                const matchClass = rec.匹配度 >= 0.8 ? 'match-high' : rec.匹配度 >= 0.5 ? 'match-medium' : 'match-low';
                const statusClass = rec.状态 === '已采用' ? 'tag-adopted' : rec.状态 === '已忽略' ? 'tag-ignored' : 'tag-recommended';

                // 检查是否为同规则ID的多条记录
                const isSameRuleGroup = rec.是否多记录规则;
                const ruleGroupClass = isSameRuleGroup ? `same-rule-group ${rec.颜色类名}` : '';

                // 检查该规则ID是否已有其他记录被选中（防重复选择）
                const isRuleDisabled = checkRuleIdDisabled(rec.规则ID, rec.适用ID);
                const disabledClass = isRuleDisabled ? 'rule-disabled' : '';

                // 获取禁用原因
                const disabledReason = getDisabledReason(rec.规则ID, rec.适用ID);

                // 生成规则ID标识
                const ruleIdBadge = isSameRuleGroup ?
                    `<span class="rule-id-badge bg-secondary text-white">规则ID: ${rec.规则ID} (${rec.规则ID分组序号}/${rec.规则ID分组总数})</span>` :
                    `<span class="rule-id-badge bg-light text-dark">规则ID: ${rec.规则ID}</span>`;

                html += `
                    <div class="col-md-12 mb-2" data-rule-id="${rec.规则ID}" data-record-id="${rec.适用ID}">
                        <div class="card rule-card ${ruleGroupClass} ${disabledClass}" data-rule-id="${rec.适用ID}">
                            <div class="card-body py-2">
                                <div class="row align-items-center">
                                    <div class="col-md-1">
                                        ${rec.状态 === '推荐' || rec.状态 === '已采用' ? `
                                            <input type="checkbox" class="form-check-input rule-checkbox"
                                                   value="${rec.适用ID}" onchange="updateSelection()" ${isRuleDisabled ? 'disabled' : ''}>
                                        ` : ''}
                                        <span class="rule-tag ${statusClass} ms-2">${rec.状态}</span>
                                        <br>${ruleIdBadge}
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="mb-1 fw-bold">
                                            ${rec.规则名称}
                                        </h6>
                                        <small class="text-muted">
                                            <i class="bi bi-geo-alt"></i> ${rec.城市 || '未知'} |
                                            <i class="bi bi-bookmark"></i> ${rec.规则来源 || '未知'}
                                        </small>
                                        <br>
                                        <small class="text-info">
                                            <i class="bi bi-tag"></i> <strong>匹配项目:</strong> ${rec.匹配项目 || '未知'}
                                        </small>
                                        ${rec.重复已采用规则 && rec.重复已采用规则.length > 0 && rec.状态 === '推荐' ? `
                                            <div class="mt-2">
                                                <div class="alert alert-danger py-2 px-3 mb-0 small border-start border-danger border-4">
                                                    <div class="d-flex align-items-center mb-2">
                                                        <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>
                                                        <strong class="text-danger">重复筛查警告</strong>
                                                    </div>
                                                    <div class="text-dark mb-2">
                                                        <strong>与已采用规则存在重复：</strong>类别相同，医保名称有交集
                                                    </div>
                                                    ${rec.重复已采用规则.map(dupRule => `
                                                        <div class="bg-light rounded p-2 mb-2 border-start border-warning border-3">
                                                            <div class="d-flex justify-content-between align-items-start">
                                                                <div class="flex-grow-1">
                                                                    <div class="fw-bold text-dark mb-1">
                                                                        <i class="bi bi-shield-check text-success me-1"></i>
                                                                        ${dupRule.规则名称}
                                                                    </div>
                                                                    <div class="small text-muted mb-1">
                                                                        <span class="badge bg-secondary me-1">${dupRule.规则类型}</span>
                                                                        <span class="text-muted">${dupRule.城市} | ${dupRule.规则来源}</span>
                                                                    </div>
                                                                    <div class="small">
                                                                        <strong class="text-warning">重复项目：</strong>
                                                                        <span class="text-danger fw-bold">${dupRule.重复项目.join('、')}</span>
                                                                    </div>
                                                                </div>
                                                                <button class="btn btn-outline-primary btn-sm ms-2"
                                                                        onclick="viewRuleDetail(${dupRule.规则ID}, '${dupRule.规则名称}')"
                                                                        title="查看已采用规则详情">
                                                                    <i class="bi bi-eye"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    `).join('')}
                                                    <div class="text-muted small mt-2">
                                                        <i class="bi bi-info-circle me-1"></i>
                                                        建议：请仔细比较规则内容，避免重复采用功能相同的规则
                                                    </div>
                                                </div>
                                            </div>
                                        ` : ''}
                                    </div>
                                    <div class="col-md-4">
                                        <p class="mb-1 small"><strong class="text-success">行为认定：</strong>${rec.行为认定}</p>
                                        <p class="mb-0 small text-muted" style="line-height: 1.3;">
                                            <strong class="text-primary">规则内涵：</strong>${rec.规则内涵 || '未设置'}
                                        </p>
                                    </div>
                                    <div class="col-md-1 text-center">
                                        <span class="match-score ${matchClass} h6">${(rec.匹配度 * 100).toFixed(1)}%</span>
                                        <br>
                                        <small class="text-muted">匹配度</small>
                                    </div>
                                    <div class="col-md-2 text-end">
                                        <div class="btn-group btn-group-sm">
                                            ${rec.状态 === '推荐' ? `
                                                <button class="btn btn-outline-success btn-sm" onclick="adoptRule(${rec.适用ID})" title="采用" ${isRuleDisabled ? 'disabled' : ''}>
                                                    <i class="bi bi-check"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="ignoreRule(${rec.适用ID})" title="忽略" ${isRuleDisabled ? 'disabled' : ''}>
                                                    <i class="bi bi-x"></i>
                                                </button>
                                            ` : rec.状态 === '已采用' ? `
                                                <button class="btn btn-outline-warning btn-sm" onclick="ignoreRule(${rec.适用ID})" title="取消采用">
                                                    <i class="bi bi-x-circle"></i>
                                                </button>
                                            ` : rec.状态 === '已忽略' ? `
                                                <button class="btn btn-outline-success btn-sm" onclick="adoptRule(${rec.适用ID})" title="恢复采用">
                                                    <i class="bi bi-arrow-clockwise"></i>
                                                </button>
                                            ` : ''}
                                            <button class="btn btn-outline-info btn-sm" onclick="viewRuleDetail(${rec.规则ID}, '${rec.规则名称}')" title="查看详情">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        ${isRuleDisabled ? `<br><small class="text-muted">${disabledReason}</small>` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';

            // 添加底部分页控件
            if (totalPages > 1) {
                html += `
                    <div class="d-flex justify-content-center mt-4">
                        <nav>
                            <ul class="pagination">
                                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1}); return false;">上一页</a>
                                </li>
                                ${generatePageNumbers(currentPage, totalPages)}
                                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1}); return false;">下一页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                `;
            }

            section.innerHTML = html;

            // 显示批量操作工具栏并重置为推荐状态
            const toolbar = document.getElementById('batchToolbar');
            toolbar.style.display = 'block';
            resetBatchToolbarForRecommendations();
            updateSelection();

            // 更新同规则ID记录的禁用状态（处理已采用状态的记录）
            updateSameRuleIdStatus();
        }

        // 生成页码
        function generatePageNumbers(current, total) {
            let html = '';
            const maxVisible = 5; // 最多显示5个页码

            let start = Math.max(1, current - Math.floor(maxVisible / 2));
            let end = Math.min(total, start + maxVisible - 1);

            // 调整起始位置
            if (end - start + 1 < maxVisible) {
                start = Math.max(1, end - maxVisible + 1);
            }

            for (let i = start; i <= end; i++) {
                html += `
                    <li class="page-item ${i === current ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
                    </li>
                `;
            }

            return html;
        }

        // 切换页面
        function changePage(page) {
            const totalPages = Math.ceil(allFilteredRecommendations.length / pageSize);
            if (page < 1 || page > totalPages) return;

            currentPage = page;
            renderCurrentPage();

            // 滚动到顶部
            document.getElementById('recommendationsSection').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // 改变每页显示数量
        function changePageSize(newSize) {
            pageSize = parseInt(newSize);
            currentPage = 1; // 重置到第一页
            renderCurrentPage();
        }

        // 检查规则ID是否应该被禁用（防重复选择）
        function checkRuleIdDisabled(ruleId, currentRecordId) {
            // 检查是否有同规则ID的记录已经是"已采用"状态（当前医院的记录中）
            const adoptedRecord = filteredRecommendations.find(r =>
                r.规则ID == ruleId &&
                r.适用ID != currentRecordId &&
                r.状态 === '已采用'
            );
            if (adoptedRecord) {
                return true; // 找到同规则ID的已采用记录（当前医院）
            }

            // 检查是否有同规则ID的其他记录已被选中
            const selectedCheckboxes = document.querySelectorAll('.rule-checkbox:checked');
            for (let checkbox of selectedCheckboxes) {
                const recordId = checkbox.value;
                if (recordId !== currentRecordId.toString()) {
                    // 查找对应的记录
                    const record = filteredRecommendations.find(r => r.适用ID == recordId);
                    if (record && record.规则ID == ruleId) {
                        return true; // 找到同规则ID的已选记录
                    }
                }
            }

            // 注意：不再检查全局已采用规则ID，因为用户应该能够手动勾选状态为"推荐"的规则
            // 即使该规则ID被其他医院采用，对当前医院来说仍然是可选的
            return false;
        }

        // 获取禁用原因
        function getDisabledReason(ruleId, currentRecordId) {
            // 检查是否有同规则ID的记录已经是"已采用"状态（当前医院的记录中）
            const adoptedRecord = filteredRecommendations.find(r =>
                r.规则ID == ruleId &&
                r.适用ID != currentRecordId &&
                r.状态 === '已采用'
            );
            if (adoptedRecord) {
                return `该规则ID已有记录被当前医院采用 (城市: ${adoptedRecord.城市 || '未知'})`;
            }

            // 检查是否有同规则ID的其他记录已被选中
            const selectedCheckboxes = document.querySelectorAll('.rule-checkbox:checked');
            for (let checkbox of selectedCheckboxes) {
                const recordId = checkbox.value;
                if (recordId !== currentRecordId.toString()) {
                    // 查找对应的记录
                    const record = filteredRecommendations.find(r => r.适用ID == recordId);
                    if (record && record.规则ID == ruleId) {
                        return `该规则ID已有记录被选择 (城市: ${record.城市 || '未知'})`;
                    }
                }
            }

            // 提供全局采用信息作为参考，但不禁用选择
            if (globalAdoptedRuleIds && globalAdoptedRuleIds.has(ruleId)) {
                return `该规则ID已被其他医院采用（仍可选择）`;
            }

            return '';
        }

        // 更新同规则ID记录的禁用状态
        function updateSameRuleIdStatus() {
            // 获取所有选中的记录
            const selectedCheckboxes = document.querySelectorAll('.rule-checkbox:checked');
            const selectedRuleIds = new Set();

            selectedCheckboxes.forEach(checkbox => {
                const recordId = checkbox.value;
                const record = filteredRecommendations.find(r => r.适用ID == recordId);
                if (record) {
                    selectedRuleIds.add(record.规则ID);
                }
            });

            // 获取所有已采用状态的规则ID
            const adoptedRuleIds = new Set();
            filteredRecommendations.forEach(record => {
                if (record.状态 === '已采用') {
                    adoptedRuleIds.add(record.规则ID);
                }
            });

            // 更新所有记录的禁用状态
            document.querySelectorAll('[data-rule-id]').forEach(element => {
                const ruleId = element.getAttribute('data-rule-id');
                const recordId = element.getAttribute('data-record-id');

                if (ruleId && recordId) {
                    const record = filteredRecommendations.find(r => r.适用ID == recordId);
                    if (record && record.是否多记录规则) {
                        // 检查是否应该禁用：
                        // 1. 有同规则ID的记录已被选中，且当前记录未被选中
                        // 2. 有同规则ID的记录已是"已采用"状态，且当前记录不是"已采用"状态
                        const isSelected = document.querySelector(`.rule-checkbox[value="${recordId}"]:checked`);
                        const shouldDisableBySelection = selectedRuleIds.has(record.规则ID) && !isSelected;
                        const shouldDisableByAdopted = adoptedRuleIds.has(record.规则ID) && record.状态 !== '已采用';

                        const shouldDisable = shouldDisableBySelection || shouldDisableByAdopted;

                        // 更新卡片样式
                        const card = element.querySelector('.rule-card');
                        if (shouldDisable) {
                            card.classList.add('rule-disabled');
                        } else {
                            card.classList.remove('rule-disabled');
                        }

                        // 更新复选框和按钮状态
                        const checkbox = element.querySelector('.rule-checkbox');
                        const buttons = element.querySelectorAll('.btn-outline-success, .btn-outline-secondary');

                        if (checkbox) checkbox.disabled = shouldDisable;
                        buttons.forEach(btn => btn.disabled = shouldDisable);
                    }
                }
            });
        }

        // 切换分组显示状态（已废弃，保留以兼容现有代码）
        function toggleGroupDisplay(groupId) {
            console.log('toggleGroupDisplay 已废弃，不再使用分组展开/收缩功能');
        }

        // 采用规则
        function adoptRule(适用ID) {
            // 保存当前滚动位置
            saveScrollPosition();

            // 查找对应的推荐记录
            const rec = filteredRecommendations.find(r => r.适用ID == 适用ID);

            // 检查是否为多记录规则ID，给出提示
            if (rec && rec.是否多记录规则) {
                const message = `该规则ID (${rec.规则ID}) 有 ${rec.规则ID分组总数} 条记录，您正在采用其中第 ${rec.规则ID分组序号} 条。\n\n` +
                              `规则名称: ${rec.规则名称}\n` +
                              `匹配项目: ${rec.匹配项目}\n` +
                              `城市: ${rec.城市}\n\n` +
                              `确定要采用这条记录吗？`;

                if (confirm(message)) {
                    updateRuleStatus(适用ID, '已采用');
                    setTimeout(() => scrollToRule(适用ID), 100);
                }
            } else {
                // 单记录规则
                updateRuleStatus(适用ID, '已采用');
                setTimeout(() => scrollToRule(适用ID), 100);
            }
        }

        // 忽略规则
        function ignoreRule(适用ID) {
            // 保存当前滚动位置
            saveScrollPosition();

            // 查找对应的推荐记录
            const rec = filteredRecommendations.find(r => r.适用ID == 适用ID);

            // 检查是否为多记录规则ID，给出提示
            if (rec && rec.是否多记录规则) {
                const message = `该规则ID (${rec.规则ID}) 有 ${rec.规则ID分组总数} 条记录，您正在忽略其中第 ${rec.规则ID分组序号} 条。\n\n` +
                              `规则名称: ${rec.规则名称}\n` +
                              `匹配项目: ${rec.匹配项目}\n` +
                              `城市: ${rec.城市}\n\n` +
                              `确定要忽略这条记录吗？`;

                if (confirm(message)) {
                    updateRuleStatus(适用ID, '已忽略');
                    setTimeout(() => scrollToRule(适用ID), 100);
                }
            } else {
                // 单记录规则
                updateRuleStatus(适用ID, '已忽略');
                setTimeout(() => scrollToRule(适用ID), 100);
            }
        }

        // 更新规则状态
        function updateRuleStatus(适用ID, status) {
            fetch('/api/hospital-rules/status', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    适用ID: 适用ID,
                    状态: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`规则状态已更新为：${status}`);

                    // 检查是否在重复规则审查模态框中
                    const duplicateModal = document.getElementById('duplicateAnalysisModal');
                    const isInDuplicateModal = duplicateModal && duplicateModal.classList.contains('show');

                    if (isInDuplicateModal) {
                        // 如果在重复规则审查中，延迟刷新分析结果
                        setTimeout(() => {
                            analyzeDuplicateRules();
                        }, 1000);
                    } else {
                        // 局部更新规则状态，不刷新整个页面
                        updateRuleCardStatus(适用ID, status);
                    }
                } else {
                    showToast('更新失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('更新规则状态失败:', error);
                showToast('更新规则状态失败', 'error');
            });
        }

        // 局部更新规则卡片状态
        function updateRuleCardStatus(适用ID, newStatus) {
            // 找到对应的规则卡片
            const ruleCard = document.querySelector(`[data-rule-id="${适用ID}"]`);
            if (!ruleCard) return;

            // 更新内存中的数据
            const ruleIndex = filteredRecommendations.findIndex(r => r.适用ID == 适用ID);
            if (ruleIndex !== -1) {
                filteredRecommendations[ruleIndex].状态 = newStatus;

                // 同时更新原始数据
                const originalIndex = originalRecommendations.findIndex(r => r.适用ID == 适用ID);
                if (originalIndex !== -1) {
                    originalRecommendations[originalIndex].状态 = newStatus;
                }
            }

            // 更新统计信息按钮（如果存在）
            if (typeof updateStatisticsButtons === 'function') {
                updateStatisticsButtons();
            }

            // 更新状态标签
            const statusTag = ruleCard.querySelector('.rule-tag');
            if (statusTag) {
                // 移除旧的状态类
                statusTag.classList.remove('tag-recommended', 'tag-adopted', 'tag-ignored');

                // 添加新的状态类和文本
                const statusClass = newStatus === '已采用' ? 'tag-adopted' :
                                  newStatus === '已忽略' ? 'tag-ignored' : 'tag-recommended';
                statusTag.classList.add(statusClass);
                statusTag.textContent = newStatus;
            }

            // 更新操作按钮
            const buttonGroup = ruleCard.querySelector('.btn-group');
            if (buttonGroup) {
                let newButtons = '';
                if (newStatus === '推荐') {
                    newButtons = `
                        <button class="btn btn-outline-success btn-sm" onclick="adoptRule(${适用ID})" title="采用">
                            <i class="bi bi-check"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="ignoreRule(${适用ID})" title="忽略">
                            <i class="bi bi-x"></i>
                        </button>
                    `;
                } else if (newStatus === '已采用') {
                    newButtons = `
                        <button class="btn btn-outline-warning btn-sm" onclick="ignoreRule(${适用ID})" title="取消采用">
                            <i class="bi bi-x-circle"></i>
                        </button>
                    `;
                } else if (newStatus === '已忽略') {
                    newButtons = `
                        <button class="btn btn-outline-success btn-sm" onclick="adoptRule(${适用ID})" title="重新采用">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    `;
                }

                // 保留查看详情按钮
                const rec = filteredRecommendations.find(r => r.适用ID == 适用ID);
                if (rec) {
                    newButtons += `
                        <button class="btn btn-outline-info btn-sm" onclick="viewRuleDetail(${rec.规则ID}, '${rec.规则名称}')" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                    `;
                }

                buttonGroup.innerHTML = newButtons;
            }

            // 更新复选框显示
            const checkbox = ruleCard.querySelector('.rule-checkbox');
            if (checkbox) {
                if (newStatus === '推荐' || newStatus === '已采用') {
                    checkbox.style.display = 'inline-block';
                } else {
                    checkbox.style.display = 'none';
                    checkbox.checked = false;
                }
            }

            // 更新统计信息
            updateFilterStats(filteredRecommendations);

            // 更新选择状态
            updateSelection();

            // 添加视觉反馈
            ruleCard.style.transition = 'all 0.3s ease';
            ruleCard.style.backgroundColor = '#e8f5e8';
            setTimeout(() => {
                ruleCard.style.backgroundColor = '';
            }, 1000);

            // 根据当前过滤类型检查是否需要重新过滤
            if (typeof currentFilterType !== 'undefined') {
                if (currentFilterType === 'duplicate') {
                    // 如果当前显示重复筛查警告规则，检查该规则是否还符合条件
                    const rule = originalRecommendations.find(r => r.适用ID == 适用ID);
                    if (rule && (rule.状态 !== '推荐' || !rule.重复已采用规则 || rule.重复已采用规则.length === 0)) {
                        // 规则不再符合重复筛查警告条件，从当前显示中移除
                        ruleCard.style.display = 'none';
                    }
                } else if (currentFilterType === 'clean') {
                    // 如果当前显示无重复推荐规则，检查该规则是否还符合条件
                    const rule = originalRecommendations.find(r => r.适用ID == 适用ID);
                    if (rule && (rule.状态 !== '推荐' || (rule.重复已采用规则 && rule.重复已采用规则.length > 0))) {
                        // 规则不再符合无重复推荐条件，从当前显示中移除
                        ruleCard.style.display = 'none';
                    }
                }
            }
        }

        // 更新选择状态
        function updateSelection() {
            const checkboxes = document.querySelectorAll('.rule-checkbox:checked');
            selectedRules.clear();

            // 收集所有选中的适用ID
            checkboxes.forEach(cb => {
                const ruleCard = cb.closest('.rule-card');
                const ruleId = ruleCard.dataset.ruleId;
                selectedRules.add(ruleId);
            });

            // 更新同规则ID记录的禁用状态
            updateSameRuleIdStatus();

            const count = selectedRules.size;
            const ruleCount = checkboxes.length;
            document.getElementById('selectedCount').textContent = `已选择 ${ruleCount} 条规则`;

            // 显示/隐藏批量操作工具栏
            const toolbar = document.getElementById('batchToolbar');
            if (count > 0) {
                toolbar.style.display = 'block';
            }
        }

        // 全选规则（当前页）
        function selectAllRules() {
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            checkboxes.forEach(cb => {
                // 只选择未被禁用的复选框
                if (!cb.disabled) {
                    cb.checked = true;
                }
            });
            updateSelection();
        }

        // 清除选择（所有页面）
        function clearSelection() {
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            checkboxes.forEach(cb => cb.checked = false);
            selectedRules.clear();
            updateSelection();
        }

        // 批量采用规则
        function batchAdoptRules() {
            if (selectedRules.size === 0) {
                showToast('请先选择要采用的规则', 'warning');
                return;
            }

            if (confirm(`确定要采用选中的 ${selectedRules.size} 条规则吗？`)) {
                batchUpdateRuleStatus('已采用');
            }
        }

        // 批量忽略规则
        function batchIgnoreRules() {
            if (selectedRules.size === 0) {
                showToast('请先选择要忽略的规则', 'warning');
                return;
            }

            if (confirm(`确定要忽略选中的 ${selectedRules.size} 条规则吗？`)) {
                batchUpdateRuleStatus('已忽略');
            }
        }

        // 批量更新规则状态
        function batchUpdateRuleStatus(status) {
            const ruleIds = Array.from(selectedRules);

            fetch('/api/hospital-rules/batch-status', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rule_ids: ruleIds,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(`成功${status} ${ruleIds.length} 条规则`);

                    // 检查是否在重复规则审查模态框中
                    const duplicateModal = document.getElementById('duplicateAnalysisModal');
                    const isInDuplicateModal = duplicateModal && duplicateModal.classList.contains('show');

                    if (isInDuplicateModal) {
                        // 如果在重复规则审查中，延迟刷新分析结果
                        setTimeout(() => {
                            analyzeDuplicateRules();
                        }, 1000);
                    } else {
                        // 批量局部更新规则状态
                        ruleIds.forEach(ruleId => {
                            updateRuleCardStatus(ruleId, status);
                        });
                    }

                    clearSelection();
                } else {
                    showToast('批量操作失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('批量更新规则状态失败:', error);
                showToast('批量操作失败', 'error');
            });
        }

        // 批量取消采用
        function batchCancelAdoption() {
            const checkboxes = document.querySelectorAll('.rule-checkbox:checked');
            if (checkboxes.length === 0) {
                alert('请先选择要取消采用的规则');
                return;
            }

            const confirmMessage = `确定要取消采用选中的 ${checkboxes.length} 条规则吗？\n\n此操作将把这些规则的状态改为"已忽略"，您可以稍后在规则列表中重新采用。`;
            if (confirm(confirmMessage)) {
                batchUpdateRuleStatus('已忽略');
            }
        }

        // 重置批量操作工具栏为推荐页面状态
        function resetBatchToolbarForRecommendations() {
            const toolbar = document.getElementById('batchToolbar');
            const adoptBtn = toolbar.querySelector('button[onclick="batchAdoptRules()"]');
            const ignoreBtn = toolbar.querySelector('button[onclick="batchIgnoreRules()"]');
            const duplicateBtn = document.getElementById('duplicateAnalysisBtn');

            if (adoptBtn) {
                adoptBtn.style.display = 'inline-block'; // 显示批量采用按钮
                adoptBtn.innerHTML = '<i class="bi bi-check-circle"></i> 批量采用';
                adoptBtn.onclick = function() { batchAdoptRules(); };
            }
            if (ignoreBtn) {
                ignoreBtn.innerHTML = '<i class="bi bi-x-circle"></i> 批量忽略';
                ignoreBtn.onclick = function() { batchIgnoreRules(); };
            }
            if (duplicateBtn) {
                duplicateBtn.style.display = 'none'; // 隐藏重复规则审查按钮
            }

            // 隐藏测试按钮
            const testBtn = document.getElementById('testDuplicateBtn');
            if (testBtn) {
                testBtn.style.display = 'none';
            }
        }

        // 设置批量操作工具栏为已采用页面状态
        function setBatchToolbarForAdopted() {
            const toolbar = document.getElementById('batchToolbar');
            const adoptBtn = toolbar.querySelector('button[onclick="batchAdoptRules()"]');
            const ignoreBtn = toolbar.querySelector('button[onclick="batchIgnoreRules()"]');
            const duplicateBtn = document.getElementById('duplicateAnalysisBtn');

            if (adoptBtn) {
                adoptBtn.style.display = 'none'; // 隐藏批量采用按钮
            }
            if (ignoreBtn) {
                ignoreBtn.innerHTML = '<i class="bi bi-x-circle"></i> 批量取消采用';
                ignoreBtn.onclick = function() { batchCancelAdoption(); };
            }
            if (duplicateBtn) {
                duplicateBtn.style.display = 'inline-block'; // 显示重复规则审查按钮
            }

            // 显示测试按钮（调试用）
            const testBtn = document.getElementById('testDuplicateBtn');
            if (testBtn) {
                testBtn.style.display = 'inline-block';
            }
        }

        // 测试重复规则分析API
        function testDuplicateAPI() {
            const hospitalId = document.getElementById('ruleHospitalSelect').value;
            if (!hospitalId) {
                alert('请先选择医院');
                return;
            }

            console.log('测试重复规则分析API...');

            // 先测试简单的测试API
            fetch(`/api/hospital-rules/duplicate-analysis-test/${hospitalId}`)
                .then(response => {
                    console.log('测试API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('测试API响应数据:', data);
                    if (data.success) {
                        alert('测试API工作正常！');
                        // 现在测试真正的API
                        testRealDuplicateAPI(hospitalId);
                    } else {
                        alert('测试API失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('测试API异常:', error);
                    alert('测试API异常: ' + error.message);
                });
        }

        function testRealDuplicateAPI(hospitalId) {
            console.log('测试真正的重复规则分析API...');

            fetch(`/api/hospital-rules/duplicate-analysis/${hospitalId}`)
                .then(response => {
                    console.log('真实API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('真实API响应数据:', data);
                    if (data.success) {
                        alert(`真实API工作正常！\n总规则数: ${data.total_rules}\n重复规则数: ${data.duplicate_rules}\n重复组数: ${data.duplicate_groups_count}`);
                    } else {
                        alert('真实API失败: ' + data.error);
                        if (data.details) {
                            console.error('详细错误信息:', data.details);
                        }
                    }
                })
                .catch(error => {
                    console.error('真实API异常:', error);
                    alert('真实API异常: ' + error.message);
                });
        }

        // 显示重复规则审查
        function showDuplicateAnalysis() {
            const hospitalId = document.getElementById('ruleHospitalSelect').value;
            if (!hospitalId) {
                alert('请先选择医院');
                return;
            }

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('duplicateAnalysisModal'));
            modal.show();

            // 重置内容
            const content = document.getElementById('duplicateAnalysisContent');
            content.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border" role="status"></div>
                    <p class="mt-2">正在分析重复规则...</p>
                </div>
            `;

            // 隐藏批量取消采用按钮
            document.getElementById('batchUnadoptBtn').style.display = 'none';

            // 调用API分析重复规则
            fetch(`/api/hospital-rules/duplicate-analysis/${hospitalId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderDuplicateAnalysis(data);
                    } else {
                        content.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle"></i>
                                分析失败: ${data.error}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('重复规则分析失败:', error);
                    content.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            分析失败，请稍后重试
                        </div>
                    `;
                });
        }

        // 渲染重复规则分析结果
        function renderDuplicateAnalysis(data) {
            const content = document.getElementById('duplicateAnalysisContent');

            if (data.duplicate_groups.length === 0) {
                content.innerHTML = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i>
                        <strong>恭喜！</strong> 该医院的已采用规则中未发现重复规则。
                        <br><small class="text-muted">共检查了 ${data.total_rules} 条已采用规则</small>
                    </div>
                `;
                return;
            }

            // 统计两大类别的分组数量（适应新的category格式，包含类型信息）
            const names1Groups = data.duplicate_groups.filter(g => g.category.includes('医保名称1重复')).length;
            const names2Groups = data.duplicate_groups.filter(g => g.category.includes('医保名称2重复')).length;

            let html = `
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>发现重复规则！</strong>
                    在 ${data.total_rules} 条已采用规则中，发现 ${data.duplicate_rules} 条规则存在重复，
                    共分为 ${data.duplicate_groups_count} 个重复组。
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <span class="me-2">
                                <strong>分类过滤:</strong>
                            </span>
                            <span class="badge bg-secondary me-2 filter-badge" onclick="filterDuplicateGroups('all')"
                                  id="filter-all" style="cursor: pointer;">
                                <i class="bi bi-list"></i> 全部显示: ${names1Groups + names2Groups} 组
                            </span>
                            <span class="badge bg-primary me-2 filter-badge" onclick="filterDuplicateGroups('医保名称1重复')"
                                  id="filter-names1" style="cursor: pointer;">
                                <i class="bi bi-1-circle"></i> 医保名称1重复: ${names1Groups} 组
                            </span>
                            <span class="badge bg-success me-2 filter-badge" onclick="filterDuplicateGroups('医保名称2重复')"
                                  id="filter-names2" style="cursor: pointer;">
                                <i class="bi bi-2-circle"></i> 医保名称2重复: ${names2Groups} 组
                            </span>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <button class="btn btn-outline-primary btn-sm me-2" onclick="expandAllGroups()">
                        <i class="bi bi-arrows-expand"></i> 展开所有
                    </button>
                    <button class="btn btn-outline-primary btn-sm me-2" onclick="collapseAllGroups()">
                        <i class="bi bi-arrows-collapse"></i> 折叠所有
                    </button>
                    <button class="btn btn-outline-success btn-sm me-2" onclick="selectAllDuplicates()">
                        <i class="bi bi-check-all"></i> 全选重复规则
                    </button>
                    <button class="btn btn-outline-secondary btn-sm me-2" onclick="clearDuplicateSelection()">
                        <i class="bi bi-x"></i> 清除选择
                    </button>
                    <button class="btn btn-danger btn-sm" id="batchUnadoptDuplicateBtn" onclick="batchUnadoptSelected()" style="display: none;">
                        <i class="bi bi-x-circle"></i> 批量取消采用
                    </button>
                </div>
            `;

            data.duplicate_groups.forEach((group, index) => {
                const groupId = `group_${index}`;
                const collapseId = `collapse_${index}`;

                // 确定分组类别的样式（适应新的category格式）
                const categoryClass = group.category.includes('医保名称1重复') ? 'bg-primary' : 'bg-success';
                const categoryIcon = group.category.includes('医保名称1重复') ? 'bi-1-circle' : 'bi-2-circle';

                html += `
                    <div class="card mb-3 duplicate-group" data-category="${group.category}">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h6 class="mb-0">
                                        <button class="btn btn-link text-decoration-none p-0" type="button"
                                                data-bs-toggle="collapse" data-bs-target="#${collapseId}"
                                                aria-expanded="false" aria-controls="${collapseId}">
                                            <i class="bi bi-chevron-right"></i>
                                            <i class="bi ${categoryIcon} me-1"></i>
                                            ${group.category} - 组 ${group.group_id}
                                            <span class="badge bg-danger ms-2">${group.rule_count} 条规则</span>
                                            <span class="badge ${categoryClass} ms-1">相似度 ${(group.similarity * 100).toFixed(0)}%</span>
                                        </button>
                                    </h6>
                                </div>
                                <div class="col-auto">
                                    <input type="checkbox" class="form-check-input group-checkbox"
                                           onchange="toggleGroupSelection('${groupId}')"
                                           id="group_check_${index}">
                                    <label class="form-check-label ms-1" for="group_check_${index}">选择整组</label>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div class="row">
                                    <div class="col-md-8">
                                        <small class="text-muted">
                                            <i class="bi bi-tags"></i> 分组依据重复项目:
                                            ${group.common_medical_names.map(name =>
                                                `<span class="badge bg-warning text-dark me-1">${name}</span>`
                                            ).join('')}
                                        </small>
                                        ${(group.cross_field_duplicates && group.cross_field_duplicates.length > 0) ? `
                                            <br><small class="text-muted mt-1">
                                                <i class="bi bi-plus-circle"></i> 组内其他重复项目:
                                                ${group.cross_field_duplicates.map(name =>
                                                    `<span class="badge bg-secondary text-white me-1">${name}</span>`
                                                ).join('')}
                                            </small>
                                        ` : ''}
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">
                                            <i class="bi bi-diagram-3"></i> 共享对照ID:
                                            ${(group.compare_ids || []).map(id =>
                                                `<span class="badge bg-info me-1">${id}</span>`
                                            ).join('')}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="collapse" id="${collapseId}">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th width="40">选择</th>
                                                <th>规则名称</th>
                                                <th>医保项目名称1</th>
                                                <th>医保项目名称2</th>
                                                <th width="80">违规数量</th>
                                                <th width="80">城市</th>
                                                <th width="120">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="${groupId}">
                `;

                group.rules.forEach(rule => {
                    html += `
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input rule-duplicate-checkbox"
                                       value="${rule.适用ID}" data-group="${groupId}">
                            </td>
                            <td>
                                <strong>${rule.规则名称 || '未知'}</strong>
                                <br><small class="text-muted">ID: ${rule.规则ID}</small>
                            </td>
                            <td>
                                ${highlightCommonNames(rule.医保名称1 || '',
                                    [...(group.common_medical_names || []), ...(group.cross_field_duplicates || [])],
                                    '医保名称1')}
                            </td>
                            <td>
                                ${highlightCommonNames(rule.医保名称2 || '',
                                    [...(group.common_medical_names || []), ...(group.cross_field_duplicates || [])],
                                    '医保名称2')}
                            </td>
                            <td>
                                <span class="badge bg-warning text-dark">
                                    ${rule.违规数量 || 0}
                                </span>
                            </td>
                            <td>${rule.城市 || '未知'}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-danger btn-sm"
                                            onclick="ignoreRule(${rule.适用ID})"
                                            title="取消采用">
                                        <i class="bi bi-x-circle"></i>
                                    </button>
                                    <button class="btn btn-outline-info btn-sm"
                                            onclick="viewRuleDetail(${rule.规则ID}, '${rule.规则名称}')"
                                            title="查看规则">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });

                html += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            content.innerHTML = html;

            // 添加事件监听器
            document.querySelectorAll('.rule-duplicate-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateDuplicateSelection);
            });

            // 添加折叠面板状态变化的事件监听器
            document.querySelectorAll('.duplicate-group .collapse').forEach(collapse => {
                collapse.addEventListener('show.bs.collapse', function() {
                    const button = this.parentElement.querySelector('.btn-link');
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) {
                            icon.className = 'bi bi-chevron-down';
                        }
                        button.setAttribute('aria-expanded', 'true');
                    }
                });

                collapse.addEventListener('hide.bs.collapse', function() {
                    const button = this.parentElement.querySelector('.btn-link');
                    if (button) {
                        const icon = button.querySelector('i');
                        if (icon) {
                            icon.className = 'bi bi-chevron-right';
                        }
                        button.setAttribute('aria-expanded', 'false');
                    }
                });
            });

            // 显示批量取消采用按钮
            document.getElementById('batchUnadoptBtn').style.display = 'inline-block';
            document.getElementById('batchUnadoptDuplicateBtn').style.display = 'inline-block';
            updateDuplicateSelection(); // 初始化按钮状态
        }

        // 高亮显示共同医保项目名称
        function highlightCommonNames(text, commonNames, fieldType) {
            if (!text || !commonNames || commonNames.length === 0) {
                return text || '';
            }

            let highlightedText = text;

            // 根据字段类型和项目来源确定高亮样式
            const getHighlightClass = (fieldType, itemSource) => {
                // 如果是分组依据的重复项目（主要重复项目）
                if (itemSource === 'primary') {
                    switch (fieldType) {
                        case '医保名称1':
                            return 'bg-primary text-white';  // 蓝色背景
                        case '医保名称2':
                            return 'bg-success text-white';  // 绿色背景
                        default:
                            return 'bg-warning text-dark';   // 默认黄色背景
                    }
                }
                // 如果是交叉字段的重复项目（次要重复项目）
                else if (itemSource === 'cross') {
                    switch (fieldType) {
                        case '医保名称1':
                            return 'bg-info text-white';     // 浅蓝色背景
                        case '医保名称2':
                            return 'bg-dark text-white';     // 深色背景
                        default:
                            return 'bg-secondary text-white'; // 灰色背景
                    }
                }
                return 'bg-warning text-dark';  // 默认样式
            };

            // 分离主要重复项目和交叉字段重复项目
            const primaryItems = commonNames.filter(name =>
                name.includes('分组依据') || !name.includes('组内其他')
            );
            const crossFieldItems = commonNames.filter(name =>
                name.includes('组内其他') || (!name.includes('分组依据') && name.startsWith(`${fieldType}:`))
            );

            // 处理主要重复项目（分组依据）
            primaryItems.forEach(name => {
                try {
                    // 检查这个共同项目是否属于当前字段类型
                    const isCurrentFieldType = name.startsWith(`${fieldType}:`);

                    if (isCurrentFieldType) {
                        // 提取实际的项目名称（去掉字段前缀）
                        const actualName = name.replace(`${fieldType}: `, '');

                        // 转义正则表达式特殊字符
                        const escapedName = actualName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        const regex = new RegExp(`(${escapedName})`, 'gi');
                        const highlightClass = getHighlightClass(fieldType, 'primary');

                        highlightedText = highlightedText.replace(regex, `<mark class="${highlightClass}" title="分组依据重复项目">$1</mark>`);
                    }
                } catch (error) {
                    console.warn('主要重复项目高亮失败:', name, '错误:', error);
                    // 降级处理...
                }
            });

            // 处理交叉字段重复项目（组内其他重复）
            crossFieldItems.forEach(name => {
                try {
                    // 检查这个项目是否属于当前字段类型
                    const isCurrentFieldType = name.startsWith(`${fieldType}:`);

                    if (isCurrentFieldType) {
                        // 提取实际的项目名称（去掉字段前缀）
                        const actualName = name.replace(`${fieldType}: `, '');

                        // 转义正则表达式特殊字符
                        const escapedName = actualName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        const regex = new RegExp(`(${escapedName})`, 'gi');
                        const highlightClass = getHighlightClass(fieldType, 'cross');

                        highlightedText = highlightedText.replace(regex, `<mark class="${highlightClass}" title="组内其他重复项目">$1</mark>`);
                    }
                } catch (error) {
                    console.warn('交叉字段重复项目高亮失败:', name, '错误:', error);
                    // 降级处理...
                }
            });

            return highlightedText;
        }

        // 展开所有分组
        function expandAllGroups() {
            const collapses = document.querySelectorAll('.duplicate-group .collapse');
            const buttons = document.querySelectorAll('.duplicate-group .btn-link');

            collapses.forEach((collapse, index) => {
                const bsCollapse = new bootstrap.Collapse(collapse, { show: true });
                // 更新按钮图标和状态
                if (buttons[index]) {
                    const icon = buttons[index].querySelector('i');
                    if (icon) {
                        icon.className = 'bi bi-chevron-down';
                    }
                    buttons[index].setAttribute('aria-expanded', 'true');
                }
            });
        }

        // 折叠所有分组
        function collapseAllGroups() {
            const collapses = document.querySelectorAll('.duplicate-group .collapse');
            const buttons = document.querySelectorAll('.duplicate-group .btn-link');

            collapses.forEach((collapse, index) => {
                const bsCollapse = new bootstrap.Collapse(collapse, { hide: true });
                // 更新按钮图标和状态
                if (buttons[index]) {
                    const icon = buttons[index].querySelector('i');
                    if (icon) {
                        icon.className = 'bi bi-chevron-right';
                    }
                    buttons[index].setAttribute('aria-expanded', 'false');
                }
            });
        }

        // 选择所有重复规则（只选择可见分组中的规则）
        function selectAllDuplicates() {
            const visibleGroups = document.querySelectorAll('.duplicate-group[style*="display: block"], .duplicate-group:not([style*="display: none"])');
            visibleGroups.forEach(group => {
                const checkboxes = group.querySelectorAll('.rule-duplicate-checkbox');
                checkboxes.forEach(cb => cb.checked = true);

                // 同时选中分组复选框
                const groupCheckbox = group.querySelector('.group-checkbox');
                if (groupCheckbox) {
                    groupCheckbox.checked = true;
                }
            });
            updateDuplicateSelection();
        }

        // 清除重复规则选择（只清除可见分组中的选择）
        function clearDuplicateSelection() {
            const visibleGroups = document.querySelectorAll('.duplicate-group[style*="display: block"], .duplicate-group:not([style*="display: none"])');
            visibleGroups.forEach(group => {
                const checkboxes = group.querySelectorAll('.rule-duplicate-checkbox, .group-checkbox');
                checkboxes.forEach(cb => cb.checked = false);
            });
            updateDuplicateSelection();
        }

        // 切换分组选择
        function toggleGroupSelection(groupId) {
            const groupCheckbox = document.querySelector(`#group_check_${groupId.split('_')[1]}`);
            const ruleCheckboxes = document.querySelectorAll(`[data-group="${groupId}"]`);

            ruleCheckboxes.forEach(cb => {
                cb.checked = groupCheckbox.checked;
            });

            updateDuplicateSelection();
        }

        // 过滤重复规则分组
        function filterDuplicateGroups(category) {
            const allGroups = document.querySelectorAll('.duplicate-group');
            const filterBadges = document.querySelectorAll('.filter-badge');

            // 更新过滤按钮状态
            filterBadges.forEach(badge => {
                badge.classList.remove('bg-dark');
                badge.classList.add('bg-secondary');
            });

            // 根据类别显示/隐藏分组（适应新的category格式）
            let visibleCount = 0;
            allGroups.forEach(group => {
                const groupCategory = group.getAttribute('data-category');

                if (category === 'all' ||
                    groupCategory === category ||
                    groupCategory.includes(category)) {
                    group.style.display = 'block';
                    visibleCount++;
                } else {
                    group.style.display = 'none';
                }
            });

            // 高亮当前选中的过滤器（适应新的category格式）
            let activeFilterId = 'filter-all';
            if (category.includes('医保名称1重复')) {
                activeFilterId = 'filter-names1';
            } else if (category.includes('医保名称2重复')) {
                activeFilterId = 'filter-names2';
            }

            const activeFilter = document.getElementById(activeFilterId);
            if (activeFilter) {
                activeFilter.classList.remove('bg-secondary');
                activeFilter.classList.add('bg-dark');
            }

            // 显示过滤结果提示
            const resultText = category === 'all' ? '显示全部分组' : `显示 ${category} 分组`;
            console.log(`过滤结果: ${resultText}，共 ${visibleCount} 个分组可见`);

            // 更新选择状态（因为可见的复选框可能发生变化）
            updateDuplicateSelection();
        }

        // 更新重复规则选择状态
        function updateDuplicateSelection() {
            // 只统计可见分组中的选中复选框
            const visibleGroups = document.querySelectorAll('.duplicate-group[style*="display: block"], .duplicate-group:not([style*="display: none"])');
            let selectedCount = 0;

            visibleGroups.forEach(group => {
                const checkboxes = group.querySelectorAll('.rule-duplicate-checkbox:checked');
                selectedCount += checkboxes.length;
            });

            const batchBtn = document.getElementById('batchUnadoptBtn');
            const batchDuplicateBtn = document.getElementById('batchUnadoptDuplicateBtn');

            if (selectedCount > 0) {
                const text = `批量取消采用 (${selectedCount})`;
                batchBtn.innerHTML = `<i class="bi bi-x-circle"></i> ${text}`;
                batchBtn.disabled = false;
                batchDuplicateBtn.innerHTML = `<i class="bi bi-x-circle"></i> ${text}`;
                batchDuplicateBtn.disabled = false;
            } else {
                batchBtn.innerHTML = '<i class="bi bi-x-circle"></i> 批量取消采用';
                batchBtn.disabled = true;
                batchDuplicateBtn.innerHTML = '<i class="bi bi-x-circle"></i> 批量取消采用';
                batchDuplicateBtn.disabled = true;
            }
        }

        // 批量取消采用选中的重复规则
        function batchUnadoptSelected() {
            const selectedCheckboxes = document.querySelectorAll('.rule-duplicate-checkbox:checked');
            if (selectedCheckboxes.length === 0) {
                alert('请先选择要取消采用的规则');
                return;
            }

            const ruleIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));
            const confirmMessage = `确定要取消采用选中的 ${ruleIds.length} 条重复规则吗？\n\n此操作将把这些规则的状态改为"已忽略"，可以在规则列表中重新采用。`;

            if (!confirm(confirmMessage)) {
                return;
            }

            // 显示加载状态
            const batchBtn = document.getElementById('batchUnadoptBtn');
            const originalText = batchBtn.innerHTML;
            batchBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';
            batchBtn.disabled = true;

            // 调用API批量取消采用
            fetch('/api/hospital-rules/batch-unadopt', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rule_ids: ruleIds,
                    reason: '重复规则审查-批量取消采用'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`成功取消采用 ${data.updated_count} 条规则` +
                          (data.failed_count > 0 ? `，失败 ${data.failed_count} 条` : ''));

                    // 重新加载重复规则分析
                    showDuplicateAnalysis();

                    // 刷新已采用规则列表
                    const hospitalId = document.getElementById('ruleHospitalSelect').value;
                    if (hospitalId) {
                        loadAdoptedRulesDirectly(hospitalId);
                    }
                } else {
                    alert(`操作失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('批量取消采用失败:', error);
                alert('操作失败，请稍后重试');
            })
            .finally(() => {
                batchBtn.innerHTML = originalText;
                batchBtn.disabled = false;
            });
        }

        // 规则分组和排序 - 按规则ID分组，取消合并显示
        function groupAndSortRecommendations(recommendations) {
            // 第一步：按规则ID分组
            const ruleGroups = {};

            recommendations.forEach(rec => {
                const ruleId = rec.规则ID || 'unknown';
                if (!ruleGroups[ruleId]) {
                    ruleGroups[ruleId] = [];
                }
                ruleGroups[ruleId].push(rec);
            });

            // 第二步：对每个分组内的规则进行排序和处理
            const sortedRecommendations = [];

            // 按规则ID排序分组
            const sortedRuleIds = Object.keys(ruleGroups).sort((a, b) => {
                // 数字规则ID按数值排序，其他按字符串排序
                const numA = parseInt(a);
                const numB = parseInt(b);
                if (!isNaN(numA) && !isNaN(numB)) {
                    return numA - numB;
                }
                return a.localeCompare(b);
            });

            let colorIndex = 0;
            sortedRuleIds.forEach(ruleId => {
                const group = ruleGroups[ruleId];

                // 对同规则ID的记录进行排序
                group.sort((a, b) => {
                    // 状态优先级：已采用 > 推荐 > 已忽略
                    const statusPriority = { '已采用': 3, '推荐': 2, '已忽略': 1 };
                    const statusDiff = (statusPriority[b.状态] || 0) - (statusPriority[a.状态] || 0);
                    if (statusDiff !== 0) return statusDiff;

                    // 匹配度降序
                    const matchDiff = (b.匹配度 || 0) - (a.匹配度 || 0);
                    if (matchDiff !== 0) return matchDiff;

                    // 匹配项目名称升序
                    const itemA = a.匹配项目 || '';
                    const itemB = b.匹配项目 || '';
                    const itemDiff = itemA.localeCompare(itemB);
                    if (itemDiff !== 0) return itemDiff;

                    // 城市名称升序
                    const cityA = a.城市 || '未知';
                    const cityB = b.城市 || '未知';
                    const cityDiff = cityA.localeCompare(cityB);
                    if (cityDiff !== 0) return cityDiff;

                    // 规则来源升序
                    const sourceA = a.规则来源 || '未知';
                    const sourceB = b.规则来源 || '未知';
                    return sourceA.localeCompare(sourceB);
                });

                // 为同规则ID的记录添加分组信息和颜色标识
                const colorClass = `rule-color-${(colorIndex % 6) + 1}`;
                if (group.length > 1) {
                    colorIndex++; // 只有多条记录的规则ID才使用颜色区分
                }

                group.forEach((rec, index) => {
                    rec.规则ID分组序号 = index + 1;
                    rec.规则ID分组总数 = group.length;
                    rec.是否规则ID首个 = index === 0;
                    rec.规则ID标识 = ruleId;
                    rec.颜色类名 = group.length > 1 ? colorClass : '';
                    rec.是否多记录规则 = group.length > 1;

                    // 保持原有的分组信息以兼容现有代码
                    rec.分组序号 = index + 1;
                    rec.分组总数 = group.length;
                    rec.是否分组首个 = index === 0;
                    rec.分组标识 = `rule_${ruleId}_group`;
                    rec.分组类型 = '规则ID';
                });

                // 添加到结果中
                sortedRecommendations.push(...group);
            });

            return sortedRecommendations;
        }

        // 保存页面滚动位置
        function saveScrollPosition() {
            sessionStorage.setItem('hospitalRulesScrollPosition', window.pageYOffset);
        }

        // 恢复页面滚动位置
        function restoreScrollPosition() {
            const scrollPosition = sessionStorage.getItem('hospitalRulesScrollPosition');
            if (scrollPosition) {
                window.scrollTo(0, parseInt(scrollPosition));
                sessionStorage.removeItem('hospitalRulesScrollPosition');
            }
        }

        // 滚动到指定规则
        function scrollToRule(适用ID) {
            const ruleCard = document.querySelector(`[data-rule-id="${适用ID}"]`);
            if (ruleCard) {
                ruleCard.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // 添加高亮效果
                ruleCard.style.transition = 'all 0.5s ease';
                ruleCard.style.boxShadow = '0 0 20px rgba(13, 202, 240, 0.5)';
                setTimeout(() => {
                    ruleCard.style.boxShadow = '';
                }, 2000);
            }
        }

        // 查看规则详情
        function viewRuleDetail(规则ID, 规则名称) {
            // 打开新窗口显示规则详情，并过滤到该规则
            const url = `/rule_knowledge_base?rule_id=${规则ID}&filter_name=${encodeURIComponent(规则名称)}`;
            window.open(url, '_blank');
        }

        // 初始化过滤器选项
        function initializeFilters(recommendations) {
            // 提取唯一值 - 城市需要拆分合并的城市名称
            const allCities = [];
            const allSources = [];

            recommendations.forEach(r => {
                // 拆分城市（可能是"西安、沈阳"这样的合并格式）
                if (r.城市 && r.城市 !== '未知') {
                    const cityList = r.城市.split('、').map(c => c.trim()).filter(c => c);
                    allCities.push(...cityList);
                }

                // 拆分规则来源
                if (r.规则来源 && r.规则来源 !== '未知') {
                    const sourceList = r.规则来源.split('、').map(s => s.trim()).filter(s => s);
                    allSources.push(...sourceList);
                }
            });

            const cities = [...new Set(allCities)].sort();
            const sources = [...new Set(allSources)].sort();
            const types = [...new Set(recommendations.map(r => r.类型).filter(t => t && t !== '未知'))].sort();
            const ruleCategories = [...new Set(recommendations.map(r => r.规则类型).filter(t => t && t !== '未知'))].sort();

            // 填充城市选项
            const citySelect = document.getElementById('filterCity');
            citySelect.innerHTML = '<option value="">全部城市</option>';
            cities.forEach(city => {
                const option = document.createElement('option');
                option.value = city;
                option.textContent = city;
                citySelect.appendChild(option);
            });

            // 填充规则来源选项
            const sourceSelect = document.getElementById('filterSource');
            sourceSelect.innerHTML = '<option value="">全部来源</option>';
            sources.forEach(source => {
                const option = document.createElement('option');
                option.value = source;
                option.textContent = source;
                sourceSelect.appendChild(option);
            });

            // 填充类型选项
            const typeSelect = document.getElementById('filterType');
            typeSelect.innerHTML = '<option value="">全部类型</option>';
            types.forEach(type => {
                const option = document.createElement('option');
                option.value = type;
                option.textContent = type;
                typeSelect.appendChild(option);
            });

            // 填充规则类型选项
            const ruleCategorySelect = document.getElementById('filterRuleCategory');
            ruleCategorySelect.innerHTML = '<option value="">全部规则类型</option>';
            ruleCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                ruleCategorySelect.appendChild(option);
            });

            // 更新统计信息
            updateFilterStats(recommendations);
        }

        // 应用过滤器
        function applyFilters() {
            const cityFilter = document.getElementById('filterCity').value;
            const sourceFilter = document.getElementById('filterSource').value;
            const typeFilter = document.getElementById('filterType').value;
            const ruleCategoryFilter = document.getElementById('filterRuleCategory').value;
            const nameFilter = document.getElementById('filterRuleName').value.toLowerCase();
            const statusFilter = document.getElementById('filterStatus').value;
            const matchScoreFilter = document.getElementById('filterMatchScore').value;

            // 根据当前统计按钮过滤状态选择基础数据源
            let baseData = baseFilteredRecommendations.length > 0 ? baseFilteredRecommendations : originalRecommendations;

            filteredRecommendations = baseData.filter(rec => {
                // 城市过滤 - 使用包含匹配
                if (cityFilter && (!rec.城市 || !rec.城市.includes(cityFilter))) return false;

                // 来源过滤 - 使用包含匹配
                if (sourceFilter && (!rec.规则来源 || !rec.规则来源.includes(sourceFilter))) return false;

                // 类型过滤
                if (typeFilter && rec.类型 !== typeFilter) return false;

                // 规则类型过滤
                if (ruleCategoryFilter && rec.规则类型 !== ruleCategoryFilter) return false;

                // 名称过滤
                if (nameFilter && !rec.规则名称.toLowerCase().includes(nameFilter)) return false;

                // 状态过滤
                if (statusFilter && rec.状态 !== statusFilter) return false;

                // 匹配度过滤
                if (matchScoreFilter) {
                    const score = rec.匹配度;
                    if (matchScoreFilter === 'high' && score < 0.8) return false;
                    if (matchScoreFilter === 'medium' && (score < 0.5 || score >= 0.8)) return false;
                    if (matchScoreFilter === 'low' && score >= 0.5) return false;
                }

                return true;
            });

            // 重新渲染
            renderFilteredRecommendations(filteredRecommendations);
            updateFilterStats(filteredRecommendations);

            // 显示批量操作工具栏并重置按钮
            const toolbar = document.getElementById('batchToolbar');
            toolbar.style.display = 'block';

            // 重置批量操作按钮为推荐页面状态
            resetBatchToolbarForRecommendations();
            updateSelection();
        }

        // 清除过滤器
        function clearFilters() {
            document.getElementById('filterCity').value = '';
            document.getElementById('filterSource').value = '';
            document.getElementById('filterType').value = '';
            document.getElementById('filterRuleCategory').value = '';
            document.getElementById('filterRuleName').value = '';
            document.getElementById('filterStatus').value = '';
            document.getElementById('filterMatchScore').value = '';

            filteredRecommendations = originalRecommendations;
            renderFilteredRecommendations(filteredRecommendations);
            updateFilterStats(filteredRecommendations);
        }

        // 更新过滤统计信息
        function updateFilterStats(recommendations) {
            const stats = document.getElementById('filterStats');
            const total = recommendations.length;
            const recommended = recommendations.filter(r => r.状态 === '推荐').length;
            const adopted = recommendations.filter(r => r.状态 === '已采用').length;
            const ignored = recommendations.filter(r => r.状态 === '已忽略').length;

            stats.innerHTML = `
                总计：${total} 条规则 |
                推荐：${recommended} 条 |
                已采用：${adopted} 条 |
                已忽略：${ignored} 条
            `;
        }

        // 加载医院已采用规则
        function loadHospitalAdoptedRules(hospitalId) {
            fetch(`/api/hospital-rules/adopted/${hospitalId}`)
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById(`adoptedRules_${hospitalId}`);
                    if (data.success && data.rules && data.rules.length > 0) {
                        // 去重规则名称（因为可能有相同规则名称的不同记录）
                        const uniqueRuleNames = [...new Set(data.rules.map(r => r.规则名称))];
                        const displayNames = uniqueRuleNames.slice(0, 2); // 显示前2个规则名称
                        const moreCount = uniqueRuleNames.length - 2;

                        let html = `<small class="text-success">
                            <i class="bi bi-check-circle"></i> 已采用 ${uniqueRuleNames.length} 条规则`;

                        if (displayNames.length > 0) {
                            html += `：${displayNames.join('、')}`;
                            if (moreCount > 0) {
                                html += ` 等${moreCount}条`;
                            }
                        }

                        html += '</small>';
                        container.innerHTML = html;
                    } else {
                        container.innerHTML = '<small class="text-muted"><i class="bi bi-info-circle"></i> 暂无已采用规则</small>';
                    }
                })
                .catch(error => {
                    console.error('加载已采用规则失败:', error);
                    const container = document.getElementById(`adoptedRules_${hospitalId}`);
                    container.innerHTML = '<small class="text-muted">加载失败</small>';
                });
        }

        // 查看医院已采用规则
        function viewAdoptedRules(hospitalId) {
            // 切换到规则推荐标签页
            const rulesTab = new bootstrap.Tab(document.getElementById('rules-tab'));
            rulesTab.show();

            // 设置医院选择
            document.getElementById('ruleHospitalSelect').value = hospitalId;

            // 直接加载已采用规则
            loadAdoptedRulesDirectly(hospitalId);
        }

        // 查看医院所有规则
        function viewAllRules(hospitalId) {
            // 切换到规则推荐标签页
            const rulesTab = new bootstrap.Tab(document.getElementById('rules-tab'));
            rulesTab.show();

            // 设置医院选择
            document.getElementById('ruleHospitalSelect').value = hospitalId;

            // 直接加载所有规则
            loadAllRulesDirectly(hospitalId);
        }

        // 直接加载所有规则
        function loadAllRulesDirectly(hospitalId) {
            const section = document.getElementById('recommendationsSection');
            section.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在加载所有规则...</p></div>';

            // 清理之前的数据
            originalRecommendations = [];
            filteredRecommendations = [];
            selectedRules.clear();

            // 先加载全局已采用规则ID，然后加载所有规则
            loadGlobalAdoptedRuleIds().then(() => {
                return fetch(`/api/hospital-rules/all/${hospitalId}`);
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 转换为推荐格式
                        const allRules = data.rules.map(rule => {
                            // 直接使用API返回的匹配项目字段，不再从推荐原因中提取
                            let matchedItem = rule.匹配项目 || '未知';

                            // 如果匹配项目为空或未知，则根据状态设置默认值
                            if (!matchedItem || matchedItem === '未知') {
                                matchedItem = rule.状态 === '已采用' ? '已采用规则' : rule.状态 === '已忽略' ? '已忽略规则' : '推荐规则';
                            }

                            return {
                                '适用ID': rule.适用ID,
                                '规则ID': rule.规则ID,
                                '规则名称': rule.规则名称,
                                '行为认定': rule.行为认定,
                                '类型': rule.类型 || '未知',
                                '规则类型': rule.规则类型 || '未知',
                                '规则内涵': rule.规则内涵 || '未设置',
                                '匹配度': rule.匹配度 || 1.0,
                                '推荐原因': rule.推荐原因,
                                '状态': rule.状态 || '推荐',
                                '匹配项目': matchedItem,
                                '城市': rule.城市 || '未知',
                                '规则来源': rule.规则来源 || '未知',
                                // 添加分组相关字段以避免JavaScript错误
                                '分组标识': matchedItem,
                                '分组序号': 1,
                                '分组总数': 1,
                                '是否分组首个': true,
                                '合并规则数量': 1
                            };
                        });

                        // 显示所有规则
                        originalRecommendations = allRules;
                        filteredRecommendations = allRules;

                        if (allRules.length > 0) {
                            // 获取医院名称用于显示
                            const hospitalSelect = document.getElementById('ruleHospitalSelect');
                            const hospitalName = hospitalSelect.options[hospitalSelect.selectedIndex].text;

                            // 计算统计信息
                            const recommendedRules = allRules.filter(rule => rule.状态 === '推荐');
                            const duplicateWarningRules = recommendedRules.filter(rule =>
                                rule.重复已采用规则 && rule.重复已采用规则.length > 0
                            );
                            const cleanRecommendedRules = recommendedRules.filter(rule =>
                                !rule.重复已采用规则 || rule.重复已采用规则.length === 0
                            );

                            // 在规则列表前添加医院信息提示和统计按钮
                            const hospitalInfo = document.createElement('div');
                            hospitalInfo.className = 'alert alert-primary mb-3';
                            hospitalInfo.innerHTML = `
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="bi bi-hospital"></i>
                                        <strong>当前查看医院：${hospitalName}</strong>
                                        <span class="badge bg-primary ms-2">${allRules.length} 条规则</span>
                                    </div>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-warning btn-sm"
                                                onclick="filterDuplicateWarningRules()"
                                                title="显示有重复筛查警告的推荐规则">
                                            <i class="bi bi-exclamation-triangle"></i>
                                            重复筛查警告 <span class="badge bg-light text-dark">${duplicateWarningRules.length}</span>
                                        </button>
                                        <button type="button" class="btn btn-info btn-sm"
                                                onclick="filterCleanRecommendedRules()"
                                                title="显示没有重复筛查警告的推荐规则">
                                            <i class="bi bi-lightbulb"></i>
                                            无重复推荐 <span class="badge bg-light text-dark">${cleanRecommendedRules.length}</span>
                                        </button>
                                        <button type="button" class="btn btn-secondary btn-sm"
                                                onclick="showAllRules()"
                                                title="显示所有规则">
                                            <i class="bi bi-list"></i>
                                            显示全部
                                        </button>
                                    </div>
                                </div>
                            `;
                            section.appendChild(hospitalInfo);

                            // 显示过滤器并初始化
                            document.getElementById('filterSection').style.display = 'block';
                            initializeFilters(allRules);

                            renderFilteredRecommendations(allRules);

                            // 显示批量操作工具栏，并设置为推荐页面状态
                            const toolbar = document.getElementById('batchToolbar');
                            toolbar.style.display = 'block';
                            resetBatchToolbarForRecommendations();
                        } else {
                            // 获取医院名称用于显示
                            const hospitalSelect = document.getElementById('ruleHospitalSelect');
                            const hospitalName = hospitalSelect.options[hospitalSelect.selectedIndex].text;

                            section.innerHTML = `<div class="alert alert-info">
                                <i class="bi bi-hospital"></i>
                                <strong>${hospitalName}</strong> 暂无规则记录
                            </div>`;
                            document.getElementById('filterSection').style.display = 'none';
                            document.getElementById('batchToolbar').style.display = 'none';
                        }
                    } else {
                        section.innerHTML = '<div class="alert alert-danger">加载失败: ' + (data.error || '未知错误') + '</div>';
                        document.getElementById('filterSection').style.display = 'none';
                        document.getElementById('batchToolbar').style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('加载所有规则失败:', error);
                    section.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
                    document.getElementById('filterSection').style.display = 'none';
                    document.getElementById('batchToolbar').style.display = 'none';
                });
        }

        // 直接加载已采用规则
        function loadAdoptedRulesDirectly(hospitalId) {
            const section = document.getElementById('recommendationsSection');
            section.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在加载已采用规则...</p></div>';

            // 清理之前的数据
            originalRecommendations = [];
            filteredRecommendations = [];
            selectedRules.clear();

            // 先加载全局已采用规则ID，然后加载已采用规则
            loadGlobalAdoptedRuleIds().then(() => {
                return fetch(`/api/hospital-rules/adopted/${hospitalId}`);
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 转换为推荐格式
                        const adoptedRules = data.rules.map(rule => {
                            // 直接使用API返回的匹配项目字段，不再从推荐原因中提取
                            let matchedItem = rule.匹配项目 || '已采用规则';

                            // 如果匹配项目为空或未知，则设置为默认值
                            if (!matchedItem || matchedItem === '未知') {
                                matchedItem = '已采用规则';
                            }

                            return {
                                '适用ID': rule.适用ID,
                                '规则ID': rule.规则ID,
                                '规则名称': rule.规则名称,
                                '行为认定': rule.行为认定,
                                '类型': rule.类型 || '未知',
                                '规则类型': rule.规则类型 || '未知',
                                '规则内涵': rule.规则内涵 || '未设置',
                                '匹配度': rule.匹配度 || 1.0,
                                '推荐原因': rule.推荐原因,
                                '状态': '已采用',
                                '匹配项目': matchedItem,
                                '城市': rule.城市 || '未知',
                                '规则来源': rule.规则来源 || '未知',
                                // 添加分组相关字段以避免JavaScript错误
                                '分组标识': matchedItem || '已采用规则',
                                '分组序号': 1,
                                '分组总数': 1,
                                '是否分组首个': true,
                                '合并规则数量': 1
                            };
                        });

                        // 显示已采用规则
                        originalRecommendations = adoptedRules;
                        filteredRecommendations = adoptedRules;

                        if (adoptedRules.length > 0) {
                            // 获取医院名称用于显示
                            const hospitalSelect = document.getElementById('ruleHospitalSelect');
                            const hospitalName = hospitalSelect.options[hospitalSelect.selectedIndex].text;

                            // 在规则列表前添加医院信息提示
                            const hospitalInfo = document.createElement('div');
                            hospitalInfo.className = 'alert alert-success mb-3';
                            hospitalInfo.innerHTML = `
                                <i class="bi bi-hospital"></i>
                                <strong>当前查看医院：${hospitalName}</strong>
                                <span class="badge bg-success ms-2">${adoptedRules.length} 条已采用规则</span>
                            `;
                            section.appendChild(hospitalInfo);

                            // 显示过滤器并初始化
                            document.getElementById('filterSection').style.display = 'block';
                            initializeFilters(adoptedRules);

                            // 设置状态过滤器为"已采用"
                            document.getElementById('filterStatus').value = '已采用';

                            renderFilteredRecommendations(adoptedRules);

                            // 显示批量操作工具栏，并设置为已采用页面状态
                            const toolbar = document.getElementById('batchToolbar');
                            toolbar.style.display = 'block';
                            setBatchToolbarForAdopted();
                        } else {
                            // 获取医院名称用于显示
                            const hospitalSelect = document.getElementById('ruleHospitalSelect');
                            const hospitalName = hospitalSelect.options[hospitalSelect.selectedIndex].text;

                            section.innerHTML = `<div class="alert alert-info">
                                <i class="bi bi-hospital"></i>
                                <strong>${hospitalName}</strong> 暂无已采用规则
                            </div>`;
                            document.getElementById('filterSection').style.display = 'none';
                            document.getElementById('batchToolbar').style.display = 'none';
                        }
                    } else {
                        section.innerHTML = `<div class="alert alert-danger">加载已采用规则失败: ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    console.error('加载已采用规则失败:', error);
                    section.innerHTML = '<div class="alert alert-danger">加载已采用规则失败，请稍后重试</div>';
                });
        }

        // 加载医院数据分析
        function loadHospitalAnalysis(hospitalId) {
            const section = document.getElementById('hospitalDataSection');
            section.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>正在加载医院数据...</p></div>';

            fetch(`/api/hospital-data/${hospitalId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderHospitalData(data);
                    } else {
                        section.innerHTML = `<div class="alert alert-danger">加载医院数据失败: ${data.error}</div>`;
                    }
                })
                .catch(error => {
                    console.error('加载医院数据失败:', error);
                    section.innerHTML = '<div class="alert alert-danger">加载医院数据失败，请稍后重试</div>';
                });
        }

        // 渲染医院数据
        function renderHospitalData(data) {
            const section = document.getElementById('hospitalDataSection');
            const hospital = data.hospital_info;
            const stats = data.statistics;
            const summary = data.summary;
            const dataList = data.data;

            let html = `
                <!-- 医院基本信息 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5><i class="bi bi-hospital"></i> ${hospital.医院名称} - 收费数据分析</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>医院编码：</strong>${hospital.医院编码 || '未设置'}
                            </div>
                            <div class="col-md-3">
                                <strong>所在城市：</strong>${hospital.所在城市 || '未设置'}
                            </div>
                            <div class="col-md-3">
                                <strong>医院等级：</strong>${hospital.医院等级 || '未设置'}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据统计 -->
                <div class="row mb-3">
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-primary">${stats.总记录数.toLocaleString()}</h4>
                                <small class="text-muted">总记录数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-success">${stats.项目种类数.toLocaleString()}</h4>
                                <small class="text-muted">项目种类</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-warning">${stats.总收费金额.toLocaleString()}</h4>
                                <small class="text-muted">总收费金额(元)</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-info">${stats.总收费次数.toLocaleString()}</h4>
                                <small class="text-muted">总收费次数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <small class="text-muted">数据时间范围</small>
                                <div>${stats.最早数据时间 || '无'} 至 ${stats.最新数据时间 || '无'}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收费项目汇总（分类显示） -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6>收费项目汇总 (按金额排序前20)</h6>
                    </div>
                    <div class="card-body">
                        <!-- 分类标签页 -->
                        <ul class="nav nav-tabs mb-3" id="summaryTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="total-tab" data-bs-toggle="tab" data-bs-target="#total-panel" type="button" role="tab">
                                    总体前20
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="drugs-tab" data-bs-toggle="tab" data-bs-target="#drugs-panel" type="button" role="tab">
                                    药品前20
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="items-tab" data-bs-toggle="tab" data-bs-target="#items-panel" type="button" role="tab">
                                    项目前20
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="materials-tab" data-bs-toggle="tab" data-bs-target="#materials-panel" type="button" role="tab">
                                    耗材前20
                                </button>
                            </li>
                        </ul>

                        <!-- 分类内容 -->
                        <div class="tab-content" id="summaryTabContent">
                            <!-- 总体前20 -->
                            <div class="tab-pane fade show active" id="total-panel" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>医保项目编码</th>
                                                <th>医保项目名称</th>
                                                <th>总金额(元)</th>
                                                <th>总次数</th>
                                                <th>记录数</th>
                                                <th>平均单价</th>
                                            </tr>
                                        </thead>
                                        <tbody id="total-tbody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 药品前20 -->
                            <div class="tab-pane fade" id="drugs-panel" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>医保项目编码</th>
                                                <th>医保项目名称</th>
                                                <th>总金额(元)</th>
                                                <th>总次数</th>
                                                <th>记录数</th>
                                                <th>平均单价</th>
                                            </tr>
                                        </thead>
                                        <tbody id="drugs-tbody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 项目前20 -->
                            <div class="tab-pane fade" id="items-panel" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>医保项目编码</th>
                                                <th>医保项目名称</th>
                                                <th>总金额(元)</th>
                                                <th>总次数</th>
                                                <th>记录数</th>
                                                <th>平均单价</th>
                                            </tr>
                                        </thead>
                                        <tbody id="items-tbody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 耗材前20 -->
                            <div class="tab-pane fade" id="materials-panel" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>医保项目编码</th>
                                                <th>医保项目名称</th>
                                                <th>总金额(元)</th>
                                                <th>总次数</th>
                                                <th>记录数</th>
                                                <th>平均单价</th>
                                            </tr>
                                        </thead>
                                        <tbody id="materials-tbody">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            html += `

                <!-- 详细数据 -->
                <div class="card">
                    <div class="card-header">
                        <h6>详细收费记录 (最新20条)</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>医保项目编码</th>
                                        <th>医保项目名称</th>
                                        <th>收费金额</th>
                                        <th>收费次数</th>
                                        <th>科室名称</th>
                                        <th>医生姓名</th>
                                        <th>患者年龄</th>
                                        <th>创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;

            dataList.forEach(item => {
                html += `
                    <tr>
                        <td>${item.医保项目编码 || ''}</td>
                        <td>${item.医保项目名称 || ''}</td>
                        <td class="text-end">${item.收费金额 || '0'}</td>
                        <td class="text-end">${item.收费次数 || '0'}</td>
                        <td>${item.科室名称 || ''}</td>
                        <td>${item.医生姓名 || ''}</td>
                        <td>${item.患者年龄 || ''}</td>
                        <td>${item.创建时间 || ''}</td>
                    </tr>
                `;
            });

            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            section.innerHTML = html;

            // DOM更新后填充各分类的数据
            setTimeout(() => {
                fillSummaryTable('total', summary.total || []);
                fillSummaryTable('drugs', summary.drugs || []);
                fillSummaryTable('items', summary.items || []);
                fillSummaryTable('materials', summary.materials || []);
            }, 100);
        }

        // 填充汇总表格数据
        function fillSummaryTable(type, data) {
            const tbody = document.getElementById(`${type}-tbody`);
            if (!tbody) return;

            let html = '';
            data.forEach((item, index) => {
                const avgPrice = item.总次数 > 0 ? (item.总金额 / item.总次数).toFixed(2) : '0.00';
                html += `
                    <tr>
                        <td class="text-center">${index + 1}</td>
                        <td>${item.医保项目编码}</td>
                        <td>${item.医保项目名称}</td>
                        <td class="text-end">${item.总金额.toLocaleString()}</td>
                        <td class="text-end">${item.总次数.toLocaleString()}</td>
                        <td class="text-end">${item.记录数.toLocaleString()}</td>
                        <td class="text-end">${avgPrice}</td>
                    </tr>
                `;
            });

            if (data.length === 0) {
                html = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">暂无数据</td>
                    </tr>
                `;
            }

            tbody.innerHTML = html;
        }

        // 显示推荐规则的通用函数
        function displayRecommendations(recommendations) {
            // 更新全局变量
            allFilteredRecommendations = recommendations;
            currentPage = 1; // 重置到第一页

            // 重新渲染页面
            renderCurrentPage();
        }

        // 统计信息过滤功能
        function filterDuplicateWarningRules() {
            currentFilterType = 'duplicate';
            const duplicateRules = originalRecommendations.filter(rule =>
                rule.状态 === '推荐' && rule.重复已采用规则 && rule.重复已采用规则.length > 0
            );
            baseFilteredRecommendations = duplicateRules;
            filteredRecommendations = duplicateRules;
            displayRecommendations(filteredRecommendations);
            updateStatisticsButtons();
            // 清空下拉筛选器，重新应用过滤器以保持联动
            clearDropdownFilters();
        }

        function filterCleanRecommendedRules() {
            currentFilterType = 'clean';
            const cleanRecommendedRules = originalRecommendations.filter(rule =>
                rule.状态 === '推荐' && (!rule.重复已采用规则 || rule.重复已采用规则.length === 0)
            );
            baseFilteredRecommendations = cleanRecommendedRules;
            filteredRecommendations = cleanRecommendedRules;
            displayRecommendations(filteredRecommendations);
            updateStatisticsButtons();
            // 清空下拉筛选器，重新应用过滤器以保持联动
            clearDropdownFilters();
        }

        function showAllRules() {
            currentFilterType = 'all';
            baseFilteredRecommendations = [];
            filteredRecommendations = originalRecommendations;
            displayRecommendations(filteredRecommendations);
            updateStatisticsButtons();
            // 清空下拉筛选器，重新应用过滤器以保持联动
            clearDropdownFilters();
        }

        // 清空下拉筛选器
        function clearDropdownFilters() {
            document.getElementById('filterCity').value = '';
            document.getElementById('filterSource').value = '';
            document.getElementById('filterType').value = '';
            document.getElementById('filterRuleCategory').value = '';
            document.getElementById('filterRuleName').value = '';
            document.getElementById('filterStatus').value = '';
            document.getElementById('filterMatchScore').value = '';
        }

        // 检查是否有下拉筛选器被激活
        function hasActiveDropdownFilters() {
            return document.getElementById('filterCity').value ||
                   document.getElementById('filterSource').value ||
                   document.getElementById('filterType').value ||
                   document.getElementById('filterRuleCategory').value ||
                   document.getElementById('filterRuleName').value ||
                   document.getElementById('filterStatus').value ||
                   document.getElementById('filterMatchScore').value;
        }

        function updateStatisticsButtons() {
            // 更新统计按钮的数字（始终基于原始数据计算）
            const recommendedRules = originalRecommendations.filter(rule => rule.状态 === '推荐');
            const duplicateWarningRules = recommendedRules.filter(rule =>
                rule.重复已采用规则 && rule.重复已采用规则.length > 0
            );
            const cleanRecommendedRules = recommendedRules.filter(rule =>
                !rule.重复已采用规则 || rule.重复已采用规则.length === 0
            );

            // 查找并更新按钮
            const duplicateBtn = document.querySelector('button[onclick="filterDuplicateWarningRules()"]');
            const cleanRecommendedBtn = document.querySelector('button[onclick="filterCleanRecommendedRules()"]');

            if (duplicateBtn) {
                const badge = duplicateBtn.querySelector('.badge');
                if (badge) badge.textContent = duplicateWarningRules.length;
            }

            if (cleanRecommendedBtn) {
                const badge = cleanRecommendedBtn.querySelector('.badge');
                if (badge) badge.textContent = cleanRecommendedRules.length;
            }

            // 更新按钮状态
            document.querySelectorAll('.btn-group button').forEach(btn => {
                btn.classList.remove('active');
            });

            if (currentFilterType === 'duplicate' && duplicateBtn) {
                duplicateBtn.classList.add('active');
            } else if (currentFilterType === 'clean' && cleanRecommendedBtn) {
                cleanRecommendedBtn.classList.add('active');
            } else {
                const showAllBtn = document.querySelector('button[onclick="showAllRules()"]');
                if (showAllBtn) showAllBtn.classList.add('active');
            }
        }

    </script>

    <!-- 重复规则审查模态框 -->
    <div class="modal fade" id="duplicateAnalysisModal" tabindex="-1" aria-labelledby="duplicateAnalysisModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="duplicateAnalysisModalLabel">
                        <i class="bi bi-search"></i> 重复规则审查
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="duplicateAnalysisContent">
                        <div class="text-center">
                            <div class="spinner-border" role="status"></div>
                            <p class="mt-2">正在分析重复规则...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-danger" id="batchUnadoptBtn" onclick="batchUnadoptSelected()" style="display: none;">
                        <i class="bi bi-x-circle"></i> 批量取消采用
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
