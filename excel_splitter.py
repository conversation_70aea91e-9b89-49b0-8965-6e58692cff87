import pandas as pd
import os
from flask import Flask, render_template, request, send_file, jsonify
from werkzeug.utils import secure_filename
import io
import zipfile
from unidecode import unidecode
import concurrent.futures
import oracledb
import configparser
import datetime
import shutil
import threading
from multiprocessing import Process, Queue, Pool, cpu_count
import math
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
import gc
import functools

# 创建Flask应用
app = Flask(__name__)

# 设置上传文件夹和输出文件夹
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'output'
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['OUTPUT_FOLDER'] = OUTPUT_FOLDER

# 读取配置文件
config = configparser.ConfigParser()
config.read('config.ini')

# 获取数据库配置
DB_USERNAME = config.get('database', 'username', fallback='')
DB_PASSWORD = config.get('database', 'password', fallback='')
DB_DSN = config.get('database', 'dsn', fallback='')

def allowed_file(filename):
    # 检查文件扩展名是否允许
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def sanitize_filename(filename):
    # 只对文件名中的非法字符进行处理，保留中文字符
    return ''.join(c for c in filename if c.isalnum() or c in (' ', '.', '_', '-'))


def split_excel(input_file, output_dir, group_column, original_filename):
    try:
        # 读取Excel文件的所有sheet
        excel_file = pd.ExcelFile(input_file)
        sheet_names = excel_file.sheet_names
    except Exception as e:
        return f"读取文件 {original_filename} 时出错: {e}"

    base_file_name = os.path.splitext(original_filename)[0]
    output_files = []
    grouped_data = {}

    max_rows_per_sheet = 500000  # Excel每个sheet的最大行数

    for sheet_name in sheet_names:
        try:
            # 读取整个sheet的数据
            data = pd.read_excel(input_file, sheet_name=sheet_name)

            # 检查分组列是否存在
            if group_column not in data.columns:
                return f"错误: 列 '{group_column}' 不存在于文件 {original_filename} 的 sheet {sheet_name} 中。"

            # 添加一列表示原始sheet名称
            data['原始Sheet'] = sheet_name

            # 按指定列进行分组
            grouped = data.groupby(group_column)

            for code, group in grouped:
                if code not in grouped_data:
                    grouped_data[code] = []
                grouped_data[code].append(group)

        except Exception as e:
            return f"读取文件 {original_filename} 的 sheet {sheet_name} 时出错: {e}"

    # 将分组后的数据存到Excel文件
    for code, groups in grouped_data.items():
        safe_code = sanitize_filename(str(code))
        output_file = os.path.join(output_dir, f'{safe_code}_{base_file_name}.xlsx')
        try:
            # 合并所有组的数据
            combined_data = pd.concat(groups, ignore_index=True)
            
            # 将合并后数据保存到多个sheet中
            wb = Workbook()
            wb.remove(wb.active)  # 移除默认创建的sheet
            
            for i in range(0, len(combined_data), max_rows_per_sheet):
                sheet_data = combined_data.iloc[i:i + max_rows_per_sheet]
                sheet_name = f'Sheet{i // max_rows_per_sheet + 1}'[:31]  # 限制sheet名称长度
                ws = wb.create_sheet(title=sheet_name)
                
                for r in dataframe_to_rows(sheet_data, index=False, header=True):
                    ws.append(r)
            
            wb.save(output_file)
            
            output_files.append((output_file, f'{code}_{base_file_name}.xlsx'))
        except Exception as e:
            return f"保存文件 {code}_{base_file_name}.xlsx 时出错: {e}"

    return output_files

def process_file(file, group_column):
    if file and allowed_file(file.filename):
        # 处理每个上传的文件
        original_filename = file.filename
        safe_filename = sanitize_filename(original_filename)
        input_path = os.path.join(app.config['UPLOAD_FOLDER'], safe_filename)
        output_dir = os.path.join(app.config['OUTPUT_FOLDER'], os.path.splitext(safe_filename)[0])

        # 创建必要的目录
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs(output_dir, exist_ok=True)

        # 保存上传的文件
        file.save(input_path)

        # 拆分Excel文件
        return split_excel(input_path, output_dir, group_column, original_filename)
    else:
        return f'不支持的文件类型: {file.filename}'

@app.route('/', methods=['GET'])
def index():
    # 每次请求时重新读取配置文件，以确保获取最新的配置
    config.read('config.ini')
    db_username = config.get('database', 'username', fallback='')
    db_password = config.get('database', 'password', fallback='')
    db_dsn = config.get('database', 'dsn', fallback='')
    
    # 渲染主页，并传递数据库配置
    return render_template('index.html', db_username=db_username, db_password=db_password, db_dsn=db_dsn)

@app.route('/split', methods=['POST'])
def split_files():
    # 处理文件拆分请求
    if 'files' not in request.files:
        return render_template('index.html', message='没有选择文件')
    
    files = request.files.getlist('files')
    group_column = request.form['group_column']

    if not files or files[0].filename == '':
        return render_template('index.html', message='没有选择文件')

    all_output_files = []
    error_messages = []

    # 使用线程池并行处理文件
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future_to_file = {executor.submit(process_file, file, group_column): file for file in files}
        for future in concurrent.futures.as_completed(future_to_file):
            file = future_to_file[future]
            try:
                result = future.result()
                if isinstance(result, str):
                    error_messages.append(result)
                else:
                    all_output_files.extend(result)
            except Exception as exc:
                error_messages.append(f'{file.filename} 生成异常: {exc}')

    if error_messages:
        return render_template('index.html', message='<br>'.join(error_messages))

    if all_output_files:
        # 创建包含所有拆分文件的ZIP文件
        memory_file = io.BytesIO()
        with zipfile.ZipFile(memory_file, 'w') as zf:
            # 获取所有拆分文件的根目录
            root_dir = os.path.commonpath([f for f, _ in all_output_files])
            for f, original_name in all_output_files:
                # 将文件添加到ZIP文件中，保持相对路径结构
                zf.write(f, os.path.relpath(f, root_dir))
        memory_file.seek(0)

        # 返回ZIP文件供下载
        return send_file(
            memory_file,
            mimetype='application/zip',
            as_attachment=True,
            download_name='split_files.zip'
        )

    return render_template('index.html', message='没有成功拆分任何文件')

def connect_to_oracle(username, password, dsn):
    # 使用 Thin 模式，免去安装 Oracle 客户端的需求
    connection = oracledb.connect(user=username, password=password, dsn=dsn)
    return connection

def fetch_data(connection, query):
    df = pd.read_sql(query, con=connection)
    return df

def split_and_save_to_excel(df, group_columns, output_dir, prefix, suffix):
    output_files = []
    unique_values = df[group_columns].drop_duplicates()
    
    max_rows_per_sheet = 500000  # 设置每个sheet的最大行数，略小于Excel的限制

    for _, row in unique_values.iterrows():
        subset = df
        for column in group_columns:
            subset = subset[subset[column] == row[column]]
        
        filename_parts = [str(row[col]) for col in group_columns]
        filename = '_'.join(filter(None, [prefix] + filename_parts + [suffix])) + '.xlsx'
        filename = sanitize_filename(filename)

        output_file = os.path.join(output_dir, filename)
        
        # 使用openpyxl来写入多个sheet
        wb = Workbook()
        wb.remove(wb.active)  # 移除默认创建的sheet
        
        for i in range(0, len(subset), max_rows_per_sheet):
            sheet_data = subset.iloc[i:i + max_rows_per_sheet]
            sheet_name = f'Sheet{i // max_rows_per_sheet + 1}'[:31]  # 限制sheet名称长度
            ws = wb.create_sheet(title=sheet_name)
            
            for r in dataframe_to_rows(sheet_data, index=False, header=True):
                ws.append(r)
        
        wb.save(output_file)
        
        output_files.append((output_file, filename))
        print(f"数据已保存至文件: {filename}")
    
    return output_files

def fetch_data_with_params(connection, query, params):  
    # 使用参数化查询来避免SQL注入  
    cursor = connection.cursor()  
    cursor.execute(query, params)  
    columns = [col[0] for col in cursor.description]  
    data = cursor.fetchall()  
    return pd.DataFrame(data, columns=columns)

def create_zip_from_files(output_dir, file_prefix, file_suffix, zip_name):  
    memory_file = io.BytesIO()  
    with zipfile.ZipFile(memory_file, 'w') as zf:  
        for filename in os.listdir(output_dir):  
            if filename.startswith(file_prefix) and filename.endswith(file_suffix):  
                file_path = os.path.join(output_dir, filename)  
                zf.write(file_path, arcname=os.path.basename(file_path))  
    memory_file.seek(0)  
    return memory_file

def process_group(connection_params, query, group, group_columns, output_dir, file_prefix, file_suffix):
    try:
        with connect_to_oracle(**connection_params) as connection:
            where_clause = " AND ".join([f"{col} = :{col}" for col in group_columns])
            query_with_params = f"{query} WHERE {where_clause}"
            params = {col: group[col] for col in group_columns}
            
            df = fetch_data_with_params(connection, query_with_params, params)  
            
            file_tuples = split_and_save_to_excel(df, group_columns, output_dir, file_prefix, file_suffix)  
            return file_tuples
    except Exception as e:
        return [(None, f"Error processing group {group}: {str(e)}")]

@app.route('/query_db', methods=['POST'])  
def query_db():  
    query = request.form['query']  
    group_columns = request.form.getlist('group_columns[]')  
    db_username = request.form['db_username']  
    db_password = request.form['db_password']  
    db_dsn = request.form['db_dsn']  
    file_prefix = request.form.get('file_prefix', '').strip()  
    file_suffix = request.form.get('file_suffix', '').strip()  
    num_processes = int(request.form.get('num_processes', cpu_count()))  # 默认使用CPU核心数
      
    try:  
        connection_params = {
            'username': db_username,
            'password': db_password,
            'dsn': db_dsn
        }
        
        with connect_to_oracle(**connection_params) as connection:  
            output_dir = os.path.join(app.config['OUTPUT_FOLDER'], 'db_query_results')  
            os.makedirs(output_dir, exist_ok=True)  
            
            # 首先获取所有唯一的组合
            group_query = f"SELECT DISTINCT {', '.join(group_columns)} FROM ({query})"
            df_groups = fetch_data(connection, group_query)
        
        # 使用多进程处理每个组
        with Pool(processes=num_processes) as pool:
            process_func = functools.partial(
                process_group, 
                connection_params, 
                query, 
                group_columns=group_columns, 
                output_dir=output_dir, 
                file_prefix=file_prefix, 
                file_suffix=file_suffix
            )
            results = pool.map(process_func, df_groups.to_dict('records'))
        
        # 合并所有结果
        output_files = [item for sublist in results for item in sublist]
        
        # 处理结果...
        # (The rest of your code for creating ZIP file and returning response)

    except Exception as e:  
        app.logger.error(f"An error occurred: {str(e)}")  # 记录错误日志  
        return jsonify({'error': 'An error occurred while processing your request.'}), 500  


def query_YYJG():  
    error_messages_local = []  # 在函数内部定义一个局部的错误消息列表（如果需要在函数内部管理）  
    db_username = request.form['db_username']  
    db_password = request.form['db_password']  
    db_dsn = request.form['db_dsn']  
    try:  
        connection = connect_to_oracle(db_username, db_password, db_dsn)  
        df = fetch_data(connection, "select 定点机构编码 as 机构编码 from 住院医院编码 order by 定点机构编码")  
          
        # 假设 df 是一个 pandas DataFrame，且有一个列名为 '机构编码'  
        institution_codes = set(df['机构编码'])  
          
        connection.close()  
          
        # 遍历机构编码集合（这里只是打印作为示例）  
        for code in institution_codes:  
            print(f"机构编码: {code}")  
          
        # 返回机构编码集合（如果需要在函数外部使用）  
        return institution_codes  
      
    except Exception as e:  
        # 将错误消息添加到局部或全局列表中  
        error_messages_local.append(f"处理查询时出错: {str(e)}")  
        # 如果需要在函数外部访问错误消息，可以使用全局列表  
        # error_messages.append(f"处理查询时出错: {str(e)}")  
          
        # 返回空集合以表示失败  
        return set()  
  
    finally:  
        # 注意：如果 connection 变量在 try 块中未成功赋值（例如由于异常），  
        # 则在这里尝试关闭它可能会引发另一个异常。  
        # 更好的做法是使用 try-except-finally 结构来确保连接总是被关闭，  
        # 但要检查 connection 是否已被成功创建。  
        # 由于我们已经在 try 块中关闭了连接，这里可以省略，  
        # 但为了完整性，我保留了这个 finally 块并添加了一个检查。  
        if 'connection' in locals() and connection:  
            try:  
                connection.close()  
            except Exception as close_ex:  
                error_messages_local.append(f"关闭数据库连接时出错: {str(close_ex)}")  
                # 如果需要在函数外部访问这个错误，也可以使用全局列表  
                # error_messages.append(f"关闭数据库连接时出错: {str(close_ex)}")  
  
    # 注意：由于 error_messages 在这个示例中是假设在外部定义的，  
    # 如果你选择使用 error_messages_local，则需要在函数外部以其他方式处理错误消息。  
    # 另外，根据你的实际需求，你可能希望调整错误处理策略。

def execute_query(connection_params, sql_filename, sql_content, results_dir, no_results_dir, output_files, error_messages):
    try:
        connection = oracledb.connect(**connection_params)
        sql_content = sql_content.strip().rstrip(';')
        
        cursor = connection.cursor()
        cursor.execute(sql_content)
        results = cursor.fetchall()
        
        if results:
            # 查询有结果
            df = pd.DataFrame(results, columns=[col[0] for col in cursor.description])
            output_filename = f"{os.path.splitext(sql_filename)[0]}_result.xlsx"
            output_path = os.path.join(results_dir, output_filename)
            df.to_excel(output_path, index=False)
            output_files.append((output_path, output_filename))
            
            # 保存SQL文件到结果目录
            with open(os.path.join(results_dir, sql_filename), 'w', encoding='utf-8') as f:
                f.write(sql_content)
        else:
            # 查询无结果
            with open(os.path.join(no_results_dir, sql_filename), 'w', encoding='utf-8') as f:
                f.write(sql_content)
        
        cursor.close()
        connection.close()
    except Exception as e:
        error_messages.append(f"处理文件 {sql_filename} 时出错: {str(e)}")

def thread_worker(connection_params, sql_files, results_dir, no_results_dir, output_files, error_messages, num_threads=5):
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [executor.submit(execute_query, connection_params, sql_filename, sql_content, results_dir, no_results_dir, output_files, error_messages) for sql_filename, sql_content in sql_files]
        concurrent.futures.wait(futures)

def process_worker(connection_params, sql_files, results_dir, no_results_dir, output_queue, error_queue, num_threads=5):
    output_files = []
    error_messages = []
    thread_worker(connection_params, sql_files, results_dir, no_results_dir, output_files, error_messages, num_threads)
    output_queue.put(output_files)
    error_queue.put(error_messages)

@app.route('/batch_query', methods=['GET', 'POST'])
def batch_query():
    if request.method == 'GET':
        config.read('config.ini')
        db_username = config.get('database', 'username', fallback='')
        db_password = config.get('database', 'password', fallback='')
        db_dsn = config.get('database', 'dsn', fallback='')
        return render_template('batch_query.html', db_username=db_username, db_password=db_password, db_dsn=db_dsn)
    
    elif request.method == 'POST':
        db_username = request.form['db_username']
        db_password = request.form['db_password']
        db_dsn = request.form['db_dsn']
        num_processes = int(request.form.get('num_processes', 10))
        num_threads = int(request.form.get('num_threads', 5))
        
        if 'sql_files' not in request.files:
            return render_template('batch_query.html', message='没有选择SQL文件')
        
        sql_files = request.files.getlist('sql_files')
        
        if not sql_files or sql_files[0].filename == '':
            return render_template('batch_query.html', message='没有选择SQL文件')
        
        try:
            connection_params = {
                'user': db_username,
                'password': db_password,
                'dsn': db_dsn
            }
            
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            base_output_dir = os.path.join(app.config['OUTPUT_FOLDER'], f'batch_query_results_{timestamp}')
            results_dir = os.path.join(base_output_dir, 'results')
            no_results_dir = os.path.join(base_output_dir, 'no_results')
            os.makedirs(results_dir, exist_ok=True)
            os.makedirs(no_results_dir, exist_ok=True)
            
            output_queue = Queue()
            error_queue = Queue()
            
            # 读取SQL文件内容
            sql_file_contents = []
            for sql_file in sql_files:
                content = sql_file.read().decode('utf-8')
                sql_file_contents.append((sql_file.filename, content))
            
            # 将SQL文件内容分成多个批次
            batch_size = len(sql_file_contents) // num_processes
            sql_file_batches = [sql_file_contents[i:i + batch_size] for i in range(0, len(sql_file_contents), batch_size)]
            
            processes = []
            for batch in sql_file_batches:
                process = Process(target=process_worker, args=(connection_params, batch, results_dir, no_results_dir, output_queue, error_queue, num_threads))
                processes.append(process)
                process.start()
            
            for process in processes:
                process.join()
            
            output_files = []
            error_messages = []
            while not output_queue.empty():
                output_files.extend(output_queue.get())
            while not error_queue.empty():
                error_messages.extend(error_queue.get())
            
            if error_messages:
                return render_template('batch_query.html', message='<br>'.join(error_messages))
            
            if output_files:
                # 创建ZIP文件
                memory_file = io.BytesIO()
                with zipfile.ZipFile(memory_file, 'w') as zf:
                    for root, dirs, files in os.walk(base_output_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, base_output_dir)
                            zf.write(file_path, arcname)
                memory_file.seek(0)
                
                return send_file(
                    memory_file,
                    mimetype='application/zip',
                    as_attachment=True,
                    download_name=f'batch_query_results_{timestamp}.zip'
                )
            else:
                return render_template('batch_query.html', message='没有生成任何结果文件')
        
        except Exception as e:
            return render_template('batch_query.html', message=f'执行查询时出错: {str(e)}')

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5002))# 默认使用 5002 端口，但可以通过环境变量 PORT 来覆盖
    app.run(host='0.0.0.0', debug=False, port=port)
