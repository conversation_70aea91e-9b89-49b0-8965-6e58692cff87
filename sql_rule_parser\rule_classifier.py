"""
规则类型识别器
基于SQL结构特征自动识别规则类型
"""

import re
from typing import List
from .models import RuleType, SQLToken, TokenType


class RuleClassifier:
    """规则类型识别器"""
    
    def __init__(self):
        # 规则类型识别模式
        self.patterns = {
            RuleType.DUPLICATE_BILLING: [
                # 重复收费特征：INTERSECT关键字
                r'\bINTERSECT\b',
                # 同时收取多个项目
                r'同时.*?收取',
                r'重复.*?收取',
                # 时间条件匹配
                r"to_char\([^)]*,'yyyy-MM-dd hh24'\)",
            ],
            RuleType.EXCESSIVE_USAGE: [
                # 超量使用特征：HAVING SUM(数量) > 阈值
                r'HAVING\s+SUM\s*\(\s*数量\s*\)\s*>\s*[\d.]+',
                # 超标准收费
                r'未按.*?计价.*?标准',
                r'超.*?标准',
                # 每日数量限制
                r'超每日数量',
                r'超出.*?标准',
            ],
            RuleType.CASE_EXTRACTION: [
                # 病例提取特征：简单WHERE条件
                r'病例提取',
                r'根据项目提取',
                # 无指征使用
                r'无指征',
                r'无相关疾病指征',
                # 常规检查
                r'常规检查',
                r'普遍.*?检测',
            ],
            RuleType.OVERCHARGE: [
                # 超标准收费
                r'超标准收费',
                r'未按.*?计价单位',
                r'超.*?加收标准',
                # 计价相关
                r'计价标准',
                r'收费标准',
            ],
            RuleType.INAPPROPRIATE_USAGE: [
                # 不当使用
                r'不当使用',
                r'超说明书',
                r'非.*?患者',
                # 年龄限制
                r'年龄.*?患者',
                r'限年龄',
            ]
        }
    
    def classify_rule(self, sql_content: str, tokens: List[SQLToken] = None) -> RuleType:
        """识别规则类型"""
        sql_upper = sql_content.upper()
        
        # 1. 基于文本模式识别
        for rule_type, patterns in self.patterns.items():
            for pattern in patterns:
                if re.search(pattern, sql_content, re.IGNORECASE):
                    return rule_type
        
        # 2. 基于SQL结构特征识别
        structure_type = self._classify_by_structure(sql_content, tokens)
        if structure_type != RuleType.UNKNOWN:
            return structure_type
        
        # 3. 基于关键字组合识别
        keyword_type = self._classify_by_keywords(sql_upper)
        if keyword_type != RuleType.UNKNOWN:
            return keyword_type
        
        return RuleType.UNKNOWN
    
    def _classify_by_structure(self, sql_content: str, tokens: List[SQLToken] = None) -> RuleType:
        """基于SQL结构特征识别"""
        sql_upper = sql_content.upper()

        # 检查INTERSECT - 重复收费的强特征
        if 'INTERSECT' in sql_upper:
            return RuleType.DUPLICATE_BILLING

        # 检查HAVING SUM(数量) > 数值 - 超量使用的强特征
        having_pattern = r'HAVING\s+SUM\s*\(\s*数量\s*\)\s*>\s*[\d.]+'
        if re.search(having_pattern, sql_content, re.IGNORECASE):
            return RuleType.EXCESSIVE_USAGE

        # 检查包含违规数量计算的CTE - 也是超量使用特征
        if 'WITH' in sql_upper and '违规数量' in sql_content and 'SUM(数量)' in sql_upper:
            return RuleType.EXCESSIVE_USAGE

        # 检查WITH子句结合复杂逻辑 - 通常是重复收费
        if 'WITH' in sql_upper and ('INTERSECT' in sql_upper or '同时' in sql_content):
            return RuleType.DUPLICATE_BILLING

        # 检查简单的WHERE条件 - 可能是病例提取
        if self._is_simple_extraction_query(sql_content):
            return RuleType.CASE_EXTRACTION

        return RuleType.UNKNOWN
    
    def _classify_by_keywords(self, sql_upper: str) -> RuleType:
        """基于关键字组合识别"""
        
        # 重复收费关键字
        duplicate_keywords = ['重复', '同时收取', 'INTERSECT', '时间段']
        duplicate_score = sum(1 for keyword in duplicate_keywords if keyword in sql_upper)
        
        # 超量使用关键字
        excessive_keywords = ['HAVING', 'SUM(数量)', '>', '超', '标准']
        excessive_score = sum(1 for keyword in excessive_keywords if keyword in sql_upper)
        
        # 病例提取关键字
        extraction_keywords = ['无指征', '常规检查', '普遍', '大量']
        extraction_score = sum(1 for keyword in extraction_keywords if keyword in sql_upper)
        
        # 选择得分最高的类型
        scores = {
            RuleType.DUPLICATE_BILLING: duplicate_score,
            RuleType.EXCESSIVE_USAGE: excessive_score,
            RuleType.CASE_EXTRACTION: extraction_score
        }
        
        max_score = max(scores.values())
        if max_score >= 2:  # 至少匹配2个关键字
            for rule_type, score in scores.items():
                if score == max_score:
                    return rule_type
        
        return RuleType.UNKNOWN
    
    def _is_simple_extraction_query(self, sql_content: str) -> bool:
        """判断是否为简单的病例提取查询"""
        sql_upper = sql_content.upper()
        
        # 简单查询特征：
        # 1. 没有复杂的子查询或CTE
        # 2. 没有INTERSECT或复杂的JOIN
        # 3. 主要是简单的WHERE条件
        
        complex_features = [
            'WITH', 'INTERSECT', 'UNION', 'EXISTS',
            'ROW_NUMBER', 'PARTITION BY', 'WINDOW'
        ]
        
        has_complex = any(feature in sql_upper for feature in complex_features)
        
        # 如果没有复杂特征，且包含诊断相关条件，可能是病例提取
        if not has_complex:
            extraction_indicators = [
                '诊断', '无指征', '常规检查', '普遍', 
                'ILIKE ANY', 'ARRAY', '大量'
            ]
            has_extraction = any(indicator in sql_content for indicator in extraction_indicators)
            return has_extraction
        
        return False
    
    def get_confidence_score(self, sql_content: str, predicted_type: RuleType) -> float:
        """获取分类置信度分数"""
        if predicted_type == RuleType.UNKNOWN:
            return 0.0
        
        patterns = self.patterns.get(predicted_type, [])
        matched_patterns = 0
        
        for pattern in patterns:
            if re.search(pattern, sql_content, re.IGNORECASE):
                matched_patterns += 1
        
        # 计算置信度 (匹配的模式数 / 总模式数)
        confidence = matched_patterns / len(patterns) if patterns else 0.0
        
        # 根据强特征调整置信度
        if predicted_type == RuleType.DUPLICATE_BILLING and 'INTERSECT' in sql_content.upper():
            confidence = max(confidence, 0.9)
        elif predicted_type == RuleType.EXCESSIVE_USAGE and re.search(r'HAVING\s+SUM.*?>', sql_content, re.IGNORECASE):
            confidence = max(confidence, 0.9)
        
        return min(confidence, 1.0)
    
    def explain_classification(self, sql_content: str, predicted_type: RuleType) -> List[str]:
        """解释分类结果"""
        explanations = []
        
        if predicted_type == RuleType.UNKNOWN:
            explanations.append("无法识别明确的规则类型特征")
            return explanations
        
        patterns = self.patterns.get(predicted_type, [])
        
        for pattern in patterns:
            if re.search(pattern, sql_content, re.IGNORECASE):
                explanations.append(f"匹配模式: {pattern}")
        
        # 添加结构特征说明
        if predicted_type == RuleType.DUPLICATE_BILLING:
            if 'INTERSECT' in sql_content.upper():
                explanations.append("检测到INTERSECT关键字，表明查找同时存在的项目组合")
            if 'to_char' in sql_content and 'hh24' in sql_content:
                explanations.append("检测到时间精确匹配，用于识别同时段重复收费")
        
        elif predicted_type == RuleType.EXCESSIVE_USAGE:
            if re.search(r'HAVING\s+SUM.*?>', sql_content, re.IGNORECASE):
                explanations.append("检测到数量聚合条件，用于识别超量使用")
        
        return explanations
