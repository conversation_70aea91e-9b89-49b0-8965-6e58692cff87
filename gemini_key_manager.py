#!/usr/bin/env python3
"""
Gemini API密钥管理器
实现多密钥轮询、限流保护和错误处理
"""

import time
import logging
import threading
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta

@dataclass
class KeyStatus:
    """API密钥状态"""
    key: str
    is_available: bool = True
    last_used: Optional[datetime] = None
    cooldown_until: Optional[datetime] = None
    usage_count: int = 0
    error_count: int = 0
    last_error: Optional[str] = None
    consecutive_errors: int = 0

class GeminiKeyManager:
    """Gemini API密钥管理器"""
    
    def __init__(self, api_keys: List[str], cooldown_time: int = 300, max_retries: int = 3):
        """
        初始化密钥管理器
        
        Args:
            api_keys: API密钥列表
            cooldown_time: 密钥冷却时间（秒）
            max_retries: 每个密钥的最大重试次数
        """
        if not api_keys:
            raise ValueError("至少需要提供一个API密钥")
        
        self.cooldown_time = cooldown_time
        self.max_retries = max_retries
        self.current_index = 0
        self._lock = threading.Lock()
        
        # 初始化密钥状态
        self.key_statuses: Dict[str, KeyStatus] = {}
        for key in api_keys:
            self.key_statuses[key] = KeyStatus(key=key)
        
        self.keys = list(api_keys)
        
        # 日志配置
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"密钥管理器初始化完成，共 {len(self.keys)} 个密钥")
    
    def get_next_key(self) -> Optional[str]:
        """
        获取下一个可用的API密钥
        
        Returns:
            可用的API密钥，如果没有可用密钥则返回None
        """
        with self._lock:
            # 清理过期的冷却状态
            self._cleanup_cooldowns()
            
            # 查找可用密钥
            available_keys = [key for key, status in self.key_statuses.items() if status.is_available]
            
            if not available_keys:
                self.logger.warning("没有可用的API密钥")
                return None
            
            # 轮询选择密钥
            attempts = 0
            while attempts < len(self.keys):
                key = self.keys[self.current_index]
                self.current_index = (self.current_index + 1) % len(self.keys)
                
                if key in available_keys:
                    # 更新使用状态
                    status = self.key_statuses[key]
                    status.last_used = datetime.now()
                    status.usage_count += 1
                    
                    self.logger.debug(f"选择密钥: {key[:10]}... (使用次数: {status.usage_count})")
                    return key
                
                attempts += 1
            
            self.logger.warning("轮询完所有密钥，没有找到可用密钥")
            return None
    
    def mark_key_error(self, key: str, error_message: str, is_rate_limit: bool = False):
        """
        标记密钥出现错误
        
        Args:
            key: 出错的API密钥
            error_message: 错误信息
            is_rate_limit: 是否为限流错误
        """
        with self._lock:
            if key not in self.key_statuses:
                return
            
            status = self.key_statuses[key]
            status.error_count += 1
            status.consecutive_errors += 1
            status.last_error = error_message
            
            if is_rate_limit:
                # 限流错误，设置冷却时间
                status.cooldown_until = datetime.now() + timedelta(seconds=self.cooldown_time)
                status.is_available = False
                self.logger.warning(f"密钥 {key[:10]}... 被限流，冷却至 {status.cooldown_until}")
            elif status.consecutive_errors >= self.max_retries:
                # 连续错误过多，临时禁用
                status.cooldown_until = datetime.now() + timedelta(seconds=60)  # 短暂冷却
                status.is_available = False
                self.logger.warning(f"密钥 {key[:10]}... 连续错误 {status.consecutive_errors} 次，临时禁用")
            
            self.logger.error(f"密钥 {key[:10]}... 错误: {error_message}")
    
    def mark_key_success(self, key: str):
        """
        标记密钥成功使用
        
        Args:
            key: 成功使用的API密钥
        """
        with self._lock:
            if key not in self.key_statuses:
                return
            
            status = self.key_statuses[key]
            status.consecutive_errors = 0  # 重置连续错误计数
            status.last_error = None
            
            self.logger.debug(f"密钥 {key[:10]}... 使用成功")
    
    def _cleanup_cooldowns(self):
        """清理过期的冷却状态"""
        now = datetime.now()
        for status in self.key_statuses.values():
            if status.cooldown_until and now >= status.cooldown_until:
                status.is_available = True
                status.cooldown_until = None
                self.logger.info(f"密钥 {status.key[:10]}... 冷却结束，重新可用")
    
    def get_status_summary(self) -> Dict[str, any]:
        """
        获取密钥状态摘要
        
        Returns:
            包含所有密钥状态信息的字典
        """
        with self._lock:
            self._cleanup_cooldowns()
            
            available_count = sum(1 for status in self.key_statuses.values() if status.is_available)
            total_usage = sum(status.usage_count for status in self.key_statuses.values())
            total_errors = sum(status.error_count for status in self.key_statuses.values())
            
            key_details = []
            for i, (key, status) in enumerate(self.key_statuses.items()):
                key_details.append({
                    'index': i,
                    'key_preview': f"{key[:10]}...{key[-4:]}",
                    'is_available': status.is_available,
                    'usage_count': status.usage_count,
                    'error_count': status.error_count,
                    'consecutive_errors': status.consecutive_errors,
                    'last_used': status.last_used.isoformat() if status.last_used else None,
                    'cooldown_until': status.cooldown_until.isoformat() if status.cooldown_until else None,
                    'last_error': status.last_error
                })
            
            return {
                'total_keys': len(self.keys),
                'available_keys': available_count,
                'unavailable_keys': len(self.keys) - available_count,
                'total_usage': total_usage,
                'total_errors': total_errors,
                'current_index': self.current_index,
                'key_details': key_details
            }
    
    def reset_all_keys(self):
        """重置所有密钥状态"""
        with self._lock:
            for status in self.key_statuses.values():
                status.is_available = True
                status.cooldown_until = None
                status.consecutive_errors = 0
                status.last_error = None
            
            self.logger.info("所有密钥状态已重置")
    
    def disable_key(self, key: str, reason: str = "手动禁用"):
        """
        手动禁用指定密钥
        
        Args:
            key: 要禁用的API密钥
            reason: 禁用原因
        """
        with self._lock:
            if key in self.key_statuses:
                self.key_statuses[key].is_available = False
                self.key_statuses[key].last_error = reason
                self.logger.info(f"密钥 {key[:10]}... 已手动禁用: {reason}")
    
    def enable_key(self, key: str):
        """
        手动启用指定密钥

        Args:
            key: 要启用的API密钥
        """
        with self._lock:
            if key in self.key_statuses:
                status = self.key_statuses[key]
                status.is_available = True
                status.cooldown_until = None
                status.consecutive_errors = 0
                status.last_error = None
                self.logger.info(f"密钥 {key[:10]}... 已手动启用")

    def add_key(self, key: str) -> bool:
        """
        动态添加新的API密钥

        Args:
            key: 要添加的API密钥

        Returns:
            bool: 添加是否成功
        """
        with self._lock:
            if not key or not key.strip():
                self.logger.error("尝试添加空密钥")
                return False

            key = key.strip()

            if key in self.key_statuses:
                self.logger.warning(f"密钥 {key[:10]}... 已存在")
                return False

            # 添加新密钥
            self.key_statuses[key] = KeyStatus(key=key)
            self.keys.append(key)

            self.logger.info(f"成功添加新密钥 {key[:10]}... (总数: {len(self.keys)})")
            return True

    def remove_key(self, key: str) -> bool:
        """
        动态删除API密钥

        Args:
            key: 要删除的API密钥

        Returns:
            bool: 删除是否成功
        """
        with self._lock:
            if key not in self.key_statuses:
                self.logger.warning(f"尝试删除不存在的密钥 {key[:10]}...")
                return False

            if len(self.keys) <= 1:
                self.logger.error("无法删除最后一个密钥")
                return False

            # 删除密钥
            del self.key_statuses[key]
            self.keys.remove(key)

            # 调整当前索引
            if self.current_index >= len(self.keys):
                self.current_index = 0

            self.logger.info(f"成功删除密钥 {key[:10]}... (剩余: {len(self.keys)})")
            return True

    def get_key_by_index(self, index: int) -> Optional[str]:
        """
        根据索引获取密钥

        Args:
            index: 密钥索引

        Returns:
            密钥字符串，如果索引无效则返回None
        """
        with self._lock:
            if 0 <= index < len(self.keys):
                return self.keys[index]
            return None

    def validate_key_format(self, key: str) -> Tuple[bool, str]:
        """
        验证密钥格式

        Args:
            key: 要验证的密钥

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not key or not key.strip():
            return False, "密钥不能为空"

        key = key.strip()

        # 检查长度
        if len(key) < 20:
            return False, "密钥长度太短，应至少20个字符"

        if len(key) > 100:
            return False, "密钥长度太长，应不超过100个字符"

        # 检查是否以AIza开头（Google API密钥格式）
        if not key.startswith('AIza'):
            return False, "密钥格式不正确，应以'AIza'开头"

        # 检查字符集（只允许字母、数字、下划线、连字符）
        import re
        if not re.match(r'^[A-Za-z0-9_-]+$', key):
            return False, "密钥包含无效字符，只允许字母、数字、下划线和连字符"

        return True, ""
