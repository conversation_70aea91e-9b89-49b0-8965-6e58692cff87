{"name": "qrcode-terminal", "keywords": ["ansi", "ascii", "qrcode", "console"], "version": "0.12.0", "description": "QRCodes, in the terminal", "homepage": "https://github.com/gtanner/qrcode-terminal", "repository": {"type": "git", "url": "https://github.com/gtanner/qrcode-terminal"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://github.com/gtanner"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/mwbrooks"}], "licenses": [{"type": "Apache 2.0"}], "main": "./lib/main", "bin": {"qrcode-terminal": "./bin/qrcode-terminal.js"}, "preferGlobal": false, "devDependencies": {"sinon": "*", "mocha": "*", "expect.js": "*", "jshint": "*"}, "scripts": {"test": "./node_modules/jshint/bin/jshint lib vendor && node example/basic.js && ./node_modules/mocha/bin/mocha -R nyan"}}