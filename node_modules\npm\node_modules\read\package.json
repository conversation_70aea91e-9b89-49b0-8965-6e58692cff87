{"name": "read", "version": "4.1.0", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/read.d.ts", "default": "./dist/esm/read.js"}, "require": {"types": "./dist/commonjs/read.d.ts", "default": "./dist/commonjs/read.js"}}}, "type": "module", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/read.ts"}}, "dependencies": {"mute-stream": "^2.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "@types/mute-stream": "^0.0.4", "@types/tap": "^15.0.11", "@typescript-eslint/parser": "^8.0.1", "c8": "^10.1.2", "eslint-import-resolver-typescript": "^3.6.1", "tap": "^16.3.9", "ts-node": "^10.9.1", "tshy": "^3.0.2", "typescript": "^5.2.2"}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "author": "GitHub Inc.", "description": "read(1) for node programs", "repository": {"type": "git", "url": "git+https://github.com/npm/read.git"}, "license": "ISC", "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "c8 tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": true, "typescript": true}, "main": "./dist/commonjs/read.js", "types": "./dist/commonjs/read.d.ts", "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "files": ["dist/"], "module": "./dist/esm/read.js"}