"""
调试STR_POSITION函数解析
"""

import sqlglot
from sqlglot import expressions as exp
from sql_deep_parser.parser import DeepSQLParser

def debug_str_position():
    # 简单的STR_POSITION表达式
    sql = "SELECT * FROM test WHERE STR_POSITION(a.出院诊断名称, '癌') > 0"
    
    print("测试SQL:")
    print(sql)
    print("\n" + "="*50)
    
    # 解析AST
    ast = sqlglot.parse_one(sql)
    print("AST结构:")
    print(ast)
    print("\n" + "="*50)
    
    # 找到WHERE子句
    where_clause = ast.find(exp.Where)
    if where_clause:
        print("WHERE子句:")
        print(where_clause.this)
        print(f"WHERE表达式类型: {type(where_clause.this)}")
        print("\n" + "="*50)
        
        # 检查二元表达式
        if isinstance(where_clause.this, exp.GT):
            binary_expr = where_clause.this
            print("二元表达式:")
            print(f"  左侧: {binary_expr.left}")
            print(f"  左侧类型: {type(binary_expr.left)}")
            print(f"  右侧: {binary_expr.right}")
            print(f"  右侧类型: {type(binary_expr.right)}")
            print("\n" + "="*50)
            
            # 检查左侧是否为Anonymous函数
            if isinstance(binary_expr.left, exp.Anonymous):
                func = binary_expr.left
                print("函数调用:")
                print(f"  函数名: {func.this}")
                print(f"  参数数量: {len(func.expressions)}")
                for i, arg in enumerate(func.expressions):
                    print(f"  参数 {i+1}: {arg} (类型: {type(arg)})")
                print("\n" + "="*50)
                
                # 测试字段引用提取
                parser = DeepSQLParser()
                field_ref = parser._extract_field_reference(binary_expr.left)
                print(f"字段引用提取结果: {field_ref}")
                
                if field_ref:
                    print(f"  字段名: {field_ref.field_name}")
                    print(f"  字段类型: {field_ref.field_type}")
                    print(f"  表别名: {field_ref.table_alias}")
                    print(f"  原始表达式: {field_ref.original_expression}")

if __name__ == "__main__":
    debug_str_position()
