#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医院规则推荐系统Bug修复测试脚本
测试内容：
1. 数据库字段长度限制修复
2. 匹配项目字段显示修复
3. 字符串截断功能测试

执行前请确保：
1. 已执行数据库升级脚本 sql/fix_hospital_rules_bugs.sql
2. 后端服务正在运行
3. 有测试数据可用

作者: Augment Agent
日期: 2025-07-20
"""

import sys
import os
import requests
import json
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入应用模块进行单元测试
try:
    from app import truncate_matched_items, truncate_reason
    print("✓ 成功导入后端函数")
except ImportError as e:
    print(f"✗ 导入后端函数失败: {e}")
    sys.exit(1)

# 测试配置
BASE_URL = "http://localhost:5000"
TEST_HOSPITAL_ID = 1  # 请根据实际情况修改

def print_test_header(test_name):
    """打印测试标题"""
    print(f"\n{'='*60}")
    print(f"测试: {test_name}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")

def test_string_truncation_functions():
    """测试字符串截断函数"""
    print_test_header("字符串截断函数测试")
    
    # 测试匹配项目截断函数
    print("\n1. 测试匹配项目截断函数")
    
    # 测试正常长度
    normal_items = "项目A、项目B、项目C"
    result = truncate_matched_items(normal_items)
    assert result == normal_items, f"正常长度测试失败: {result}"
    print(f"✓ 正常长度测试通过: {result}")
    
    # 测试超长字符串
    long_items = "、".join([f"超长项目名称{i:04d}" for i in range(500)])  # 生成超长字符串
    result = truncate_matched_items(long_items, max_length=100)
    assert len(result) <= 100, f"超长字符串截断失败: 长度={len(result)}"
    assert result.endswith("..."), f"截断标记缺失: {result}"
    print(f"✓ 超长字符串截断测试通过: 长度={len(result)}, 结果={result[:50]}...")
    
    # 测试空值
    result = truncate_matched_items(None)
    assert result is None, f"空值测试失败: {result}"
    print("✓ 空值测试通过")
    
    # 测试推荐原因截断函数
    print("\n2. 测试推荐原因截断函数")
    
    long_reason = "这是一个非常长的推荐原因" * 100
    result = truncate_reason(long_reason, max_length=50)
    assert len(result) <= 50, f"推荐原因截断失败: 长度={len(result)}"
    assert result.endswith("..."), f"截断标记缺失: {result}"
    print(f"✓ 推荐原因截断测试通过: 长度={len(result)}")

def test_api_endpoints():
    """测试API端点"""
    print_test_header("API端点测试")
    
    # 测试获取所有规则API
    print("\n1. 测试获取所有规则API")
    try:
        response = requests.get(f"{BASE_URL}/api/hospital-rules/all/{TEST_HOSPITAL_ID}")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                rules = data.get('rules', [])
                print(f"✓ 获取所有规则成功: 共{len(rules)}条规则")
                
                # 检查匹配项目字段
                if rules:
                    first_rule = rules[0]
                    if '匹配项目' in first_rule:
                        print(f"✓ 匹配项目字段存在: {first_rule['匹配项目']}")
                    else:
                        print("✗ 匹配项目字段缺失")
                        
                    # 显示前3条规则的匹配项目信息
                    for i, rule in enumerate(rules[:3]):
                        matched_items = rule.get('匹配项目', '未知')
                        print(f"  规则{i+1}: ID={rule.get('规则ID')}, 匹配项目={matched_items}")
            else:
                print(f"✗ API返回错误: {data.get('error')}")
        else:
            print(f"✗ API请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"✗ API测试异常: {e}")
    
    # 测试获取已采用规则API
    print("\n2. 测试获取已采用规则API")
    try:
        response = requests.get(f"{BASE_URL}/api/hospital-rules/adopted/{TEST_HOSPITAL_ID}")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                rules = data.get('rules', [])
                print(f"✓ 获取已采用规则成功: 共{len(rules)}条规则")
                
                # 检查匹配项目字段
                for i, rule in enumerate(rules[:3]):
                    matched_items = rule.get('匹配项目', '未知')
                    print(f"  已采用规则{i+1}: ID={rule.get('规则ID')}, 匹配项目={matched_items}")
            else:
                print(f"✗ API返回错误: {data.get('error')}")
        else:
            print(f"✗ API请求失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"✗ API测试异常: {e}")

def test_database_field_length():
    """测试数据库字段长度"""
    print_test_header("数据库字段长度测试")
    
    print("请手动执行以下SQL查询来验证数据库字段长度修改：")
    print("""
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        DATA_LENGTH
    FROM USER_TAB_COLUMNS 
    WHERE TABLE_NAME = '医院适用规则表' 
    AND COLUMN_NAME IN ('匹配项目', '推荐原因')
    ORDER BY COLUMN_NAME;
    """)
    
    print("\n预期结果：")
    print("- 匹配项目字段: VARCHAR2, 长度=4000")
    print("- 推荐原因字段: VARCHAR2, 长度=2000")

def test_long_string_insertion():
    """测试长字符串插入（需要有测试医院数据）"""
    print_test_header("长字符串插入测试")
    
    print("此测试需要手动触发规则生成来验证长字符串处理...")
    print("请在前端页面执行以下操作：")
    print("1. 选择一个有大量收费项目的医院")
    print("2. 点击'生成规则推荐'")
    print("3. 观察是否出现ORA-01461错误")
    print("4. 检查生成的推荐记录中匹配项目字段是否正确显示")

def generate_test_report():
    """生成测试报告"""
    print_test_header("测试报告")
    
    print("修复内容总结：")
    print("1. ✓ 数据库表结构修复：")
    print("   - 匹配项目字段从VARCHAR2(500)扩展到VARCHAR2(4000)")
    print("   - 推荐原因字段从VARCHAR2(500)扩展到VARCHAR2(2000)")
    
    print("\n2. ✓ 后端代码修复：")
    print("   - 添加truncate_matched_items()函数处理匹配项目字段长度")
    print("   - 添加truncate_reason()函数处理推荐原因字段长度")
    print("   - 在插入和更新数据库时使用截断函数")
    print("   - 确保API正确返回匹配项目字段")
    
    print("\n3. ✓ 前端代码修复：")
    print("   - 修复'所有规则'页面直接使用API返回的匹配项目字段")
    print("   - 修复'已采用规则'页面直接使用API返回的匹配项目字段")
    print("   - 不再从推荐原因中重新提取匹配项目信息")
    
    print("\n验证步骤：")
    print("1. 执行数据库升级脚本: sql/fix_hospital_rules_bugs.sql")
    print("2. 重启后端服务")
    print("3. 在前端测试规则生成和显示功能")
    print("4. 检查是否还有ORA-01461错误")
    print("5. 验证匹配项目字段显示正确")

def main():
    """主测试函数"""
    print("医院规则推荐系统Bug修复测试")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 测试字符串截断函数
        test_string_truncation_functions()
        
        # 2. 测试API端点
        test_api_endpoints()
        
        # 3. 测试数据库字段长度
        test_database_field_length()
        
        # 4. 测试长字符串插入
        test_long_string_insertion()
        
        # 5. 生成测试报告
        generate_test_report()
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
