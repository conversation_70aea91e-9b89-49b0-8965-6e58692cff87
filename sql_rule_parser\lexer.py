"""
SQL词法分析器
"""

import re
from typing import List, Iterator
from .models import S<PERSON>Token, TokenType


class SQLLexer:
    """SQL词法分析器"""
    
    # SQL关键字
    KEYWORDS = {
        'SELECT', 'FROM', 'WHERE', 'JOIN', 'INNER', 'LEFT', 'RIGHT', 'OUTER',
        'ON', 'AND', 'OR', 'NOT', 'IN', 'EXISTS', 'BETWEEN', 'LIKE', 'ILIKE',
        'IS', 'NULL', 'AS', 'DISTINCT', 'ORDER', 'BY', 'GROUP', 'HAVING',
        'UNION', 'INTERSECT', 'EXCEPT', 'WITH', 'CASE', 'WHEN', 'THEN', 'ELSE',
        'END', 'IF', 'COALESCE', 'CAST', 'EXTRACT', 'COUNT', 'SUM', 'AVG',
        'MIN', 'MAX', 'ROW_NUMBER', 'OVER', 'PARTITION', 'WINDOW', 'ANY', 'ALL',
        'SOME', 'ARRAY', 'DATE', 'TIME', 'TIMESTAMP', 'INTERVAL', 'TO_CHAR',
        'TO_DATE', 'NUMERIC', 'INTEGER', 'VARCHAR', 'TEXT', 'BOOLEAN'
    }
    
    # 操作符
    OPERATORS = {
        '=', '!=', '<>', '<', '>', '<=', '>=', '+', '-', '*', '/', '%',
        '||', '::', '->', '->>', '#>', '#>>', '@>', '<@', '?', '?&', '?|'
    }
    
    # 标点符号
    PUNCTUATION = {'(', ')', '[', ']', '{', '}', ',', ';', '.', ':'}
    
    def __init__(self):
        self.text = ""
        self.position = 0
        self.tokens = []
        
    def tokenize(self, text: str) -> List[SQLToken]:
        """对SQL文本进行词法分析"""
        self.text = text
        self.position = 0
        self.tokens = []
        
        while self.position < len(self.text):
            self._skip_whitespace()
            
            if self.position >= len(self.text):
                break
                
            # 处理注释
            if self._match_comment():
                continue
                
            # 处理字符串
            if self._match_string():
                continue
                
            # 处理数字
            if self._match_number():
                continue
                
            # 处理标识符和关键字
            if self._match_identifier():
                continue
                
            # 处理操作符
            if self._match_operator():
                continue
                
            # 处理标点符号
            if self._match_punctuation():
                continue
                
            # 未知字符，跳过
            self.position += 1
            
        self.tokens.append(SQLToken(TokenType.EOF, "", self.position))
        return self.tokens
    
    def _current_char(self) -> str:
        """获取当前字符"""
        if self.position >= len(self.text):
            return ""
        return self.text[self.position]
    
    def _peek_char(self, offset: int = 1) -> str:
        """预览字符"""
        pos = self.position + offset
        if pos >= len(self.text):
            return ""
        return self.text[pos]
    
    def _skip_whitespace(self):
        """跳过空白字符"""
        while self.position < len(self.text) and self.text[self.position].isspace():
            self.position += 1
    
    def _match_comment(self) -> bool:
        """匹配注释"""
        if self._current_char() == '-' and self._peek_char() == '-':
            start_pos = self.position
            # 单行注释
            while self.position < len(self.text) and self.text[self.position] != '\n':
                self.position += 1
            comment_text = self.text[start_pos:self.position]
            self.tokens.append(SQLToken(TokenType.COMMENT, comment_text, start_pos))
            return True
            
        if self._current_char() == '/' and self._peek_char() == '*':
            start_pos = self.position
            self.position += 2
            # 多行注释
            while self.position < len(self.text) - 1:
                if self.text[self.position] == '*' and self.text[self.position + 1] == '/':
                    self.position += 2
                    break
                self.position += 1
            comment_text = self.text[start_pos:self.position]
            self.tokens.append(SQLToken(TokenType.COMMENT, comment_text, start_pos))
            return True
            
        return False
    
    def _match_string(self) -> bool:
        """匹配字符串"""
        if self._current_char() in ("'", '"'):
            quote = self._current_char()
            start_pos = self.position
            self.position += 1
            
            while self.position < len(self.text):
                if self.text[self.position] == quote:
                    self.position += 1
                    break
                elif self.text[self.position] == '\\':
                    self.position += 2  # 跳过转义字符
                else:
                    self.position += 1
                    
            string_text = self.text[start_pos:self.position]
            self.tokens.append(SQLToken(TokenType.STRING, string_text, start_pos))
            return True
            
        return False
    
    def _match_number(self) -> bool:
        """匹配数字"""
        if self._current_char().isdigit() or (self._current_char() == '.' and self._peek_char().isdigit()):
            start_pos = self.position
            
            # 整数部分
            while self.position < len(self.text) and self.text[self.position].isdigit():
                self.position += 1
                
            # 小数部分
            if self.position < len(self.text) and self.text[self.position] == '.':
                self.position += 1
                while self.position < len(self.text) and self.text[self.position].isdigit():
                    self.position += 1
                    
            # 科学计数法
            if self.position < len(self.text) and self.text[self.position].lower() == 'e':
                self.position += 1
                if self.position < len(self.text) and self.text[self.position] in '+-':
                    self.position += 1
                while self.position < len(self.text) and self.text[self.position].isdigit():
                    self.position += 1
                    
            number_text = self.text[start_pos:self.position]
            self.tokens.append(SQLToken(TokenType.NUMBER, number_text, start_pos))
            return True
            
        return False
    
    def _match_identifier(self) -> bool:
        """匹配标识符和关键字"""
        if self._current_char().isalpha() or self._current_char() == '_' or ord(self._current_char()) > 127:
            start_pos = self.position
            
            while (self.position < len(self.text) and 
                   (self.text[self.position].isalnum() or 
                    self.text[self.position] in '_' or 
                    ord(self.text[self.position]) > 127)):
                self.position += 1
                
            identifier_text = self.text[start_pos:self.position]
            
            # 检查是否为关键字
            token_type = TokenType.KEYWORD if identifier_text.upper() in self.KEYWORDS else TokenType.IDENTIFIER
            self.tokens.append(SQLToken(token_type, identifier_text, start_pos))
            return True
            
        return False
    
    def _match_operator(self) -> bool:
        """匹配操作符"""
        # 检查双字符操作符
        two_char = self.text[self.position:self.position + 2]
        if two_char in self.OPERATORS:
            self.tokens.append(SQLToken(TokenType.OPERATOR, two_char, self.position))
            self.position += 2
            return True
            
        # 检查单字符操作符
        one_char = self._current_char()
        if one_char in self.OPERATORS:
            self.tokens.append(SQLToken(TokenType.OPERATOR, one_char, self.position))
            self.position += 1
            return True
            
        return False
    
    def _match_punctuation(self) -> bool:
        """匹配标点符号"""
        if self._current_char() in self.PUNCTUATION:
            self.tokens.append(SQLToken(TokenType.PUNCTUATION, self._current_char(), self.position))
            self.position += 1
            return True
            
        return False
