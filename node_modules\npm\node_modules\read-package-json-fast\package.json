{"name": "read-package-json-fast", "version": "4.0.0", "description": "Like read-package-json, but faster", "main": "lib/index.js", "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "tap": "^16.3.0"}, "dependencies": {"json-parse-even-better-errors": "^4.0.0", "npm-normalize-package-bin": "^4.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/npm/read-package-json-fast.git"}, "files": ["bin/", "lib/"], "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}