# 导入所需的库和模块
from flask import Flask, request, render_template, redirect, url_for, flash, send_file, session
import os
import re
import webbrowser
import pandas as pd
from werkzeug.utils import secure_filename
import datetime
import io
import zipfile
import unicodedata
import oracledb
from contextlib import contextmanager
import logging
from typing import Optional, Dict, Any, Type
import time
from functools import wraps
import logging
from typing import Generator, Any
from psycopg_pool import ConnectionPool


# 创建Flask应用实例
app = Flask(__name__)
app.secret_key = 'your_secret_key_here'  # 设置密钥用于flash消息和session

# 设置上传文件夹
UPLOAD_FOLDER = 'templates'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
ALLOWED_EXTENSIONS = {'sql'}  # 允许上传的文件扩展名

# 检查文件扩展名是否允许
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# 清理文件名，保留合法字符（包括中文）
def sanitize_filename(filename):
    # 使用 NFKC 标准化 Unicode 字符
    filename = unicodedata.normalize('NFKC', filename)
    # 移除不允许的字符，保留中文、字母、数字、下划线、连字符和点
    filename = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9_\-\.]', '_', filename)
    # 确保文件名不超过 255 字符
    return filename[:255]

# 配置连接池
def init_connection_pool(pool_config: Dict[str, Any]) -> oracledb.ConnectionPool:
    try:
        pool = oracledb.create_pool(
            user=pool_config['username'],
            password=pool_config['password'],
            dsn=pool_config['dsn'],
            min=2,
            max=5,
            increment=1,
            getmode=oracledb.POOL_GETMODE_WAIT,
            wait_timeout=10000,
            timeout=300,
            retry_count=3,
            retry_delay=2,
            max_lifetime_session=28800
        )
        return pool
    except Exception as e:
        logging.error(f"Failed to initialize connection pool: {str(e)}")
        raise

@contextmanager
def get_connection(pool: oracledb.ConnectionPool):
    """获取数据库连接的上下文管理器，增强版本带有重试机制和更好的错误处理"""
    connection = None
    retry_count = 0
    max_retries = 3
    
    while retry_count <= max_retries:
        try:    
            if retry_count > 0:
                logging.warning(f"尝试重新获取数据库连接，第 {retry_count} 次尝试")
                
            connection = pool.acquire()
            
            # 测试连接是否有效
            try:
                with connection.cursor() as test_cursor:
                    test_cursor.execute("SELECT 1 FROM DUAL")
                    test_cursor.fetchone()
            except Exception as test_error:
                logging.warning(f"连接测试失败，重新获取连接: {str(test_error)}")
                try:
                    if connection:
                        pool.release(connection)
                except:
                    pass
                retry_count += 1
                if retry_count > max_retries:
                    raise
                time.sleep(1)  # 稍等一会再重试
                continue
                
            # 连接有效，可以使用
            yield connection
            break  # 成功退出循环
            
        except oracledb.DatabaseError as e:
            error_message = str(e)
            if "not connected" in error_message.lower():
                logging.warning(f"数据库连接已断开: {error_message}")
                retry_count += 1
                if retry_count > max_retries:
                    logging.error(f"重试 {max_retries} 次后仍然无法连接到数据库")
                    raise
                time.sleep(retry_count)  # 增加的等待时间
            else:
                logging.error(f"数据库错误: {error_message}")
                raise
                
        except Exception as e:
            logging.error(f"获取数据库连接时出错: {str(e)}")
            raise
            
        finally:
            if connection:
                try:
                    pool.release(connection)
                except Exception as e:
                    logging.error(f"释放数据库连接时出错: {str(e)}")

@contextmanager
def get_pg_connection(poolpg: ConnectionPool):
    connection = None
    try:
        connection = poolpg.getconn()
        yield connection
    except Exception as e:
        logging.error(f"PostgreSQL error occurred: {str(e)}")
        raise
    finally:
        if connection:
            try:
                poolpg.putconn(connection)
            except Exception as e:
                logging.error(f"Error releasing PostgreSQL connection: {str(e)}")


def retry_on_error(max_retries=3, delay=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except (oracledb.DatabaseError, oracledb.InterfaceError) as e:
                    retries += 1
                    if retries == max_retries:
                        logging.error(f"Max retries reached for {func.__name__}: {str(e)}")
                        raise
                    logging.warning(f"Retry {retries} for {func.__name__} after error: {str(e)}")
                    time.sleep(delay * retries)
            return None
        return wrapper
    return decorator

@retry_on_error()
def execute_query(connection: oracledb.Connection, query: str, params: Optional[Dict] = None) -> pd.DataFrame:
    try:
        with connection.cursor() as cursor:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            columns = [col[0] for col in cursor.description]
            rows = cursor.fetchall()
            return pd.DataFrame(rows, columns=columns)
    except Exception as e:
        logging.error(f"Error executing query: {str(e)}\nQuery: {query}\nParams: {params}")
        raise

# 主页路由
@app.route('/')
def index():
    templates = list_sql_templates()  # 获取可用的SQL模板列表
    message = None
    generated_files = None
    
    # 检查是否有下载完成的消息
    if session.get('download_complete'):
        message = session.pop('message', None)
        generated_files = session.pop('generated_files', None)
        session.pop('download_complete', None)
    
    return render_template('index.html', templates=templates, message=message, generated_files=generated_files, variables=None, selected_template=None)

# 列出可用的SQL模板
def list_sql_templates():
    return [f for f in os.listdir('templates') if f.endswith('.sql')]

# 生成SQL路由
@app.route('/generate_sql', methods=['POST'])
def generate_sql():
    template_name = request.form['template']
    requirements = request.form.to_dict(flat=False)  # 获取所有输入的要求
    
    # 读取模板文件，指定编码为utf-8
    with open(f'templates/{template_name}', 'r', encoding='utf-8') as file:
        template = file.read()
    
    # 根据要求生成SQL
    sql_statements = generate_sql_from_template(template, requirements)
    
    # 保存 SQL 到 output 文件夹
    generated_files = save_sql_to_file(sql_statements, template_name, requirements)
    
    message = "SQL 生成成功！"
    # 将生成的SQL内容传递给模板
    generated_sql = sql_statements[0] if sql_statements else ""
    
    # 确保传递 template_name 和 variables
    return render_template('index.html', templates=list_sql_templates(), message=message, generated_files=generated_files, selected_template=template_name, generated_sql=generated_sql, variables=requirements.keys())

# 获取模板变量路由
@app.route('/template_variables', methods=['POST'])
def template_variables():
    selected_template = request.form.get('template')
    if not selected_template:  # 检查是否选择了有效模板
        return redirect(url_for('index'))  # 重定向回首页或处理逻辑
    # 读取模板文件，指定编码为utf-8
    with open(f'templates/{selected_template}', 'r', encoding='utf-8') as file:
        template = file.read()
    
    # 提取变量并去重
    variables = list(set(re.findall(r'\{(.*?)\}', template)))
    
    return render_template('index.html', templates=list_sql_templates(), variables=variables, template_name=selected_template, selected_template=selected_template)

# 上传Excel文件并生成SQL路由
@app.route('/upload_excel', methods=['POST'])
def upload_excel():
    excel_file = request.files['excel_file']
    template_name = request.form['template']
    
    # 读取 Excel 文件
    df = pd.read_excel(excel_file)
    
    # 生成 SQL 语句
    sql_statements = []
    for index, row in df.iterrows():
        requirements = row.to_dict()
        template = read_template(template_name)
        sql_statements.append(generate_sql_from_template(template, requirements)[0])
    
    # 保存 SQL 到 output 文件夹
    generated_files = save_sql_to_file(sql_statements, template_name, requirements)
    
    message = "SQL 批量生成成功！"
    
    return render_template('index.html', templates=list_sql_templates(), message=message, generated_files=generated_files, selected_template=template_name, variables=None)

# 读取模板文件
def read_template(template_name):
    with open(f'templates/{template_name}', 'r', encoding='utf-8') as file:
        return file.read()

# 根据模板和要求生成SQL
def generate_sql_from_template(template, requirements):
    for variable in re.findall(r'\{(.*?)\}', template):
        # 获取对应的输入值
        value = requirements.get(variable, [""])[0] if isinstance(requirements.get(variable), list) else requirements.get(variable, "")
        template = template.replace(f'{{{variable}}}', str(value).strip())
    
    return [template]

# 保存生成的SQL到文件
def save_sql_to_file(sql_statements, template_name, requirements):
    os.makedirs('output', exist_ok=True)
    generated_files = []
    
    # 去掉文件扩展名作为前缀
    template_prefix = os.path.splitext(template_name)[0]  
    
    variable_names = "_".join([str(req[0]).strip() for req in requirements.values() if str(req[0]).strip()])
    
    base_filename = f'{variable_names}' if variable_names else template_prefix
    
    for i, sql in enumerate(sql_statements):
        # 如果有多个 SQL 语句，在文件名后添加索引
        filename = f'{i + 1}.sql' if len(sql_statements) > 1 else f'{base_filename}.sql'
        with open(f'output/{filename}', 'w', encoding='utf-8') as f:
            f.write(sql)
        generated_files.append(filename)
    
    return generated_files

# 上传模板文件路由
@app.route('/upload_template', methods=['POST'])
def upload_template():
    if 'template_file' not in request.files:
        flash('No file part')
        return redirect(url_for('index'))
    file = request.files['template_file']
    if file.filename == '':
        flash('No selected file')
        return redirect(url_for('index'))
    if file and allowed_file(file.filename):
        filename = sanitize_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        flash(f'File {filename} uploaded successfully')
        return redirect(url_for('index'))
    flash('Invalid file type. Please upload a .sql file.')
    return redirect(url_for('index'))

# 批量生成SQL路由
@app.route('/batch_generate', methods=['POST'])
def batch_generate():
    action = request.form.get('action')
    if action == '批量生成SQL':
        return batch_generate_sql()
    elif action == '批量生成单独SQL':
        return batch_generate_separate_sql()
    else:
        flash('Invalid action')
        return redirect(url_for('index'))

# 批量生成合并SQL
def batch_generate_sql():
    if 'excel_file' not in request.files:
        flash('No file part')
        return redirect(url_for('index'))
    file = request.files['excel_file']
    if file.filename == '':
        flash('No selected file')
        return redirect(url_for('index'))
    if file and file.filename.endswith('.xlsx'):
        template_name = request.form['template']
        if not template_name:
            flash('No template selected')
            return redirect(url_for('index'))
        
        df = pd.read_excel(file)
        
        # 读取模板文件
        with open(f'templates/{template_name}', 'r', encoding='utf-8') as template_file:
            template = template_file.read()
        
        # 获取模板变量
        template_variables = get_template_variables(template_name)
        
        # 检查 Excel 文件是否包含所有必要的列
        missing_columns = [var for var in template_variables if var not in df.columns]
        if missing_columns:
            flash(f'Excel 文件缺少以下列: {", ".join(missing_columns)}')
            return redirect(url_for('index'))
        
        # 生成 SQL 语句
        sql_statements = []
        for _, row in df.iterrows():
            requirements = row.to_dict()
            sql = generate_sql_from_template(template, requirements)[0]
            sql_statements.append(sql)
        
        # 用分号连接所有 SQL 语句
        combined_sql = ';\n\n'.join(sql_statements) + ';'
        
        # 生成时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存生成的SQL
        output_filename = f'batch_output_{os.path.splitext(file.filename)[0]}_{timestamp}.sql'
        output_path = f'output/{output_filename}'
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(combined_sql)
        
        # 创建ZIP文件
        zip_filename = f'batch_sql_{timestamp}.zip'
        zip_path = f'output/{zip_filename}'
        with zipfile.ZipFile(zip_path, 'w') as zf:
            zf.write(output_path, output_filename)
        
        message = "批量 SQL 生成成功！"
        download_link = url_for('download_file', filename=zip_filename)
        return render_template('index.html', templates=list_sql_templates(), message=message, 
                               generated_files=[output_filename], selected_template=template_name, 
                               download_link=download_link)
    else:
        flash('Invalid file type. Please upload an Excel file.')
        return redirect(url_for('index'))

# 批量生成单独SQL文件
def batch_generate_separate_sql():
    if 'excel_file' not in request.files:
        flash('No file part')
        return redirect(url_for('index'))
    file = request.files['excel_file']
    if file.filename == '':
        flash('No selected file')
        return redirect(url_for('index'))
    if file and file.filename.endswith('.xlsx'):
        template_name = request.form['template']
        if not template_name:
            flash('No template selected')
            return redirect(url_for('index'))
        
        df = pd.read_excel(file)
        
        # 读取模板文件
        with open(f'templates/{template_name}', 'r', encoding='utf-8') as template_file:
            template = template_file.read()
        
        # 获取模板变量
        template_variables = get_template_variables(template_name)
        
        # 检查Excel文件是否包含所有必要的列
        missing_columns = [var for var in template_variables if var not in df.columns]
        if missing_columns:
            flash(f'Excel 文件缺少以下列: {", ".join(missing_columns)}')
            return redirect(url_for('index'))
        
        # 生成时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建新的输出文件夹
        output_folder = f'output/batch_{os.path.splitext(file.filename)[0]}_{timestamp}'
        os.makedirs(output_folder, exist_ok=True)
        
        # 获取模板名称（不包括.sql扩展名）
        template_prefix = os.path.splitext(template_name)[0]
        
        # 生成SQL语句并保存到单独的文件
        generated_files = []
        for index, row in df.iterrows():
            requirements = row.to_dict()
            sql = generate_sql_from_template(template, requirements)[0]
            
            # 生成文件名
            variable_names = "_".join([str(requirements.get(var, '')).strip() for var in template_variables if str(requirements.get(var, '')).strip()])
            
            # 新增：添加用户指定的列到文件名
            filename_columns = request.form.get('filename_columns', '').split(',')  # 获取用户指定的文件名列
            additional_name_parts = "_".join([str(row.get(col, '')).strip() for col in filename_columns if col.strip()])
            
            # 将 additional_name_parts 放到 variable_names 前面
            if additional_name_parts:
                variable_names = f"{additional_name_parts}_{variable_names}"
                #variable_names += f"_{additional_name_parts}" #将 additional_name_parts 放到 variable_names 后面
            sanitized_variable_names = sanitize_filename(variable_names)

            # 截断文件名，最大长度设为 100 个字符（增加长度以适应中文字符）
            max_filename_length = 100
            if len(sanitized_variable_names) > max_filename_length:
                sanitized_variable_names = sanitized_variable_names[:max_filename_length]

            # 添加 Excel 行序号（假设 index 是从 0 开始的 Excel 行索引）
            excel_row_number = index + 2  # 加 2 是因为 Excel 行号从 1 开始，而且通常有标题行
            filename = f'{excel_row_number:03d}_{template_prefix}_{sanitized_variable_names}.sql'
            
            # 保存SQL到文件
            file_path = os.path.join(output_folder, filename)
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(sql)
                generated_files.append(filename)
            except OSError as e:
                flash(f'Error saving file {filename}: {str(e)}')
                continue
        
        # 创建ZIP文件
        zip_filename = f'batch_separate_sql_{timestamp}.zip'
        zip_path = f'output/{zip_filename}'
        with zipfile.ZipFile(zip_path, 'w') as zf:
            for filename in generated_files:
                zf.write(os.path.join(output_folder, filename), filename)
        
        message = "批量生成单独 SQL 成功！"
        download_link = url_for('download_file', filename=zip_filename)
        return render_template('index.html', templates=list_sql_templates(), message=message, 
                               generated_files=generated_files, selected_template=template_name, 
                               download_link=download_link)
    else:
        flash('Invalid file type. Please upload an Excel file.')
        return redirect(url_for('index'))

# 下载文件路由
@app.route('/download/<filename>')
def download_file(filename):
    return send_file(f'output/{filename}', as_attachment=True)

# 获取模板变量
def get_template_variables(template_name):
    with open(f'templates/{template_name}', 'r', encoding='utf-8') as file:
        template = file.read()
    return list(set(re.findall(r'\{(.*?)\}', template)))

# 主程序入口
if __name__ == '__main__':
    #webbrowser.open('http://127.0.0.1:5000/')  # 自动打开浏览器（已注释）
    port = int(os.environ.get('PORT', 5000))  # 获取端口号，默认为5000

    app.run(host='0.0.0.0', debug=True)  # 启动应用，监听所有IP，开启调试模式
