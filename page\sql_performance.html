<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL性能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        :root {
            --primary: #0078D4;
            --primary-hover: #106EBE;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --border-color: rgba(0, 0, 0, 0.1);
            --radius: 12px;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            padding: 2rem;
        }

        .card {
            background: var(--card-bg);
            border-radius: var(--radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
            margin-bottom: 1.5rem;
        }

        .results-table {
            width: 100%;
            margin-top: 1.5rem;
        }

        .results-table th {
            background-color: #f8f9fa;
            padding: 0.75rem;
        }

        .execution-time {
            font-weight: bold;
        }

        .slow-query {
            color: #dc3545;
        }

        .fast-query {
            color: #198754;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="mb-4">
            <a href="{{ url_for('index') }}" class="btn btn-link">
                <i class="bi bi-arrow-left"></i> 返回主页
            </a>
        </nav>
        
        <h1 class="mb-4">SQL性能测试</h1>
        
        <div class="card">
            <h5 class="card-title mb-3">数据库连接配置</h5>
            <form id="performanceTestForm">
                <div class="row g-3 mb-3">
                    <div class="col-md-3">
                        <select class="form-select" name="db_type" id="dbType" required>
                            <option value="">选择数据库类型</option>
                            <option value="postgresql">PostgreSQL</option>
                            <option value="oracle">Oracle</option>
                            <option value="mysql">MySQL</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="db_username" placeholder="数据库用户名" >
                    </div>
                    <div class="col-md-3">
                        <input type="password" class="form-control" name="db_password" placeholder="数据库密码" >
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="db_dsn" placeholder="数据库连接串" >
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">SQL文件</label>
                    <div class="input-group">
                        <input type="file" class="form-control" id="sqlFiles" name="sql_files" accept=".sql" multiple required>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <button type="button" class="btn btn-success" onclick="saveConfig()">
                        <i class="bi bi-save"></i> 保存配置
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-play"></i> 开始测试
                    </button>
                </div>
            </form>
        </div>

        <div class="card">
            <h5 class="card-title mb-3">测试结果</h5>
            <div class="table-responsive">
                <table class="table table-hover" id="resultsTable">
                    <thead>
                        <tr>
                            <th>SQL文件名</th>
                            <th>执行时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="resultsBody"></tbody>
                </table>
            </div>
            <div class="mt-3">
                <button class="btn btn-success" onclick="exportResults()">
                    <i class="bi bi-download"></i> 导出结果
                </button>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('performanceTestForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            
            try {
                const response = await fetch('/api/sql/performance-test', {
                    method: 'POST',
                    body: formData
                });
                
                const results = await response.json();
                displayResults(results);
            } catch (error) {
                console.error('测试执行失败:', error);
                alert('测试执行失败: ' + error.message);
            }
        });

        function displayResults(results) {
            const tbody = document.getElementById('resultsBody');
            tbody.innerHTML = '';
            
            results.forEach(result => {
                const row = document.createElement('tr');
                const timeClass = result.execution_time > 5 ? 'slow-query' : 'fast-query';
                const escapedSql = result.sql.replace(/'/g, "\\'").replace(/"/g, '\\"');
                
                row.innerHTML = `
                    <td>${result.filename}</td>
                    <td class="execution-time ${timeClass}">${result.execution_time.toFixed(3)}</td>
                    <td>${result.status}</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick='viewSqlDetails("${result.sql}")'>
                            查看详情
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function viewSqlDetails(sql) {
            const modal = new bootstrap.Modal(document.createElement('div'));
            modal.element.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">SQL详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <pre><code>${sql}</code></pre>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal.element);
            modal.show();
        }

        function exportResults() {
            const rows = Array.from(document.getElementById('resultsBody').getElementsByTagName('tr'));
            const csvContent = rows.map(row => {
                return Array.from(row.getElementsByTagName('td'))
                    .slice(0, 3) // 排除操作列
                    .map(cell => cell.textContent)
                    .join(',');
            }).join('\n');
            
            const blob = new Blob([`文件名,执行时间(秒),状态\n${csvContent}`], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'sql_performance_results.csv';
            a.click();
            window.URL.revokeObjectURL(url);
        }

        async function saveConfig() {
            const formData = new FormData(document.getElementById('performanceTestForm'));
            const config = {
                db_username: formData.get('db_username'),
                db_password: formData.get('db_password'),
                db_dsn: formData.get('db_dsn')
            };

            try {
                const response = await fetch('/save_db_config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });
                
                const result = await response.json();
                if (result.success) {
                    alert('配置保存成功');
                } else {
                    alert('配置保存失败: ' + result.message);
                }
            } catch (error) {
                alert('配置保存失败: ' + error.message);
            }
        }
    </script>
</body>
</html> 