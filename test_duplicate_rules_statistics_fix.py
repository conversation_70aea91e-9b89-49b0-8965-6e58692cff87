#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复规则统计修复验证脚本

验证修复后的重复规则检测功能：
1. 统计数字是否正确显示
2. 前端过滤功能是否正常工作
3. 分组结果是否按类型正确分组

使用方法：
python test_duplicate_rules_statistics_fix.py [hospital_id]

作者: Augment Agent
日期: 2025-07-22
"""

import sys
import requests
import json
from datetime import datetime
from collections import defaultdict

def test_duplicate_rules_statistics(hospital_id):
    """测试重复规则统计功能"""
    print("=" * 80)
    print("测试: 重复规则统计修复验证")
    print("=" * 80)
    
    base_url = "http://localhost:5000"
    
    try:
        print(f"1.1 调用重复规则分析API，医院ID: {hospital_id}")
        
        response = requests.get(f"{base_url}/api/hospital-rules/duplicate-analysis/{hospital_id}", timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                duplicate_groups = result.get('duplicate_groups', [])
                total_rules = result.get('total_rules', 0)
                duplicate_rules = result.get('duplicate_rules', 0)
                duplicate_groups_count = result.get('duplicate_groups_count', 0)
                
                print(f"✓ API调用成功")
                print(f"✓ 总规则数: {total_rules}")
                print(f"✓ 重复规则数: {duplicate_rules}")
                print(f"✓ 重复组数: {duplicate_groups_count}")
                
                if duplicate_groups:
                    return verify_statistics_accuracy(duplicate_groups)
                else:
                    print("✓ 该医院暂无重复规则")
                    return True
            else:
                print(f"✗ API返回错误: {result.get('error')}")
                return False
        else:
            print(f"✗ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return False

def verify_statistics_accuracy(duplicate_groups):
    """验证统计数据的准确性"""
    print(f"\n统计准确性验证:")
    print("-" * 80)
    
    # 统计各类别的分组数量
    names1_count = 0
    names2_count = 0
    category_details = defaultdict(int)
    
    for group in duplicate_groups:
        category = group.get('category', '')
        rule_type = group.get('rule_type', '未知')
        
        # 统计详细分类
        category_details[category] += 1
        
        # 统计大类别
        if '医保名称1重复' in category:
            names1_count += 1
        elif '医保名称2重复' in category:
            names2_count += 1
    
    print(f"统计结果:")
    print(f"  医保名称1重复组: {names1_count} 个")
    print(f"  医保名称2重复组: {names2_count} 个")
    print(f"  总分组数: {len(duplicate_groups)} 个")
    
    print(f"\n详细分类统计:")
    for category, count in category_details.items():
        print(f"  {category}: {count} 个")
    
    # 验证前端过滤逻辑
    print(f"\n前端过滤逻辑验证:")
    
    # 模拟前端的过滤逻辑
    frontend_names1_count = len([g for g in duplicate_groups if '医保名称1重复' in g.get('category', '')])
    frontend_names2_count = len([g for g in duplicate_groups if '医保名称2重复' in g.get('category', '')])
    
    print(f"  前端统计 - 医保名称1重复: {frontend_names1_count} 个")
    print(f"  前端统计 - 医保名称2重复: {frontend_names2_count} 个")
    
    # 验证一致性
    if frontend_names1_count == names1_count and frontend_names2_count == names2_count:
        print(f"  ✓ 前后端统计一致性: 通过")
        return True
    else:
        print(f"  ✗ 前后端统计一致性: 失败")
        return False

def test_category_format_compatibility():
    """测试category格式兼容性"""
    print("\n" + "=" * 80)
    print("测试: Category格式兼容性")
    print("=" * 80)
    
    # 模拟新格式的category数据
    test_categories = [
        '医保名称1重复 (药品)',
        '医保名称1重复 (检验)',
        '医保名称2重复 (药品)',
        '医保名称2重复 (影像)',
        '医保名称1重复 (未知)'
    ]
    
    print("测试category格式:")
    for category in test_categories:
        print(f"  {category}")
    
    # 测试前端过滤逻辑
    print(f"\n前端过滤逻辑测试:")
    
    # 模拟前端的includes()逻辑
    names1_matches = [cat for cat in test_categories if '医保名称1重复' in cat]
    names2_matches = [cat for cat in test_categories if '医保名称2重复' in cat]
    
    print(f"  医保名称1重复匹配: {len(names1_matches)} 个")
    for match in names1_matches:
        print(f"    - {match}")
    
    print(f"  医保名称2重复匹配: {len(names2_matches)} 个")
    for match in names2_matches:
        print(f"    - {match}")
    
    # 验证过滤逻辑
    expected_names1 = 3  # 药品、检验、未知
    expected_names2 = 2  # 药品、影像
    
    if len(names1_matches) == expected_names1 and len(names2_matches) == expected_names2:
        print(f"  ✓ 过滤逻辑测试: 通过")
        return True
    else:
        print(f"  ✗ 过滤逻辑测试: 失败")
        return False

def test_frontend_display_simulation():
    """模拟前端显示测试"""
    print("\n" + "=" * 80)
    print("测试: 前端显示模拟")
    print("=" * 80)
    
    # 模拟API返回的数据
    mock_duplicate_groups = [
        {
            'group_id': 1,
            'category': '医保名称1重复 (药品)',
            'rule_type': '药品',
            'rule_count': 3,
            'rules': [{'规则ID': 101}, {'规则ID': 102}, {'规则ID': 103}]
        },
        {
            'group_id': 2,
            'category': '医保名称1重复 (检验)',
            'rule_type': '检验',
            'rule_count': 2,
            'rules': [{'规则ID': 201}, {'规则ID': 202}]
        },
        {
            'group_id': 3,
            'category': '医保名称2重复 (药品)',
            'rule_type': '药品',
            'rule_count': 2,
            'rules': [{'规则ID': 301}, {'规则ID': 302}]
        }
    ]
    
    print("模拟数据:")
    for group in mock_duplicate_groups:
        print(f"  分组{group['group_id']}: {group['category']} ({group['rule_count']}条规则)")
    
    # 模拟前端统计逻辑
    names1_groups = len([g for g in mock_duplicate_groups if '医保名称1重复' in g['category']])
    names2_groups = len([g for g in mock_duplicate_groups if '医保名称2重复' in g['category']])
    
    print(f"\n前端统计结果:")
    print(f"  医保名称1重复: {names1_groups} 组")
    print(f"  医保名称2重复: {names2_groups} 组")
    print(f"  总计: {names1_groups + names2_groups} 组")
    
    # 验证预期结果
    if names1_groups == 2 and names2_groups == 1:
        print(f"  ✓ 前端统计模拟: 通过")
        return True
    else:
        print(f"  ✗ 前端统计模拟: 失败")
        return False

def test_filter_functionality():
    """测试过滤功能"""
    print("\n" + "=" * 80)
    print("测试: 过滤功能")
    print("=" * 80)
    
    # 模拟分组数据
    test_groups = [
        {'category': '医保名称1重复 (药品)', 'visible': True},
        {'category': '医保名称1重复 (检验)', 'visible': True},
        {'category': '医保名称2重复 (药品)', 'visible': True},
        {'category': '医保名称2重复 (影像)', 'visible': True}
    ]
    
    print("测试过滤场景:")
    
    # 场景1: 显示全部
    print(f"  场景1 - 显示全部:")
    all_visible = len([g for g in test_groups if g['visible']])
    print(f"    可见分组: {all_visible} 个 ✓")
    
    # 场景2: 只显示医保名称1重复
    print(f"  场景2 - 只显示医保名称1重复:")
    names1_visible = len([g for g in test_groups if '医保名称1重复' in g['category']])
    print(f"    可见分组: {names1_visible} 个 ✓")
    
    # 场景3: 只显示医保名称2重复
    print(f"  场景3 - 只显示医保名称2重复:")
    names2_visible = len([g for g in test_groups if '医保名称2重复' in g['category']])
    print(f"    可见分组: {names2_visible} 个 ✓")
    
    print(f"  ✓ 过滤功能测试: 通过")
    return True

def main():
    """主测试函数"""
    hospital_id = 1
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("重复规则统计修复验证")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试医院ID: {hospital_id}")
    
    try:
        # 测试1: 重复规则统计
        success1 = test_duplicate_rules_statistics(hospital_id)
        
        # 测试2: Category格式兼容性
        success2 = test_category_format_compatibility()
        
        # 测试3: 前端显示模拟
        success3 = test_frontend_display_simulation()
        
        # 测试4: 过滤功能
        success4 = test_filter_functionality()
        
        # 输出测试结果
        print("\n" + "=" * 80)
        print("测试结果汇总")
        print("=" * 80)
        
        print(f"测试1 - 重复规则统计: {'✓ 通过' if success1 else '✗ 失败'}")
        print(f"测试2 - Category格式兼容性: {'✓ 通过' if success2 else '✗ 失败'}")
        print(f"测试3 - 前端显示模拟: {'✓ 通过' if success3 else '✗ 失败'}")
        print(f"测试4 - 过滤功能: {'✓ 通过' if success4 else '✗ 失败'}")
        
        if success1 and success2 and success3 and success4:
            print("\n🎉 所有测试通过！统计修复成功。")
            print("\n验证步骤:")
            print("1. 打开医院个性化规则推荐系统")
            print("2. 选择医院并点击'已采用'")
            print("3. 点击'重复规则审查'")
            print("4. 检查统计数字是否正确显示（不再是0）")
            print("5. 测试分类过滤功能是否正常工作")
            print("6. 验证分组标题是否显示类型信息")
            return True
        else:
            print("\n❌ 部分测试失败，请检查修复实现。")
            return False
            
    except Exception as e:
        print(f"\n✗ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
