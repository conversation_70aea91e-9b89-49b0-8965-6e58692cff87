-- 检查当前数据库表结构
-- 执行此脚本来确认字段长度是否已经修改

SELECT 
    '当前医院适用规则表字段结构' AS 检查项目,
    COLUMN_NAME AS 字段名,
    DATA_TYPE AS 数据类型,
    DATA_LENGTH AS 字段长度,
    NULLABLE AS 可空
FROM USER_TAB_COLUMNS 
WHERE TABLE_NAME = '医院适用规则表' 
AND COLUMN_NAME IN ('匹配项目', '推荐原因')
ORDER BY COLUMN_NAME;

-- 检查是否有超长数据
SELECT 
    '匹配项目字段长度检查' AS 检查项目,
    COUNT(*) AS 总记录数,
    COUNT(CASE WHEN LENGTH(匹配项目) > 500 THEN 1 END) AS 超过500字符的记录数,
    MAX(LENGTH(匹配项目)) AS 最大长度,
    AVG(LENGTH(匹配项目)) AS 平均长度
FROM 医院适用规则表 
WHERE 匹配项目 IS NOT NULL;

-- 显示最长的几条记录
SELECT 
    '最长匹配项目记录' AS 检查项目,
    适用ID,
    规则ID,
    LENGTH(匹配项目) AS 长度,
    SUBSTR(匹配项目, 1, 100) || '...' AS 匹配项目预览
FROM 医院适用规则表 
WHERE 匹配项目 IS NOT NULL
ORDER BY LENGTH(匹配项目) DESC
FETCH FIRST 5 ROWS ONLY;
