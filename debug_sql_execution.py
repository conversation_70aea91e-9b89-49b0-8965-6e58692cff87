#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试SQL执行功能
"""

import urllib.request
import json
import time
from datetime import datetime

def debug_sql_execution():
    """调试SQL执行功能"""
    base_url = "http://127.0.0.1:5001"
    
    print("=" * 70)
    print("调试SQL执行功能")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 1. 模拟前端生成SQL后的执行请求
    print("\n🔍 测试1: 模拟前端SQL执行请求（使用生成的SQL）")
    
    # 首先生成一个SQL
    print("   步骤1: 生成SQL...")
    try:
        generate_data = {
            "rule_ids": [193],  # 使用刚才生成的规则ID
            "template_path": "rule_pg_name_inpatient",
            "visit_type": "住院"
        }
        
        req_data = json.dumps(generate_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/generate_rule_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=30) as response:
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                data = json.loads(content)
                
                if data.get('success') and data.get('sql'):
                    generated_sql = data['sql']
                    print(f"   ✅ SQL生成成功，长度: {len(generated_sql)} 字符")
                    print(f"   SQL前100字符: {generated_sql[:100]}...")
                else:
                    print(f"   ❌ SQL生成失败: {data.get('error', '未知错误')}")
                    return
            else:
                print(f"   ❌ SQL生成HTTP错误: {response.getcode()}")
                return
    except Exception as e:
        print(f"   ❌ SQL生成异常: {str(e)}")
        return
    
    # 2. 测试在默认PostgreSQL中执行生成的SQL
    print("\n   步骤2: 在默认PostgreSQL中执行生成的SQL...")
    try:
        execute_data = {
            "sql": generated_sql,
            "database": "pg",
            "host": "default",
            "schema": ""  # 不指定schema
        }
        
        req_data = json.dumps(execute_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=30) as response:
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                data = json.loads(content)
                
                if data.get('success'):
                    print(f"   ✅ SQL执行成功!")
                    print(f"      数据库: {data.get('database', 'Unknown')}")
                    print(f"      主机: {data.get('host', 'Unknown')}")
                    print(f"      Schema: {data.get('schema', 'None')}")
                    print(f"      返回行数: {data.get('affected_rows', 0)}")
                else:
                    print(f"   ❌ SQL执行失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"   ❌ SQL执行HTTP错误: {response.getcode()}")
    except Exception as e:
        print(f"   ❌ SQL执行异常: {str(e)}")
    
    # 3. 测试在指定Schema中执行
    print("\n   步骤3: 在指定Schema中执行SQL...")
    try:
        execute_data = {
            "sql": generated_sql,
            "database": "pg",
            "host": "default",
            "schema": "ZQS_YY_ZQDXFSFLYY_7ZD"  # 指定schema
        }
        
        req_data = json.dumps(execute_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=30) as response:
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                data = json.loads(content)
                
                if data.get('success'):
                    print(f"   ✅ 在指定Schema中SQL执行成功!")
                    print(f"      数据库: {data.get('database', 'Unknown')}")
                    print(f"      主机: {data.get('host', 'Unknown')}")
                    print(f"      Schema: {data.get('schema', 'None')}")
                    print(f"      返回行数: {data.get('affected_rows', 0)}")
                else:
                    print(f"   ❌ 在指定Schema中SQL执行失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"   ❌ 在指定Schema中SQL执行HTTP错误: {response.getcode()}")
    except Exception as e:
        print(f"   ❌ 在指定Schema中SQL执行异常: {str(e)}")
    
    # 4. 测试简单SQL执行
    print("\n🔍 测试2: 简单SQL执行测试")
    simple_sqls = [
        {
            "name": "PostgreSQL简单查询",
            "sql": "SELECT 1 as test_column",
            "database": "pg",
            "host": "default",
            "schema": ""
        },
        {
            "name": "Oracle简单查询", 
            "sql": "SELECT 1 as test_column FROM dual",
            "database": "oracle",
            "host": "default",
            "schema": ""
        }
    ]
    
    for test_case in simple_sqls:
        print(f"\n   测试: {test_case['name']}")
        try:
            req_data = json.dumps({
                "sql": test_case['sql'],
                "database": test_case['database'],
                "host": test_case['host'],
                "schema": test_case['schema']
            }).encode('utf-8')
            
            req = urllib.request.Request(
                f"{base_url}/api/rules/execute_sql",
                data=req_data,
                headers={'Content-Type': 'application/json'},
                method='POST'
            )
            
            with urllib.request.urlopen(req, timeout=15) as response:
                if response.getcode() == 200:
                    content = response.read().decode('utf-8')
                    data = json.loads(content)
                    
                    if data.get('success'):
                        print(f"   ✅ {test_case['name']} 执行成功!")
                        print(f"      返回行数: {data.get('affected_rows', 0)}")
                        print(f"      数据: {data.get('data', [])}")
                    else:
                        print(f"   ❌ {test_case['name']} 执行失败: {data.get('error', 'Unknown error')}")
                else:
                    print(f"   ❌ {test_case['name']} HTTP错误: {response.getcode()}")
        except Exception as e:
            print(f"   ❌ {test_case['name']} 异常: {str(e)}")
    
    print("\n" + "=" * 70)
    print("SQL执行调试完成!")

if __name__ == "__main__":
    debug_sql_execution()
