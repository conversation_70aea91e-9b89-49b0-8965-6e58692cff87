{"version": "11.4.0", "name": "npm", "description": "a package manager for JavaScript", "workspaces": ["docs", "smoke-tests", "mock-globals", "mock-registry", "workspaces/*"], "files": ["bin/", "lib/", "index.js", "docs/content/", "docs/output/", "man/"], "keywords": ["install", "modules", "package manager", "package.json"], "homepage": "https://docs.npmjs.com/", "author": "GitHub Inc.", "repository": {"type": "git", "url": "git+https://github.com/npm/cli.git"}, "bugs": {"url": "https://github.com/npm/cli/issues"}, "directories": {"doc": "./doc", "man": "./man"}, "main": "./index.js", "bin": {"npm": "bin/npm-cli.js", "npx": "bin/npx-cli.js"}, "exports": {".": [{"default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "dependencies": {"@isaacs/string-locale-compare": "^1.1.0", "@npmcli/arborist": "^9.1.0", "@npmcli/config": "^10.3.0", "@npmcli/fs": "^4.0.0", "@npmcli/map-workspaces": "^4.0.2", "@npmcli/package-json": "^6.1.1", "@npmcli/promise-spawn": "^8.0.2", "@npmcli/redact": "^3.1.1", "@npmcli/run-script": "^9.1.0", "@sigstore/tuf": "^3.1.1", "abbrev": "^3.0.1", "archy": "~1.0.0", "cacache": "^19.0.1", "chalk": "^5.4.1", "ci-info": "^4.2.0", "cli-columns": "^4.0.0", "fastest-levenshtein": "^1.0.16", "fs-minipass": "^3.0.3", "glob": "^10.4.5", "graceful-fs": "^4.2.11", "hosted-git-info": "^8.1.0", "ini": "^5.0.0", "init-package-json": "^8.2.1", "is-cidr": "^5.1.1", "json-parse-even-better-errors": "^4.0.0", "libnpmaccess": "^10.0.1", "libnpmdiff": "^8.0.3", "libnpmexec": "^10.1.2", "libnpmfund": "^7.0.3", "libnpmorg": "^8.0.0", "libnpmpack": "^9.0.3", "libnpmpublish": "^11.0.0", "libnpmsearch": "^9.0.0", "libnpmteam": "^8.0.1", "libnpmversion": "^8.0.1", "make-fetch-happen": "^14.0.3", "minimatch": "^9.0.5", "minipass": "^7.1.1", "minipass-pipeline": "^1.2.4", "ms": "^2.1.2", "node-gyp": "^11.2.0", "nopt": "^8.1.0", "normalize-package-data": "^7.0.0", "npm-audit-report": "^6.0.0", "npm-install-checks": "^7.1.1", "npm-package-arg": "^12.0.2", "npm-pick-manifest": "^10.0.0", "npm-profile": "^11.0.1", "npm-registry-fetch": "^18.0.2", "npm-user-validate": "^3.0.0", "p-map": "^7.0.3", "pacote": "^21.0.0", "parse-conflict-json": "^4.0.0", "proc-log": "^5.0.0", "qrcode-terminal": "^0.12.0", "read": "^4.1.0", "semver": "^7.7.2", "spdx-expression-parse": "^4.0.0", "ssri": "^12.0.0", "supports-color": "^10.0.0", "tar": "^6.2.1", "text-table": "~0.2.0", "tiny-relative-date": "^1.3.0", "treeverse": "^3.0.0", "validate-npm-package-name": "^6.0.0", "which": "^5.0.0"}, "bundleDependencies": ["@isaacs/string-locale-compare", "@npmcli/arborist", "@npmcli/config", "@npmcli/fs", "@npmcli/map-workspaces", "@npmcli/package-json", "@npmcli/promise-spawn", "@npmcli/redact", "@npmcli/run-script", "@sigstore/tuf", "abbrev", "archy", "cacache", "chalk", "ci-info", "cli-columns", "fastest-le<PERSON><PERSON><PERSON>", "fs-minipass", "glob", "graceful-fs", "hosted-git-info", "ini", "init-package-json", "is-cidr", "json-parse-even-better-errors", "libnpmaccess", "libnpmdiff", "libnpmexec", "libnpmfund", "libnpmorg", "libnpmpack", "libnpmpublish", "libnpmsearch", "libnpmteam", "libnpmversion", "make-fetch-happen", "minimatch", "minipass", "minipass-pipeline", "ms", "node-gyp", "nopt", "normalize-package-data", "npm-audit-report", "npm-install-checks", "npm-package-arg", "npm-pick-manifest", "npm-profile", "npm-registry-fetch", "npm-user-validate", "p-map", "pacote", "parse-conflict-json", "proc-log", "qrcode-terminal", "read", "semver", "spdx-expression-parse", "ssri", "supports-color", "tar", "text-table", "tiny-relative-date", "treeverse", "validate-npm-package-name", "which"], "devDependencies": {"@npmcli/docs": "^1.0.0", "@npmcli/eslint-config": "^5.1.0", "@npmcli/git": "^6.0.3", "@npmcli/mock-globals": "^1.0.0", "@npmcli/mock-registry": "^1.0.0", "@npmcli/template-oss": "4.23.6", "@tufjs/repo-mock": "^3.0.1", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "ajv-formats-draft2019": "^1.6.1", "cli-table3": "^0.6.4", "diff": "^7.0.0", "nock": "^13.4.0", "npm-packlist": "^10.0.0", "remark": "^14.0.2", "remark-gfm": "^3.0.1", "remark-github": "^11.2.4", "rimraf": "^5.0.5", "spawk": "^1.7.1", "tap": "^16.3.9"}, "scripts": {"dependencies": "node scripts/bundle-and-gitignore-deps.js && node scripts/dependency-graph.js", "dumpconf": "env | grep npm | sort | uniq", "licenses": "npx licensee --production --errors-only", "test": "tap", "test:nocolor": "CI=true tap -Rclassic", "test-all": "node . run test -ws -iwr --if-present", "snap": "tap", "prepack": "node . run build -w docs", "posttest": "node . run lint", "lint": "node . run eslint", "lintfix": "node . run eslint -- --fix", "lint-all": "node . run lint -ws -iwr --if-present", "resetdeps": "node scripts/resetdeps.js", "rp-pull-request": "node scripts/update-authors.js", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "tap": {"test-env": ["LC_ALL=sk"], "timeout": 600, "nyc-arg": ["--exclude", "docs/**", "--exclude", "smoke-tests/**", "--exclude", "mock-globals/**", "--exclude", "mock-registry/**", "--exclude", "workspaces/**", "--exclude", "tap-snapshots/**"], "test-ignore": "^(docs|smoke-tests|mock-globals|mock-registry|workspaces)/"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.6", "content": "./scripts/template-oss/root.js"}, "license": "Artistic-2.0", "engines": {"node": "^20.17.0 || >=22.9.0"}}