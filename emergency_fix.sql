-- 紧急修复脚本 - 立即解决ORA-01461错误
-- 执行日期: 2025-07-20
-- 
-- 此脚本必须立即执行以解决生产问题

-- 1. 首先检查当前字段长度
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH
FROM USER_TAB_COLUMNS 
WHERE TABLE_NAME = '医院适用规则表' 
AND COLUMN_NAME IN ('匹配项目', '推荐原因');

-- 2. 立即修改字段长度（如果还没有修改）
-- 注意：如果字段已经是4000长度，这些语句会被忽略
BEGIN
    -- 修改匹配项目字段长度
    EXECUTE IMMEDIATE 'ALTER TABLE 医院适用规则表 MODIFY (匹配项目 VARCHAR2(4000))';
    DBMS_OUTPUT.PUT_LINE('匹配项目字段长度已修改为4000');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1441 THEN
            DBMS_OUTPUT.PUT_LINE('匹配项目字段长度已经是4000或更大');
        ELSE
            DBMS_OUTPUT.PUT_LINE('修改匹配项目字段失败: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    -- 修改推荐原因字段长度
    EXECUTE IMMEDIATE 'ALTER TABLE 医院适用规则表 MODIFY (推荐原因 VARCHAR2(2000))';
    DBMS_OUTPUT.PUT_LINE('推荐原因字段长度已修改为2000');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1441 THEN
            DBMS_OUTPUT.PUT_LINE('推荐原因字段长度已经是2000或更大');
        ELSE
            DBMS_OUTPUT.PUT_LINE('修改推荐原因字段失败: ' || SQLERRM);
        END IF;
END;
/

-- 3. 验证修改结果
SELECT 
    '修改后字段结构' AS 状态,
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH
FROM USER_TAB_COLUMNS 
WHERE TABLE_NAME = '医院适用规则表' 
AND COLUMN_NAME IN ('匹配项目', '推荐原因')
ORDER BY COLUMN_NAME;

-- 4. 临时处理现有超长数据（如果有的话）
-- 这是一个安全的截断操作，只处理确实超长的数据
UPDATE 医院适用规则表 
SET 匹配项目 = SUBSTR(匹配项目, 1, 3997) || '...'
WHERE LENGTH(匹配项目) > 4000;

UPDATE 医院适用规则表 
SET 推荐原因 = SUBSTR(推荐原因, 1, 1997) || '...'
WHERE LENGTH(推荐原因) > 2000;

-- 5. 提交更改
COMMIT;

-- 6. 显示处理结果
SELECT 
    '处理完成' AS 状态,
    '字段长度已扩展，超长数据已截断' AS 结果,
    SYSDATE AS 完成时间
FROM DUAL;

-- 7. 重新收集统计信息
ANALYZE TABLE 医院适用规则表 COMPUTE STATISTICS;
