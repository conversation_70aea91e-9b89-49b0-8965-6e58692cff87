#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试重复规则分析API

使用方法：
python test_duplicate_api.py [hospital_id]

如果不提供hospital_id，将使用默认值1
"""

import sys
import requests
import json

def test_duplicate_analysis_api(hospital_id=1):
    """测试重复规则分析API"""
    url = f"http://localhost:5000/api/hospital-rules/duplicate-analysis/{hospital_id}"
    
    print(f"测试URL: {url}")
    print("发送请求...")
    
    try:
        response = requests.get(url, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            if data.get('success'):
                print("\n✓ API调用成功")
                print(f"总规则数: {data.get('total_rules', 0)}")
                print(f"重复规则数: {data.get('duplicate_rules', 0)}")
                print(f"重复组数: {data.get('duplicate_groups_count', 0)}")
            else:
                print(f"\n✗ API返回错误: {data.get('error')}")
                if data.get('details'):
                    print(f"详细信息: {data.get('details')}")
        else:
            print(f"HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    except Exception as e:
        print(f"其他异常: {e}")

def test_basic_connection():
    """测试基本连接"""
    url = "http://localhost:5000/api/hospitals"
    
    print("测试基本连接...")
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print("✓ 服务器连接正常")
            return True
        else:
            print(f"✗ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 无法连接到服务器: {e}")
        return False

def main():
    hospital_id = 1
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("重复规则分析API测试")
    print("=" * 50)
    
    # 测试基本连接
    if not test_basic_connection():
        print("请确保后端服务正在运行")
        sys.exit(1)
    
    print()
    
    # 测试重复规则分析API
    test_duplicate_analysis_api(hospital_id)

if __name__ == "__main__":
    main()
