"""
调试INTERSECT解析问题
"""

import sqlglot
from sqlglot import expressions as exp

sql = """
SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = '血液灌流'
INTERSECT
SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = '血液透析'
"""

# 解析SQL
ast = sqlglot.parse_one(sql)
print("AST结构:")
print(ast)
print(f"AST类型: {type(ast)}")
print(f"是否为Intersect: {isinstance(ast, exp.Intersect)}")

if isinstance(ast, exp.Intersect):
    print(f"左侧查询: {ast.left}")
    print(f"右侧查询: {ast.right}")
    
    # 检查左侧查询的WHERE条件
    if isinstance(ast.left, exp.Select):
        where_left = ast.left.find(exp.Where)
        if where_left:
            print(f"左侧WHERE: {where_left.this}")
    
    # 检查右侧查询的WHERE条件
    if isinstance(ast.right, exp.Select):
        where_right = ast.right.find(exp.Where)
        if where_right:
            print(f"右侧WHERE: {where_right.this}")

# 查找所有INTERSECT节点
intersects = list(ast.find_all(exp.Intersect))
print(f"找到的INTERSECT节点数量: {len(intersects)}")

for i, intersect in enumerate(intersects):
    print(f"INTERSECT {i+1}: {intersect}")
