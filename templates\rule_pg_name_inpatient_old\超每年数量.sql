  WITH TAB1 AS ( SELECT 
    b.patient_id,
    b.医保项目名称,
    to_char(b.项目使用日期,'yyyy') as 使用年份,
    SUM(数量) as 年度使用总量
FROM 医保住院结算明细 B
WHERE 医保项目名称 in ({医保名称1})
GROUP BY 
    b.patient_id,
    b.医保项目名称,
    to_char(b.项目使用日期,'yyyy')
HAVING SUM(数量) > {违规数量} 
)
SELECT
  A.病案号,
  A.结算单据号,
  A.医疗机构编码,
  A.医疗机构名称,
  A.结算日期,
  A.住院号,
  A.个人编码,
  A.患者社会保障号码,
  A.身份证号,
  A.险种类型,
  A.入院科室,
  A.出院科室,
  A.主诊医师姓名,
  A.患者姓名,
  A.患者年龄,
  A.异地标志,
  A.入院日期,
  A.出院日期,
 (a.出院日期 :: DATE) - (a.入院日期 :: DATE) as 住院天数,
  A.医疗总费用,
  A.基本统筹支付,
  A<PERSON>个人自付,
  <PERSON><PERSON>个人自费,
  A.符合基本医疗保险的费用,
  A.入院诊断编码,
  A.入院诊断名称,
  A.出院诊断编码,
  A.出院诊断名称,
  A.主手术及操作编码,
  A.主手术及操作名称,
  A.其他手术及操作编码,
  A.其他手术及操作名称,
  B.开单科室名称,
  B.执行科室名称,
  B.开单医师姓名,
  B.费用类别,
  B.结算日期,
  to_char(b.项目使用日期,'yyyy') as 使用年份,
  B.医院项目编码,
  B.医院项目名称,
  B.医保项目编码,
  B.医保项目名称,
  B.规格,
  B.单价,
  B.支付类别,
  B.报销比例,
  B.自付比例,
  --A.记账流水号,
  SUM(b.数量) AS 使用数量,
  SUM(b.金额) AS 使用金额,
  SUM(b.医保范围内金额) AS 医保范围内总金额
FROM
  医保住院结算明细 b
  join tab1 on b.patient_id = tab1.patient_id and tab1.医保项目名称 = b.医保项目名称 and tab1.使用年份 = to_char(b.项目使用日期,'yyyy')  
 JOIN 医保住院结算主单 a ON a.结算单据号 = b.结算单据号
 WHERE
  b.医保项目名称 in ({医保名称1})
  and not A.出院诊断名称 ~* '({排除诊断})'
  and not B.开单科室名称 ~* '({排除科室})'
 group by 
 A.病案号,
  A.结算单据号,
  A.医疗机构编码,
  A.医疗机构名称,
  A.结算日期,
  A.住院号,
  A.个人编码,
  A.患者社会保障号码,
  A.身份证号,
  A.险种类型,
  A.入院科室,
  A.出院科室,
  A.主诊医师姓名,
  A.患者姓名,
  A.患者年龄,
  A.异地标志,
  A.入院日期,
  A.出院日期,
  A.医疗总费用,
  A.基本统筹支付,
  A.个人自付,
  A.个人自费,
  A.符合基本医疗保险的费用,
  A.入院诊断编码,
  A.入院诊断名称,
  A.出院诊断编码,
  A.出院诊断名称,
  A.主手术及操作编码,
  A.主手术及操作名称,
  A.其他手术及操作编码,
  A.其他手术及操作名称,
  B.开单科室名称,
  B.执行科室名称,
  B.开单医师姓名,
  B.费用类别,
  B.结算日期,
  to_char(b.项目使用日期,'yyyy'),
  B.医院项目编码,
  B.医院项目名称,
  B.医保项目编码,
  B.医保项目名称,
  B.规格,
  B.单价,
  B.支付类别,
  B.报销比例,
  B.自付比例
  --A.记账流水号,
ORDER BY
  A.患者姓名,
  a.结算单据号
