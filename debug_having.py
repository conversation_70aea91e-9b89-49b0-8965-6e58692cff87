"""
调试HAVING子句结构
"""

import sqlglot
from sqlglot import expressions as exp

sql = """
SELECT 结算单据号, SUM(数量) as 总数量
FROM 医保住院结算明细
WHERE 医保项目名称 = '血液透析'
GROUP BY 结算单据号
HAVING SUM(数量) > 2
"""

# 解析SQL
ast = sqlglot.parse_one(sql)
print("AST结构:")
print(ast)
print(f"AST类型: {type(ast)}")

# 查找GROUP BY节点
group_node = ast.find(exp.Group)
if group_node:
    print(f"\nGROUP节点: {group_node}")
    print(f"GROUP节点类型: {type(group_node)}")
    print(f"GROUP节点属性: {dir(group_node)}")
    
    # 检查是否有having属性
    print(f"有having属性: {hasattr(group_node, 'having')}")
    if hasattr(group_node, 'having'):
        print(f"having内容: {group_node.having}")

# 查找HAVING节点
having_nodes = list(ast.find_all(exp.Having))
print(f"\n找到的HAVING节点数量: {len(having_nodes)}")

for i, having in enumerate(having_nodes):
    print(f"HAVING {i+1}: {having}")
    print(f"  类型: {type(having)}")
    print(f"  this: {having.this}")

# 查看完整的SELECT结构
print(f"\nSELECT节点详细结构:")
def print_node_structure(node, indent=0):
    prefix = "  " * indent
    print(f"{prefix}{type(node).__name__}: {str(node)[:100]}...")
    
    # 检查常见属性
    for attr in ['expressions', 'this', 'having', 'group', 'where']:
        if hasattr(node, attr):
            attr_value = getattr(node, attr)
            if attr_value is not None:
                print(f"{prefix}  {attr}: {type(attr_value)} = {str(attr_value)[:50]}...")
                if isinstance(attr_value, list) and attr_value:
                    for j, item in enumerate(attr_value[:2]):  # 只显示前2个
                        print(f"{prefix}    [{j}]: {type(item)} = {str(item)[:50]}...")

print_node_structure(ast)
