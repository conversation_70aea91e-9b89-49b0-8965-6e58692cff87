# Gemini API多密钥轮询系统

## 概述

本系统实现了Gemini API的多密钥轮询功能，支持自动切换API密钥、限流保护、错误处理和密钥管理。当某个密钥达到速率限制时，系统会自动切换到下一个可用密钥，确保服务的连续性。

## 功能特性

### 🔑 多密钥支持
- 支持配置多个Gemini API密钥
- 自动轮询使用不同密钥
- 向后兼容单密钥配置

### 🛡️ 限流保护
- 自动检测限流错误（429、503状态码）
- 被限流的密钥自动进入冷却期
- 智能重试机制

### 📊 状态监控
- 实时监控每个密钥的使用状态
- 记录使用次数和错误统计
- 提供详细的状态查询API

### ⚙️ 密钥管理
- 支持手动启用/禁用密钥
- 一键重置所有密钥状态
- 灵活的密钥配置方式

## 配置方法

### 方法1：环境变量（推荐）

#### 多密钥配置（逗号分隔）
```bash
export GEMINI_API_KEYS="key1,key2,key3"
```

#### 多密钥配置（JSON数组）
```bash
export GEMINI_API_KEYS='["key1","key2","key3"]'
```

#### 单密钥配置（向后兼容）
```bash
export GEMINI_API_KEY="your_single_key"
```

### 方法2：直接修改配置文件

编辑 `gemini_config.py` 文件：

```python
# 直接设置密钥列表
GEMINI_API_KEYS = [
    "AIzaSyABSOhbPgGLHl78bTXb55Keyxhh1rCDCUw",
    "AIzaSyBXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    "AIzaSyCYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY"
]
```

### 配置参数说明

```python
# 密钥冷却时间（秒）- 被限流后的等待时间
GEMINI_KEY_COOLDOWN_TIME = 300  # 5分钟

# 每个密钥的最大重试次数
GEMINI_MAX_RETRIES_PER_KEY = 3

# 被认为是限流的HTTP状态码
GEMINI_RATE_LIMIT_CODES = [429, 503]
```

## API接口

### 获取密钥状态
```http
GET /api/gemini/keys/status
```

响应示例：
```json
{
  "success": true,
  "data": {
    "total_keys": 3,
    "available_keys": 2,
    "unavailable_keys": 1,
    "total_usage": 150,
    "total_errors": 5,
    "current_index": 1,
    "key_details": [
      {
        "index": 0,
        "key_preview": "AIzaSyABSO...CUw",
        "is_available": true,
        "usage_count": 50,
        "error_count": 1,
        "consecutive_errors": 0,
        "last_used": "2024-01-15T10:30:00",
        "cooldown_until": null,
        "last_error": null
      }
    ]
  }
}
```

### 重置所有密钥状态
```http
POST /api/gemini/keys/reset
```

### 禁用指定密钥
```http
POST /api/gemini/keys/{key_index}/disable
Content-Type: application/json

{
  "reason": "手动禁用原因"
}
```

### 启用指定密钥
```http
POST /api/gemini/keys/{key_index}/enable
```

## 使用示例

### Python代码示例

```python
import requests

# 调用智能分析API（自动使用多密钥轮询）
response = requests.post('http://localhost:5001/api/rules/intelligent-get-medical-names', 
    json={
        'rule_content': '规则内容',
        'rule_name': '规则名称',
        'behavior_type': '行为类型',
        'city': '城市'
    }
)

# 检查密钥状态
status_response = requests.get('http://localhost:5001/api/gemini/keys/status')
print(status_response.json())
```

### 环境变量设置示例

#### Windows
```cmd
set GEMINI_API_KEYS=key1,key2,key3
```

#### Linux/Mac
```bash
export GEMINI_API_KEYS="key1,key2,key3"
```

#### Docker
```dockerfile
ENV GEMINI_API_KEYS="key1,key2,key3"
```

## 工作原理

### 密钥轮询流程

1. **初始化**：系统启动时解析配置，创建密钥管理器
2. **请求处理**：每次API调用时获取下一个可用密钥
3. **错误处理**：
   - 限流错误（429/503）：密钥进入冷却期，尝试下一个
   - 其他错误：记录错误，尝试下一个密钥
   - 连续错误：临时禁用密钥
4. **成功处理**：重置错误计数，更新使用统计

### 密钥状态管理

每个密钥维护以下状态：
- `is_available`: 是否可用
- `last_used`: 最后使用时间
- `cooldown_until`: 冷却结束时间
- `usage_count`: 使用次数
- `error_count`: 错误次数
- `consecutive_errors`: 连续错误次数

### 冷却机制

- **限流冷却**：遇到429/503错误时，密钥进入5分钟冷却期
- **错误冷却**：连续错误达到阈值时，密钥进入1分钟冷却期
- **自动恢复**：冷却期结束后自动恢复可用状态

## 监控和调试

### 日志信息

系统会记录详细的日志信息：

```
INFO - 密钥管理器初始化完成，共 3 个密钥
DEBUG - 选择密钥: AIzaSyABSO... (使用次数: 15)
WARNING - 密钥 AIzaSyABSO... 被限流，冷却至 2024-01-15 15:35:00
ERROR - 密钥 AIzaSyABSO... 错误: API请求失败: 429
```

### 状态监控

通过状态API可以实时监控：
- 每个密钥的使用情况
- 错误统计和类型
- 冷却状态和恢复时间
- 整体系统健康度

## 故障排除

### 常见问题

1. **所有密钥都不可用**
   - 检查密钥是否正确配置
   - 确认密钥是否有效
   - 查看是否所有密钥都被限流

2. **频繁切换密钥**
   - 检查网络连接
   - 确认API配额是否充足
   - 调整冷却时间配置

3. **配置不生效**
   - 重启应用程序
   - 检查环境变量设置
   - 验证配置文件语法

### 调试命令

```bash
# 运行测试脚本
python test_gemini_multi_keys.py

# 检查配置
python -c "from gemini_config import GEMINI_API_KEYS; print(f'配置了 {len(GEMINI_API_KEYS)} 个密钥')"

# 查看日志
tail -f app.log | grep -i gemini
```

## 最佳实践

1. **密钥数量**：建议配置3-5个密钥以确保冗余
2. **监控**：定期检查密钥状态和使用统计
3. **配额管理**：合理分配每个密钥的使用配额
4. **安全性**：使用环境变量而非硬编码密钥
5. **备份**：保留备用密钥以应对紧急情况

## 更新日志

- **v1.0.0**: 初始版本，支持基本的多密钥轮询
- **v1.1.0**: 添加限流保护和冷却机制
- **v1.2.0**: 增加密钥管理API和状态监控
- **v1.3.0**: 优化错误处理和重试逻辑
