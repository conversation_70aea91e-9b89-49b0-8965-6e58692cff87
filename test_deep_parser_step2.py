"""
深度SQL解析器第二步测试
测试WITH子句和INTERSECT模式解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sql_deep_parser import DeepSQLParser, RuleType


def test_simple_intersect():
    """测试简单的INTERSECT模式"""
    print("=== 测试简单INTERSECT模式 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = '血液灌流'
    INTERSECT
    SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = '血液透析'
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        
        if rule_ir.duplicate_pattern:
            print("重复收费模式:")
            print(f"  主要项目: {rule_ir.duplicate_pattern.primary_items}")
            print(f"  冲突项目: {rule_ir.duplicate_pattern.conflict_items}")
            print(f"  检测方法: {rule_ir.duplicate_pattern.detection_method}")
            print(f"  时间精度: {rule_ir.duplicate_pattern.time_precision}")
        else:
            print("未检测到重复收费模式")
    else:
        print(f"解析失败: {result.error_message}")
    
    return result.success and result.rule_ir.rule_type == RuleType.DUPLICATE_BILLING


def test_with_intersect():
    """测试WITH子句中的INTERSECT模式"""
    print("\n=== 测试WITH子句INTERSECT模式 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    WITH tab1 AS (
        SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
        FROM 医保住院结算明细 B
        WHERE B.医保项目名称 IN ('血液灌流')
        INTERSECT
        SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
        FROM 医保住院结算明细 B
        WHERE B.医保项目名称 IN ('血液透析')
    )
    SELECT * FROM 医保住院结算明细 B
    JOIN tab1 C ON B.结算单据号 = C.结算单据号
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        print(f"数据源数量: {len(rule_ir.data_sources)}")
        
        if rule_ir.duplicate_pattern:
            print("重复收费模式:")
            print(f"  主要项目: {rule_ir.duplicate_pattern.primary_items}")
            print(f"  冲突项目: {rule_ir.duplicate_pattern.conflict_items}")
            print(f"  检测方法: {rule_ir.duplicate_pattern.detection_method}")
            print(f"  时间精度: {rule_ir.duplicate_pattern.time_precision}")
        else:
            print("未检测到重复收费模式")
        
        # 打印JSON格式
        print(f"\nJSON格式:\n{rule_ir.to_json()}")
    else:
        print(f"解析失败: {result.error_message}")
    
    return result.success and result.rule_ir.rule_type == RuleType.DUPLICATE_BILLING


def test_complex_with_multiple_cte():
    """测试复杂的多CTE查询"""
    print("\n=== 测试复杂多CTE查询 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    WITH patient_filter AS (
        SELECT 结算单据号 FROM 医保住院结算主单 
        WHERE 患者年龄 > 50 AND 患者性别 = '男'
    ),
    duplicate_items AS (
        SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = '项目A'
        INTERSECT
        SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = '项目B'
    )
    SELECT * FROM 医保住院结算明细 B
    JOIN patient_filter P ON B.结算单据号 = P.结算单据号
    JOIN duplicate_items D ON B.结算单据号 = D.结算单据号
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        print(f"数据源数量: {len(rule_ir.data_sources)}")
        print(f"条件数量: {len(rule_ir.conditions)}")
        
        if rule_ir.duplicate_pattern:
            print("重复收费模式:")
            print(f"  主要项目: {rule_ir.duplicate_pattern.primary_items}")
            print(f"  冲突项目: {rule_ir.duplicate_pattern.conflict_items}")
        
        print("条件详情:")
        for i, cond in enumerate(rule_ir.conditions):
            print(f"  {i+1}. {cond.field.field_name} {cond.operator.value} {cond.value}")
    else:
        print(f"解析失败: {result.error_message}")
    
    return result.success


def test_time_precision_detection():
    """测试时间精度检测"""
    print("\n=== 测试时间精度检测 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    # 测试小时精度
    sql_hour = """
    WITH tab1 AS (
        SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
        FROM 医保住院结算明细 WHERE 医保项目名称 = 'A'
        INTERSECT
        SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
        FROM 医保住院结算明细 WHERE 医保项目名称 = 'B'
    )
    SELECT * FROM tab1
    """
    
    result = parser.parse(sql_hour)
    
    print(f"小时精度解析成功: {result.success}")
    if result.success and result.rule_ir.duplicate_pattern:
        print(f"检测到时间精度: {result.rule_ir.duplicate_pattern.time_precision}")
        expected_precision = "hour"
        actual_precision = result.rule_ir.duplicate_pattern.time_precision
        print(f"精度检测正确: {actual_precision == expected_precision}")
        return actual_precision == expected_precision
    
    return False


def main():
    """主测试函数"""
    print("开始深度SQL解析器第二步测试...\n")
    
    tests = [
        ("简单INTERSECT模式", test_simple_intersect),
        ("WITH子句INTERSECT模式", test_with_intersect),
        ("复杂多CTE查询", test_complex_with_multiple_cte),
        ("时间精度检测", test_time_precision_detection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 第二步测试全部通过！可以继续下一步开发。")
    else:
        print("⚠️  存在失败的测试，需要修复后再继续。")


if __name__ == "__main__":
    main()
