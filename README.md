# 数据处理工具箱

一个基于 Flask 的 Web 应用，提供多种数据处理工具，主要用于 SQL 查询生成和 Excel 文件处理。

## 功能特点

### 1. SQL 生成器
- 支持多种数据库类型的 SQL 查询语句生成
- 基于模板系统，可自定义和复用 SQL 模板
- 支持变量替换和参数配置

### 2. 数据库查询生成 Excel
- 直接执行数据库查询并导出为 Excel 格式
- 支持多种导出模板
- 支持 Oracle 数据库连接

### 3. 批量 SQL 查询生成 Excel
- 支持批量执行多个 SQL 查询
- 结果自动导出为 Excel 文件
- 支持并行处理提高效率

### 4. Excel 文件拆分
- 支持大型 Excel 文件的智能拆分
- 可根据指定列进行文件拆分
- 支持自定义拆分规则

### 5. Excel 内容删除
- 支持根据条件批量删除 Excel 文件内容
- 支持单文件和多文件批处理
- 保留原始文件格式和中文文件名

### 6. 系统规则管理
- 内置通用规则配置
- 支持规则的添加和管理
- 可扩展的规则系统

## 技术栈

- **后端框架**: Flask
- **数据库支持**: Oracle (通过 oracledb)
- **数据处理**: Pandas
- **前端框架**: Bootstrap 5
- **图标**: Font Awesome 5
- **文件处理**: 
  - Werkzeug
  - zipfile
  - pandas

## 项目结构

## 使用指南

### SQL 生成器使用
1. 选择或上传 SQL 模板
2. 填写必要参数
3. 选择生成方式
4. 下载生成的 SQL 文件

### SQL 性能测试
1. 选择数据库类型
2. 上传待测试的 SQL 文件
3. 执行测试
4. 查看测试报告

[更多使用说明...]

## 开发指南

### 代码规范
- 遵循 PEP 8 规范
- 使用类型注解
- 编写单元测试
- 保持代码注释完整

### 版本控制
- 使用 Git 进行版本管理
- 遵循分支管理规范
- 提交信息要清晰明确

### 错误处理
- 使用统一的错误处理机制
- 记录详细的错误日志
- 实现错误重试机制

## 注意事项
1. 数据库操作
   - 大查询使用分页
   - 注意事务处理
   - 定期清理临时文件

2. 文件处理
   - 注意内存使用
   - 大文件分批处理
   - 保留操作日志

3. 系统维护
   - 定期备份数据
   - 监控系统性能
   - 及时更新依赖

## 许可证
MIT License

### 详细安装步骤

1. **安装 Python 环境**
   ```bash
   # Windows
   下载并安装 Python 3.8+ (https://www.python.org/downloads/)
   # 确保将 Python 添加到 PATH

   # Linux
   sudo apt-get update
   sudo apt-get install python3.8 python3-pip python3-venv
   ```

2. **安装数据库客户端**
   ```bash
   # PostgreSQL
   # Windows: 下载并安装 PostgreSQL (https://www.postgresql.org/download/windows/)
   # Linux:
   sudo apt-get install postgresql-client

   # Oracle
   # 下载并安装 Oracle Instant Client
   # 设置环境变量 ORACLE_HOME 和 PATH
   ```

3. **克隆项目**
   ```bash
   git clone [repository-url]
   cd [project-directory]
   ```

4. **创建并激活虚拟环境**
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate

   # Linux/Mac
   python3 -m venv venv
   source venv/bin/activate
   ```

5. **安装项目依赖**
   ```bash
   pip install -r requirements.txt
   ```

6. **配置环境变量**
   创建 `.env` 文件：
   ```env
   # PostgreSQL 配置
   PG_HOST=localhost
   PG_PORT=5432
   PG_DATABASE=your_database
   PG_USER=your_username
   PG_PASSWORD=your_password
   PG_SCHEMA=your_schema

   # Oracle 配置
   ORACLE_HOST=localhost
   ORACLE_PORT=1521
   ORACLE_SERVICE=your_service
   ORACLE_USER=your_username
   ORACLE_PASSWORD=your_password

   # 应用配置
   FLASK_ENV=development
   FLASK_DEBUG=1
   SECRET_KEY=your-secret-key
   UPLOAD_FOLDER=uploads
   MAX_CONTENT_LENGTH=16777216
   ```

7. **初始化项目目录**
   ```bash
   # 创建必要的目录
   mkdir -p uploads/temp
   mkdir -p output
   mkdir -p logs
   mkdir -p templates
   ```

8. **设置文件权限（Linux/Mac）**
   ```bash
   chmod 755 app.py
   chmod -R 755 uploads
   chmod -R 755 output
   chmod -R 755 logs
   ```

### 启动应用

1. **开发环境启动**
   ```bash
   # Windows
   python app.py

   # Linux/Mac
   python3 app.py
   ```

2. **生产环境启动**
   ```bash
   # 使用 gunicorn (Linux)
   gunicorn -w 4 -b 0.0.0.0:5000 app:app

   # 使用 waitress (Windows)
   waitress-serve --port=5000 app:app
   ```

### 验证安装

1. 访问 http://localhost:5000
2. 测试数据库连接
3. 验证文件上传功能
4. 检查日志文件

### 常见问题解决

1. **数据库连接错误**
   - 检查数据库服务是否运行
   - 验证连接参数是否正确
   - 确认防火墙设置

2. **文件权限问题**
   - 检查目录权限设置
   - 确保应用有写入权限
   - 验证用户权限

3. **Oracle 客户端问题**
   - 确认 ORACLE_HOME 设置
   - 检查 tnsnames.ora 配置
   - 验证客户端版本兼容性

[其余内容保持不变...]
