"""
测试病案提取规则解析
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_case_extraction_rule():
    """测试病案提取规则"""
    print("=== 测试病案提取规则 ===")
    
    sql_content = """
    -- 规则名称: 特定医保项目病案提取
    -- 城市: 测试城市
    -- 行为认定: 病案提取
    SELECT
      A.`病案号` AS `病案号`,
      A.`结算单据号` AS `结算单据号`,
      A.`医疗机构编码` AS `医疗机构编码`,
      A.`医疗机构名称` AS `医疗机构名称`,
      A.`结算日期` AS `结算日期`,
      A.`住院号` AS `住院号`,
      A.`个人编码` AS `个人编码`,
      A.`患者社会保障号码` AS `患者社会保障号码`,
      A.`身份证号` AS `身份证号`,
      A.`险种类型` AS `险种类型`,
      A.`入院科室` AS `入院科室`,
      A.`出院科室` AS `出院科室`,
      A.`主诊医师姓名` AS `主诊医师姓名`,
      A.`患者姓名` AS `患者姓名`,
      A.`患者性别` AS `患者性别`,
      A.`患者出生日期` AS `患者出生日期`,
      A.`患者年龄` AS `患者年龄`,
      A.`异地标志` AS `异地标志`,
      A.`入院日期` AS `入院日期`,
      A.`出院日期` AS `出院日期`,
      toDate(A.`出院日期`) - toDate(A.`入院日期`) + 1 AS `住院天数`,
      A.`医疗总费用` AS `医疗总费用`,
      A.`基本统筹支付` AS `基本统筹支付`,
      A.`个人自付` AS `个人自付`,
      A.`个人自费` AS `个人自费`,
      A.`符合基本医疗保险的费用` AS `符合基本医疗保险的费用`,
      A.`入院诊断编码` AS `入院诊断编码`,
      A.`入院诊断名称` AS `入院诊断名称`,
      A.`出院诊断编码` AS `出院诊断编码`,
      A.`出院诊断名称` AS `出院诊断名称`,
      A.`主手术及操作编码` AS `主手术及操作编码`,
      A.`主手术及操作名称` AS `主手术及操作名称`,
      A.`其他手术及操作编码` AS `其他手术及操作编码`,
      A.`其他手术及操作名称` AS `其他手术及操作名称`,
      B.`开单科室名称` AS `开单科室名称`,
      B.`执行科室名称` AS `执行科室名称`,
      B.`开单医师姓名` AS `开单医师姓名`,
      B.`费用类别` AS `费用类别`,
      B.`项目使用日期` AS `项目使用日期`,
      B.`医院项目编码` AS `医院项目编码`,
      B.`医院项目名称` AS `医院项目名称`,
      B.`医保项目编码` AS `医保项目编码`,
      B.`医保项目名称` AS `医保项目名称`,
      B.`支付类别` AS `支付类别`,
      B.`报销比例` AS `报销比例`,
      B.`自付比例` AS `自付比例`,
      B.`支付地点类别` AS `支付地点类别`,
      B.`记账流水号` AS `记账流水号`,
      B.`规格` AS `规格`,
      B.`单价` AS `单价`,
      B.`数量` AS `数量`,
      B.`金额` AS `金额`,
      B.`医保范围内金额` AS `医保范围内金额`
    FROM
      ZZS_YB_ZDYFY_9LY.`医保门诊结算明细`  B
    JOIN
      ZZS_YB_ZDYFY_9LY.`医保门诊结算主单`  A
    ON
      A.`结算单据号` = B.`结算单据号`
    WHERE
     B.`医保项目编码`  in (
     'XL03AAJ213B002010104021',
    'XL03AAJ213B002020104089',
    'XL03AAJ213B002010304021',
    'XL03AAJ213B002010204021',
    'XL03AAJ213B002010104089'
    )
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"HTTP状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ 病案提取规则解析成功")
            
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"\n📋 基本信息:")
            print(f"  规则名称: {rule_info.get('rule_name', 'N/A')}")
            print(f"  规则类型: {rule_info.get('rule_type', 'N/A')}")
            print(f"  城市: {rule_info.get('city', 'N/A')}")
            print(f"  行为认定: {rule_info.get('behavior', 'N/A')}")
            
            print(f"\n🔍 深度分析:")
            print(f"  数据源数量: {deep_analysis.get('data_sources_count', 'N/A')}")
            print(f"  条件数量: {deep_analysis.get('conditions_count', 'N/A')}")
            print(f"  聚合函数数量: {deep_analysis.get('aggregations_count', 'N/A')}")
            
            print(f"\n🏥 医保项目:")
            medical_items = rule_info.get('medical_items', [])
            if medical_items:
                for item in medical_items:
                    print(f"  - {item}")
            else:
                print("  未检测到医保项目")
            
            # 解析JSON输出查看详细信息
            json_output = deep_analysis.get('json_output', '{}')
            try:
                json_obj = json.loads(json_output)
                
                print(f"\n📊 数据源详情:")
                data_sources = json_obj.get('data_sources', [])
                for ds in data_sources:
                    print(f"  - {ds.get('table_name', 'N/A')} ({ds.get('alias', 'N/A')})")
                
                print(f"\n🔍 条件详情:")
                conditions = json_obj.get('conditions', [])
                for i, cond in enumerate(conditions, 1):
                    field = cond.get('field', {})
                    print(f"  {i}. {field.get('field_name', 'N/A')} ({field.get('field_type', 'N/A')}) {cond.get('operator', 'N/A')} {cond.get('value', 'N/A')}")
                
                # 检查是否检测到医保项目编码
                print(f"\n🔍 医保项目编码检测:")
                found_codes = []
                for cond in conditions:
                    field = cond.get('field', {})
                    if '医保项目编码' in field.get('field_name', ''):
                        value = cond.get('value', '')
                        if isinstance(value, list):
                            found_codes.extend(value)
                        else:
                            found_codes.append(value)
                
                if found_codes:
                    print(f"  检测到 {len(found_codes)} 个医保项目编码:")
                    for code in found_codes:
                        print(f"    - {code}")
                else:
                    print("  ❌ 未检测到医保项目编码")
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
            
            return True
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        try:
            error_info = response.json()
            print(f"错误信息: {error_info}")
        except:
            print(f"响应内容: {response.text}")
        return False


if __name__ == "__main__":
    print("开始测试病案提取规则解析...\n")
    
    if test_case_extraction_rule():
        print("\n✅ 测试完成")
    else:
        print("\n❌ 测试失败")
