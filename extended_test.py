#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扩展功能测试脚本 - 深度测试3个页面的具体功能
"""

import urllib.request
import urllib.parse
import json
import time
from datetime import datetime
import sys

class ExtendedTester:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.test_results = []
        
    def log_test(self, test_name, status, message="", data=None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "status": status,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {message}")
    
    def test_rule_knowledge_base_features(self):
        """测试飞检规则知识库页面的具体功能"""
        print("\n=== 飞检规则知识库功能测试 ===")
        
        # 1. 测试规则数据加载
        try:
            with urllib.request.urlopen(f"{self.base_url}/api/rules", timeout=15) as response:
                if response.getcode() == 200:
                    content = response.read().decode('utf-8')
                    rules = json.loads(content)
                    if isinstance(rules, list) and len(rules) > 0:
                        self.log_test("规则知识库-数据加载", "PASS", f"成功加载{len(rules)}条规则")
                        
                        # 检查规则数据结构
                        first_rule = rules[0]
                        required_fields = ['id', '规则名称', '城市', '规则来源']
                        missing_fields = [field for field in required_fields if field not in first_rule]
                        if not missing_fields:
                            self.log_test("规则知识库-数据结构", "PASS", "规则数据结构完整")
                        else:
                            self.log_test("规则知识库-数据结构", "WARNING", f"缺少字段: {missing_fields}")
                    else:
                        self.log_test("规则知识库-数据加载", "FAIL", "规则数据为空")
        except Exception as e:
            self.log_test("规则知识库-数据加载", "FAIL", f"加载失败: {str(e)}")
        
        # 2. 测试筛选选项加载
        filter_apis = [
            ("城市类型", "/api/city_types"),
            ("规则来源", "/api/rule_sources"),
            ("行为认定", "/api/behavior_types"),
            ("规则类型", "/api/type_types")
        ]
        
        for filter_name, api_path in filter_apis:
            try:
                with urllib.request.urlopen(f"{self.base_url}{api_path}", timeout=10) as response:
                    if response.getcode() == 200:
                        content = response.read().decode('utf-8')
                        data = json.loads(content)
                        if data.get('success') and data.get('types'):
                            count = len(data['types'])
                            self.log_test(f"规则知识库-{filter_name}筛选", "PASS", f"加载{count}个选项")
                        else:
                            self.log_test(f"规则知识库-{filter_name}筛选", "WARNING", "数据格式异常")
            except Exception as e:
                self.log_test(f"规则知识库-{filter_name}筛选", "FAIL", f"加载失败: {str(e)}")
    
    def test_sql_generator_features(self):
        """测试规则SQL生成器页面的具体功能"""
        print("\n=== 规则SQL生成器功能测试 ===")
        
        # 1. 测试规则搜索功能
        search_scenarios = [
            {"visit_type": "住院", "expected_min": 100},
            {"visit_type": "门诊", "expected_min": 50},
            {"city": "北京", "visit_type": "住院", "expected_min": 10}
        ]
        
        for i, scenario in enumerate(search_scenarios):
            try:
                url = f"{self.base_url}/api/rules/search?" + urllib.parse.urlencode(scenario)
                with urllib.request.urlopen(url, timeout=15) as response:
                    if response.getcode() == 200:
                        content = response.read().decode('utf-8')
                        data = json.loads(content)
                        if data.get('success') and data.get('rules'):
                            rule_count = len(data['rules'])
                            expected_min = scenario.get('expected_min', 0)
                            if rule_count >= expected_min:
                                self.log_test(f"SQL生成器-搜索场景{i+1}", "PASS", 
                                            f"搜索成功，返回{rule_count}条规则")
                            else:
                                self.log_test(f"SQL生成器-搜索场景{i+1}", "WARNING", 
                                            f"返回{rule_count}条规则，少于预期{expected_min}条")
                        else:
                            self.log_test(f"SQL生成器-搜索场景{i+1}", "FAIL", "搜索响应格式异常")
            except Exception as e:
                self.log_test(f"SQL生成器-搜索场景{i+1}", "FAIL", f"搜索失败: {str(e)}")
        
        # 2. 测试SQL生成功能（模拟）
        try:
            # 首先获取一些规则ID
            url = f"{self.base_url}/api/rules/search?" + urllib.parse.urlencode({"visit_type": "住院"})
            with urllib.request.urlopen(url, timeout=10) as response:
                content = response.read().decode('utf-8')
                data = json.loads(content)
                if data.get('success') and data.get('rules'):
                    # 取前3个规则进行测试
                    test_rules = data['rules'][:3]
                    rule_ids = [rule['id'] for rule in test_rules]
                    self.log_test("SQL生成器-规则选择", "PASS", f"选择{len(rule_ids)}个规则进行SQL生成测试")
                else:
                    self.log_test("SQL生成器-规则选择", "FAIL", "无法获取测试规则")
        except Exception as e:
            self.log_test("SQL生成器-规则选择", "FAIL", f"获取规则失败: {str(e)}")
    
    def test_system_rules_features(self):
        """测试系统规则语句页面的具体功能"""
        print("\n=== 系统规则语句功能测试 ===")
        
        # 1. 测试SQL历史记录加载
        try:
            with urllib.request.urlopen(f"{self.base_url}/api/sql_history", timeout=15) as response:
                if response.getcode() == 200:
                    content = response.read().decode('utf-8')
                    history_data = json.loads(content)
                    if isinstance(history_data, list):
                        self.log_test("系统规则-历史记录加载", "PASS", f"加载{len(history_data)}条SQL历史记录")
                        
                        # 检查历史记录数据结构
                        if len(history_data) > 0:
                            first_record = history_data[0]
                            required_fields = ['id', 'rule_name', 'city', 'create_time']
                            existing_fields = [field for field in required_fields if field in first_record]
                            self.log_test("系统规则-数据结构", "PASS", 
                                        f"历史记录包含字段: {existing_fields}")
                    else:
                        self.log_test("系统规则-历史记录加载", "WARNING", "历史记录数据格式异常")
        except Exception as e:
            self.log_test("系统规则-历史记录加载", "FAIL", f"加载失败: {str(e)}")
        
        # 2. 测试筛选选项
        try:
            with urllib.request.urlopen(f"{self.base_url}/api/filter_options", timeout=10) as response:
                if response.getcode() == 200:
                    content = response.read().decode('utf-8')
                    filter_data = json.loads(content)
                    # 检查筛选选项的数据结构
                    if isinstance(filter_data, dict):
                        available_filters = list(filter_data.keys())
                        self.log_test("系统规则-筛选选项", "PASS", f"可用筛选项: {available_filters}")
                    else:
                        self.log_test("系统规则-筛选选项", "WARNING", "筛选选项数据格式异常")
        except Exception as e:
            self.log_test("系统规则-筛选选项", "FAIL", f"加载失败: {str(e)}")
    
    def test_data_consistency(self):
        """测试数据一致性"""
        print("\n=== 数据一致性测试 ===")
        
        # 1. 测试规则总数一致性
        try:
            # 从规则知识库API获取规则总数
            with urllib.request.urlopen(f"{self.base_url}/api/rules", timeout=15) as response:
                content = response.read().decode('utf-8')
                all_rules = json.loads(content)
                total_rules = len(all_rules) if isinstance(all_rules, list) else 0
            
            # 从搜索API获取规则总数（无筛选条件）
            search_url = f"{self.base_url}/api/rules/search?" + urllib.parse.urlencode({"visit_type": ""})
            with urllib.request.urlopen(search_url, timeout=15) as response:
                content = response.read().decode('utf-8')
                search_data = json.loads(content)
                search_rules = len(search_data.get('rules', [])) if search_data.get('success') else 0
            
            if abs(total_rules - search_rules) <= 100:  # 允许小幅差异
                self.log_test("数据一致性-规则总数", "PASS", 
                            f"规则总数基本一致 (全量:{total_rules}, 搜索:{search_rules})")
            else:
                self.log_test("数据一致性-规则总数", "WARNING", 
                            f"规则总数差异较大 (全量:{total_rules}, 搜索:{search_rules})")
        except Exception as e:
            self.log_test("数据一致性-规则总数", "FAIL", f"检查失败: {str(e)}")
    
    def run_extended_tests(self):
        """运行扩展测试"""
        print("=" * 60)
        print("开始扩展功能测试")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        self.test_rule_knowledge_base_features()
        self.test_sql_generator_features()
        self.test_system_rules_features()
        self.test_data_consistency()
        
        return True
    
    def generate_extended_report(self):
        """生成扩展测试报告"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        warning_tests = len([r for r in self.test_results if r['status'] == 'WARNING'])
        
        report = f"""
{'=' * 70}
扩展功能测试报告
{'=' * 70}
测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试目标: 飞检规则知识库系统 - 深度功能测试

测试统计:
- 总测试数: {total_tests}
- 通过: {passed_tests} ✅
- 失败: {failed_tests} ❌  
- 警告: {warning_tests} ⚠️
- 成功率: {(passed_tests/total_tests*100):.1f}%

详细结果:
"""
        
        for result in self.test_results:
            status_icon = "✅" if result['status'] == "PASS" else "❌" if result['status'] == "FAIL" else "⚠️"
            report += f"{status_icon} {result['test_name']}: {result['message']}\n"
        
        report += f"\n{'=' * 70}\n"
        
        return report

if __name__ == "__main__":
    tester = ExtendedTester()
    
    if tester.run_extended_tests():
        report = tester.generate_extended_report()
        print(report)
        
        # 保存报告到文件
        with open("extended_test_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        print("扩展测试报告已保存到 extended_test_report.txt")
    else:
        print("扩展测试执行失败")
        sys.exit(1)
