"""
测试诊断关键字提取功能
"""

import logging
from sql_deep_parser.parser import DeepSQLParser

# 设置日志级别
logging.basicConfig(level=logging.INFO)

def test_diagnosis_keywords():
    parser = DeepSQLParser(dialect="postgres")
    
    # 测试SQL - 包含STR_POSITION函数的诊断条件
    sql = """
    SELECT * FROM 医保住院结算明细 B
    JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
    WHERE (STR_POSITION(a.出院诊断名称, '癌') > 0 OR STR_POSITION(a.出院诊断名称, '瘤') > 0 
           OR STR_POSITION(a.出院诊断名称, '占位') > 0 OR STR_POSITION(a.出院诊断名称, '继发') > 0) 
       OR (STR_POSITION(a.入院诊断名称, '癌') > 0 OR STR_POSITION(a.入院诊断名称, '瘤') > 0 
           OR STR_POSITION(a.入院诊断名称, '占位') > 0 OR STR_POSITION(a.入院诊断名称, '继发') > 0)
    """
    
    print("测试SQL:")
    print(sql)
    print("\n" + "="*50)
    
    # 解析SQL
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        print(f"提取到的诊断关键字: {rule_ir.diagnosis_keywords}")
        print(f"诊断关键字数量: {len(rule_ir.diagnosis_keywords)}")
        
        # 验证是否提取到了预期的关键字
        expected_keywords = ['癌', '瘤', '占位', '继发']
        print(f"预期关键字: {expected_keywords}")
        
        # 检查是否所有预期关键字都被提取到
        missing_keywords = [kw for kw in expected_keywords if kw not in rule_ir.diagnosis_keywords]
        if missing_keywords:
            print(f"缺失的关键字: {missing_keywords}")
        else:
            print("✓ 所有预期关键字都已提取")
            
        # 合并关键字用'|'分隔
        if rule_ir.diagnosis_keywords:
            merged_keywords = '|'.join(rule_ir.diagnosis_keywords)
            print(f"合并后的诊断关键字: {merged_keywords}")
        
        print(f"\n条件数量: {len(rule_ir.conditions)}")
        for i, condition in enumerate(rule_ir.conditions):
            print(f"条件 {i+1}: {condition}")
            
    else:
        print(f"解析失败: {result.error_message}")
        if result.warnings:
            print("警告:")
            for warning in result.warnings:
                print(f"  - {warning}")

if __name__ == "__main__":
    test_diagnosis_keywords()
