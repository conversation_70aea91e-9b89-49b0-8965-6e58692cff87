#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试密钥冷却逻辑
"""

import time
from datetime import datetime, timedelta
from gemini_key_manager import <PERSON><PERSON>eyMana<PERSON>

def test_cooldown_logic():
    """测试冷却逻辑"""
    print('=' * 60)
    print('密钥冷却逻辑测试')
    print('=' * 60)
    
    # 创建测试密钥管理器
    test_keys = [
        "AIzaSyTestKey1234567890123456789012345678901",
        "AIzaSyTestKey2234567890123456789012345678902"
    ]
    
    # 使用1分钟冷却时间
    manager = GeminiKeyManager(test_keys, cooldown_time=60, max_retries=3)
    
    print(f"初始化密钥管理器，冷却时间: {manager.cooldown_time}秒")
    print(f"最大重试次数: {manager.max_retries}")
    
    # 显示初始状态
    print('\n=== 初始状态 ===')
    status = manager.get_status_summary()
    print(f"总密钥数: {status['total_keys']}")
    print(f"可用密钥数: {status['available_keys']}")
    
    # 测试限流错误
    print('\n=== 测试限流错误冷却 ===')
    test_key = test_keys[0]
    print(f"对密钥 {test_key[:15]}... 标记限流错误")
    
    manager.mark_key_error(test_key, "API rate limit exceeded", is_rate_limit=True)
    
    # 检查状态
    status = manager.get_status_summary()
    key_detail = next(k for k in status['key_details'] if k['key_preview'].startswith(test_key[:10]))
    
    print(f"密钥状态: {'可用' if key_detail['is_available'] else '不可用'}")
    if key_detail['cooldown_until']:
        cooldown_time = datetime.fromisoformat(key_detail['cooldown_until'])
        remaining = (cooldown_time - datetime.now()).total_seconds()
        print(f"冷却至: {cooldown_time.strftime('%H:%M:%S')}")
        print(f"剩余冷却时间: {remaining:.1f}秒")
    
    # 测试获取密钥（应该返回第二个密钥）
    print('\n=== 测试密钥轮询 ===')
    available_key = manager.get_next_key()
    if available_key:
        print(f"获取到可用密钥: {available_key[:15]}...")
        if available_key == test_keys[1]:
            print("✅ 正确跳过被冷却的密钥")
        else:
            print("❌ 密钥选择逻辑错误")
    else:
        print("❌ 没有获取到可用密钥")
    
    # 测试连续错误冷却
    print('\n=== 测试连续错误冷却 ===')
    test_key2 = test_keys[1]
    print(f"对密钥 {test_key2[:15]}... 标记连续错误")
    
    # 标记多次错误以触发连续错误冷却
    for i in range(manager.max_retries):
        manager.mark_key_error(test_key2, f"Connection error {i+1}", is_rate_limit=False)
        print(f"第 {i+1} 次错误")
    
    # 检查状态
    status = manager.get_status_summary()
    key_detail2 = next(k for k in status['key_details'] if k['key_preview'].startswith(test_key2[:10]))
    
    print(f"密钥状态: {'可用' if key_detail2['is_available'] else '不可用'}")
    print(f"连续错误次数: {key_detail2['consecutive_errors']}")
    if key_detail2['cooldown_until']:
        cooldown_time = datetime.fromisoformat(key_detail2['cooldown_until'])
        remaining = (cooldown_time - datetime.now()).total_seconds()
        print(f"冷却至: {cooldown_time.strftime('%H:%M:%S')}")
        print(f"剩余冷却时间: {remaining:.1f}秒")
    
    # 现在两个密钥都被冷却
    print('\n=== 测试所有密钥被冷却的情况 ===')
    available_key = manager.get_next_key()
    if available_key:
        print(f"❌ 不应该有可用密钥，但获取到: {available_key[:15]}...")
    else:
        print("✅ 正确返回None，所有密钥都被冷却")
    
    # 测试冷却恢复
    print('\n=== 测试冷却恢复 ===')
    print("等待冷却时间...")
    
    # 等待一段时间（这里只等待几秒用于演示）
    wait_time = 5
    print(f"等待 {wait_time} 秒...")
    time.sleep(wait_time)
    
    # 手动设置冷却时间已过期（用于测试）
    print("手动设置冷却时间已过期（测试用）")
    for status_obj in manager.key_statuses.values():
        if status_obj.cooldown_until:
            status_obj.cooldown_until = datetime.now() - timedelta(seconds=1)
    
    # 检查恢复后的状态
    print('\n=== 检查恢复后状态 ===')
    status = manager.get_status_summary()
    print(f"可用密钥数: {status['available_keys']}")
    
    available_key = manager.get_next_key()
    if available_key:
        print(f"✅ 冷却恢复，获取到可用密钥: {available_key[:15]}...")
    else:
        print("❌ 冷却恢复失败")
    
    # 测试成功使用后的状态重置
    print('\n=== 测试成功使用后状态重置 ===')
    if available_key:
        manager.mark_key_success(available_key)
        
        # 检查连续错误是否被重置
        status = manager.get_status_summary()
        key_detail = next(k for k in status['key_details'] if k['key_preview'].startswith(available_key[:10]))
        
        print(f"连续错误次数: {key_detail['consecutive_errors']}")
        if key_detail['consecutive_errors'] == 0:
            print("✅ 成功使用后连续错误次数已重置")
        else:
            print("❌ 连续错误次数未重置")
    
    # 最终状态
    print('\n=== 最终状态 ===')
    status = manager.get_status_summary()
    print(f"总密钥数: {status['total_keys']}")
    print(f"可用密钥数: {status['available_keys']}")
    print(f"总使用次数: {status['total_usage']}")
    print(f"总错误次数: {status['total_errors']}")
    
    print('\n' + '=' * 60)
    print('冷却逻辑测试完成')
    print('=' * 60)
    
    # 总结
    print('\n📋 冷却逻辑总结:')
    print('1. 限流错误 (429/503): 60秒冷却')
    print('2. 连续错误 (>=3次): 60秒冷却')
    print('3. 冷却期间密钥不可用')
    print('4. 冷却结束后自动恢复')
    print('5. 成功使用后重置连续错误计数')

def test_current_config():
    """测试当前配置"""
    print('\n=== 当前配置检查 ===')
    
    try:
        from gemini_config import GEMINI_KEY_COOLDOWN_TIME, GEMINI_MAX_RETRIES_PER_KEY, GEMINI_RATE_LIMIT_CODES
        
        print(f"限流冷却时间: {GEMINI_KEY_COOLDOWN_TIME}秒 ({GEMINI_KEY_COOLDOWN_TIME/60:.1f}分钟)")
        print(f"最大重试次数: {GEMINI_MAX_RETRIES_PER_KEY}")
        print(f"限流状态码: {GEMINI_RATE_LIMIT_CODES}")
        
        if GEMINI_KEY_COOLDOWN_TIME == 60:
            print("✅ 限流冷却时间已设置为1分钟")
        else:
            print(f"⚠️ 限流冷却时间为 {GEMINI_KEY_COOLDOWN_TIME} 秒")
            
    except ImportError as e:
        print(f"❌ 配置导入失败: {e}")

if __name__ == "__main__":
    test_current_config()
    test_cooldown_logic()
