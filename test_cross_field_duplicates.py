#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交叉字段重复检测功能测试脚本

测试新增的交叉字段重复检测功能：
1. 在医保名称1重复组中检测医保名称2字段的重复项目
2. 在医保名称2重复组中检测医保名称1字段的重复项目
3. 验证前端高亮显示效果

使用方法：
python test_cross_field_duplicates.py [hospital_id]

作者: Augment Agent
日期: 2025-07-21
"""

import sys
import requests
import json
from datetime import datetime
from collections import defaultdict

def test_cross_field_detection(hospital_id=1):
    """测试交叉字段重复检测功能"""
    url = f"http://localhost:5000/api/hospital-rules/duplicate-analysis/{hospital_id}"
    
    print(f"测试交叉字段重复检测功能")
    print(f"URL: {url}")
    print("=" * 80)
    
    try:
        response = requests.get(url, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✓ API调用成功")
                print(f"总规则数: {data.get('total_rules', 0)}")
                print(f"重复规则数: {data.get('duplicate_rules', 0)}")
                print(f"重复组数: {data.get('duplicate_groups_count', 0)}")
                
                # 分析交叉字段重复检测结果
                groups = data.get('duplicate_groups', [])
                if groups:
                    analyze_cross_field_results(groups)
                else:
                    print("\n没有发现重复规则，无法测试交叉字段检测功能")
                
                return True
            else:
                print(f"✗ API返回错误: {data.get('error')}")
                return False
        else:
            print(f"✗ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return False

def analyze_cross_field_results(groups):
    """分析交叉字段重复检测结果"""
    print(f"\n交叉字段重复检测结果分析:")
    print("=" * 80)
    
    # 统计有交叉字段重复的分组
    groups_with_cross_duplicates = []
    groups_without_cross_duplicates = []
    
    for group in groups:
        cross_field_duplicates = group.get('cross_field_duplicates', [])
        if cross_field_duplicates:
            groups_with_cross_duplicates.append(group)
        else:
            groups_without_cross_duplicates.append(group)
    
    print(f"统计结果:")
    print(f"  有交叉字段重复的分组: {len(groups_with_cross_duplicates)} 个")
    print(f"  无交叉字段重复的分组: {len(groups_without_cross_duplicates)} 个")
    
    # 详细分析有交叉字段重复的分组
    if groups_with_cross_duplicates:
        print(f"\n有交叉字段重复的分组详情:")
        print("-" * 80)
        
        for i, group in enumerate(groups_with_cross_duplicates):
            print(f"\n【分组 {group.get('group_id')}】- {group.get('category')}")
            print(f"规则数量: {group.get('rule_count', 0)}")
            
            # 分组依据的重复项目
            common_names = group.get('common_medical_names', [])
            print(f"分组依据重复项目 ({len(common_names)} 个):")
            for name in common_names:
                print(f"  - {name}")
            
            # 交叉字段重复项目
            cross_field_duplicates = group.get('cross_field_duplicates', [])
            print(f"组内其他重复项目 ({len(cross_field_duplicates)} 个):")
            for name in cross_field_duplicates:
                print(f"  - {name}")
            
            # 验证交叉字段检测逻辑
            verify_cross_field_logic(group)
            
            print("-" * 40)
    
    # 示例分析
    if groups_with_cross_duplicates:
        print(f"\n示例分析:")
        example_group = groups_with_cross_duplicates[0]
        analyze_example_group(example_group)

def verify_cross_field_logic(group):
    """验证交叉字段检测逻辑"""
    print("交叉字段检测逻辑验证:")
    
    category = group.get('category', '')
    rules = group.get('rules', [])
    cross_field_duplicates = group.get('cross_field_duplicates', [])
    
    if category == '医保名称1重复':
        # 应该检测医保名称2字段的重复
        expected_field = '医保名称2'
        print(f"  ✓ 分组类别: {category}")
        print(f"  ✓ 应检测字段: {expected_field}")
        
        # 手动验证医保名称2字段的重复
        names2_items = []
        for rule in rules:
            names2 = rule.get('医保名称2', '')
            if names2:
                items = [item.strip() for item in names2.split('、') if item.strip()]
                names2_items.extend(items)
        
        # 统计重复项目
        item_counts = {}
        for item in names2_items:
            item_counts[item] = item_counts.get(item, 0) + 1
        
        manual_duplicates = [item for item, count in item_counts.items() if count > 1]
        expected_cross_duplicates = [f"医保名称2: {item}" for item in manual_duplicates]
        
        print(f"  手动检测结果: {manual_duplicates}")
        print(f"  API返回结果: {[name.replace('医保名称2: ', '') for name in cross_field_duplicates if name.startswith('医保名称2:')]}")
        
        if set(expected_cross_duplicates) == set(cross_field_duplicates):
            print("  ✓ 交叉字段检测结果正确")
        else:
            print("  ⚠️  交叉字段检测结果可能有误")
            
    elif category == '医保名称2重复':
        # 应该检测医保名称1字段的重复
        expected_field = '医保名称1'
        print(f"  ✓ 分组类别: {category}")
        print(f"  ✓ 应检测字段: {expected_field}")
        
        # 手动验证医保名称1字段的重复
        names1_items = []
        for rule in rules:
            names1 = rule.get('医保名称1', '')
            if names1:
                items = [item.strip() for item in names1.split('、') if item.strip()]
                names1_items.extend(items)
        
        # 统计重复项目
        item_counts = {}
        for item in names1_items:
            item_counts[item] = item_counts.get(item, 0) + 1
        
        manual_duplicates = [item for item, count in item_counts.items() if count > 1]
        expected_cross_duplicates = [f"医保名称1: {item}" for item in manual_duplicates]
        
        print(f"  手动检测结果: {manual_duplicates}")
        print(f"  API返回结果: {[name.replace('医保名称1: ', '') for name in cross_field_duplicates if name.startswith('医保名称1:')]}")
        
        if set(expected_cross_duplicates) == set(cross_field_duplicates):
            print("  ✓ 交叉字段检测结果正确")
        else:
            print("  ⚠️  交叉字段检测结果可能有误")

def analyze_example_group(group):
    """分析示例分组"""
    print("示例分组详细分析:")
    print(f"分组ID: {group.get('group_id')}")
    print(f"分组类别: {group.get('category')}")
    
    rules = group.get('rules', [])
    print(f"\n包含的规则:")
    for i, rule in enumerate(rules):
        print(f"  规则 {i+1}:")
        print(f"    适用ID: {rule.get('适用ID')}")
        print(f"    规则名称: {rule.get('规则名称', 'N/A')}")
        print(f"    医保名称1: {rule.get('医保名称1', 'N/A')}")
        print(f"    医保名称2: {rule.get('医保名称2', 'N/A')}")
    
    common_names = group.get('common_medical_names', [])
    cross_field_duplicates = group.get('cross_field_duplicates', [])
    
    print(f"\n重复项目分析:")
    print(f"分组依据重复项目: {common_names}")
    print(f"组内其他重复项目: {cross_field_duplicates}")
    
    print(f"\n前端高亮效果预期:")
    if group.get('category') == '医保名称1重复':
        print("  - 医保名称1字段: 分组依据项目用蓝色高亮，组内其他重复项目用浅蓝色高亮")
        print("  - 医保名称2字段: 组内其他重复项目用深色高亮")
    elif group.get('category') == '医保名称2重复':
        print("  - 医保名称1字段: 组内其他重复项目用浅蓝色高亮")
        print("  - 医保名称2字段: 分组依据项目用绿色高亮，组内其他重复项目用深色高亮")

def test_frontend_requirements():
    """测试前端功能要求"""
    print(f"\n前端功能要求验证:")
    print("=" * 80)
    
    print("高亮颜色方案:")
    print("1. ✓ 分组依据重复项目:")
    print("   - 医保名称1字段: 蓝色背景 (bg-primary)")
    print("   - 医保名称2字段: 绿色背景 (bg-success)")
    
    print("2. ✓ 组内其他重复项目:")
    print("   - 医保名称1字段: 浅蓝色背景 (bg-info)")
    print("   - 医保名称2字段: 深色背景 (bg-dark)")
    
    print("\n显示信息:")
    print("1. ✓ 分组信息区域显示两类重复项目:")
    print("   - 分组依据重复项目 (黄色标签)")
    print("   - 组内其他重复项目 (灰色标签)")
    
    print("2. ✓ 表格中的高亮效果:")
    print("   - 鼠标悬停显示项目类型提示")
    print("   - 不同类型使用不同颜色区分")
    
    print("\n用户体验:")
    print("- 用户可以清楚地看到每个分组中的所有重复项目")
    print("- 颜色编码帮助区分重复项目的来源和重要性")
    print("- 提高了重复规则审查的全面性和准确性")

def main():
    """主测试函数"""
    hospital_id = 1
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("交叉字段重复检测功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试医院ID: {hospital_id}")
    
    try:
        # 1. 测试交叉字段检测功能
        api_success = test_cross_field_detection(hospital_id)
        
        # 2. 验证前端功能要求
        test_frontend_requirements()
        
        print("\n" + "=" * 80)
        if api_success:
            print("✓ 交叉字段重复检测测试完成")
            print("\n手动验证步骤:")
            print("1. 打开医院规则管理页面")
            print("2. 选择医院，点击'已采用'")
            print("3. 点击'重复规则审查'")
            print("4. 查看分组信息中是否显示'组内其他重复项目'")
            print("5. 验证表格中的高亮效果:")
            print("   - 分组依据项目使用主要颜色高亮")
            print("   - 组内其他重复项目使用次要颜色高亮")
            print("6. 鼠标悬停验证提示信息")
        else:
            print("✗ 交叉字段重复检测测试发现问题，请检查API实现")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
