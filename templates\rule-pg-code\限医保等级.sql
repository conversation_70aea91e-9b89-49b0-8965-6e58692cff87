WITH tab1 AS (
  SELECT 
    结算单据号,
    医保项目编码,
    医保项目名称,
    患者医保等级
  FROM 
    医保住院结算主单 a
    JOIN 医保住院结算明细 b ON a.结算单据号 = b.结算单据号
  WHERE 
    b.医保项目编码 in ({医保编码1})
    AND a.患者医保等级 in ({医保等级1})
)
SELECT
  A.病案号,
  a.结算单据号,
  a.医疗机构编码,
  a.医疗机构名称,
  a.结算日期,
  a.个人编码,
  a.住院号,
  a.患者社会保障号码,
  a.身份证号,
  a.险种类型,
  a.入院科室,
  a.出院科室,
  a.主诊医师姓名,
  a.患者姓名,
  a.患者性别,
  a.患者年龄,
  a.患者医保等级,
  a.入院日期,
  a.出院日期,
  (a.出院日期 :: DATE) - (a.入院日期 :: DATE) as 住院天数,
  a.医疗总费用,
  a.基本统筹支付,
  a.个人自付,
  a.个人自费,
  a.符合基本医疗保险的费用,
  a.入院诊断编码,
  a.入院诊断名称,
  a.出院诊断编码,
  a.出院诊断名称,
  a.主手术及操作编码,
  a.主手术及操作名称,
  a.其他手术及操作编码,
  a.其他手术及操作名称,
  b.开单科室名称,
  b.执行科室名称,
  b.开单医师姓名,
  b.项目使用日期,
  b.结算日期,
  b.医院项目编码,
  b.医院项目名称,
  b.医保项目编码,
  b.医保项目名称,
  b.费用类别,
  b.支付类别,
  b.规格,
  b.单价,
  b.报销比例,
  b.自付比例,
  SUM(b.数量) AS 使用数量,
  SUM(b.金额) AS 使用金额,
  SUM(b.医保范围内金额) AS 医保范围内总金额
FROM
  医保住院结算明细 b
  JOIN tab1 t ON b.结算单据号 = t.结算单据号 AND b.医保项目编码 = t.医保项目编码
  JOIN 医保住院结算主单 a ON a.结算单据号 = b.结算单据号
WHERE
  b.医保项目编码 in ({医保编码1})
  AND a.患者医保等级 in ({医保等级1})
  AND NOT a.出院诊断名称 ~* '({排除诊断})'
  AND NOT b.开单科室名称 ~* '({排除科室})'
GROUP BY
  A.病案号,
  a.结算单据号,
  a.医疗机构编码,
  a.医疗机构名称,
  a.结算日期,
  a.个人编码,
  a.住院号,
  a.患者社会保障号码,
  a.身份证号,
  a.险种类型,
  a.入院科室,
  a.出院科室,
  a.主诊医师姓名,
  a.患者姓名,
  a.患者性别,
  a.患者年龄,
  a.患者医保等级,
  a.入院日期,
  a.出院日期,
  a.医疗总费用,
  a.基本统筹支付,
  a.个人自付,
  a.个人自费,
  a.符合基本医疗保险的费用,
  a.入院诊断编码,
  a.入院诊断名称,
  a.出院诊断编码,
  a.出院诊断名称,
  a.主手术及操作编码,
  a.主手术及操作名称,
  a.其他手术及操作编码,
  a.其他手术及操作名称,
  b.开单科室名称,
  b.执行科室名称,
  b.开单医师姓名,
  b.项目使用日期,
  b.结算日期,
  b.医院项目编码,
  b.医院项目名称,
  b.医保项目编码,
  b.医保项目名称,
  b.费用类别,
  b.支付类别,
  b.规格,
  b.单价,
  b.报销比例,
  b.自付比例
ORDER BY
  a.患者姓名,
  b.项目使用日期; 