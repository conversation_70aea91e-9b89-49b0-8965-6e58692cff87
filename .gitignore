# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Local configuration
*.ini
*.cfg
*.conf
db_config.json

# Upload and output directories
uploads/
output/
outputs/

# System files
.DS_Store
Thumbs.db

# Environment variables
.env
.env.*

# Temporary files
*.tmp
*.temp
*.bak
*~

# Test coverage
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/ 