#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试密钥添加和删除功能
"""

import requests
import json
import time

# 测试配置
base_url = 'http://localhost:5001'

def get_key_status():
    """获取密钥状态"""
    try:
        response = requests.get(f'{base_url}/api/gemini/keys/status', timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                return data['data']
    except Exception as e:
        print(f"获取状态失败: {e}")
    return None

def test_add_key():
    """测试添加密钥"""
    print('=== 测试添加密钥 ===')
    
    # 获取初始状态
    initial_status = get_key_status()
    if not initial_status:
        print("❌ 无法获取初始状态")
        return False
    
    initial_count = initial_status['total_keys']
    print(f"初始密钥数量: {initial_count}")
    
    # 测试添加有效密钥
    test_key = "AIzaSyTestKey123456789012345678901234567890"
    
    try:
        response = requests.post(
            f'{base_url}/api/gemini/keys',
            json={'key': test_key, 'description': '测试密钥'},
            timeout=10
        )
        
        print(f'添加密钥API - 状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 密钥添加成功")
                
                # 验证密钥数量增加
                new_status = get_key_status()
                if new_status and new_status['total_keys'] == initial_count + 1:
                    print(f"✅ 密钥数量正确增加到 {new_status['total_keys']}")
                    return True
                else:
                    print("❌ 密钥数量未正确增加")
            else:
                print(f"❌ 添加失败: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return False

def test_add_invalid_key():
    """测试添加无效密钥"""
    print('\n=== 测试添加无效密钥 ===')
    
    invalid_keys = [
        ("", "空密钥"),
        ("invalid", "格式错误"),
        ("AIza123", "太短"),
        ("AIza" + "x" * 100, "太长"),
        ("AIza@#$%", "包含特殊字符")
    ]
    
    success_count = 0
    
    for invalid_key, description in invalid_keys:
        print(f"\n测试 {description}: {invalid_key[:20]}...")
        
        try:
            response = requests.post(
                f'{base_url}/api/gemini/keys',
                json={'key': invalid_key},
                timeout=10
            )
            
            if response.status_code == 400:
                data = response.json()
                if not data.get('success'):
                    print(f"✅ 正确拒绝: {data.get('error')}")
                    success_count += 1
                else:
                    print("❌ 应该被拒绝但成功了")
            else:
                print(f"❌ 状态码错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    print(f"\n无效密钥测试: {success_count}/{len(invalid_keys)} 正确处理")
    return success_count == len(invalid_keys)

def test_delete_key():
    """测试删除密钥"""
    print('\n=== 测试删除密钥 ===')
    
    # 获取当前状态
    status = get_key_status()
    if not status:
        print("❌ 无法获取状态")
        return False
    
    initial_count = status['total_keys']
    print(f"当前密钥数量: {initial_count}")
    
    if initial_count <= 1:
        print("⚠️ 只有一个密钥，无法测试删除")
        return True
    
    # 删除最后一个密钥
    last_index = initial_count - 1
    
    try:
        response = requests.delete(
            f'{base_url}/api/gemini/keys/{last_index}',
            timeout=10
        )
        
        print(f'删除密钥API - 状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 密钥删除成功")
                
                # 验证密钥数量减少
                new_status = get_key_status()
                if new_status and new_status['total_keys'] == initial_count - 1:
                    print(f"✅ 密钥数量正确减少到 {new_status['total_keys']}")
                    return True
                else:
                    print("❌ 密钥数量未正确减少")
            else:
                print(f"❌ 删除失败: {data.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return False

def test_delete_last_key():
    """测试删除最后一个密钥（应该失败）"""
    print('\n=== 测试删除最后一个密钥 ===')
    
    # 先确保只有一个密钥
    status = get_key_status()
    if not status:
        print("❌ 无法获取状态")
        return False
    
    if status['total_keys'] > 1:
        print("⚠️ 有多个密钥，跳过此测试")
        return True
    
    try:
        response = requests.delete(
            f'{base_url}/api/gemini/keys/0',
            timeout=10
        )
        
        print(f'删除最后密钥API - 状态码: {response.status_code}')
        
        if response.status_code == 400:
            data = response.json()
            if not data.get('success'):
                print(f"✅ 正确拒绝删除最后一个密钥: {data.get('error')}")
                return True
            else:
                print("❌ 应该被拒绝但成功了")
        else:
            print(f"❌ 状态码错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return False

def main():
    """主测试函数"""
    print('=' * 60)
    print('密钥管理功能测试')
    print('=' * 60)
    
    # 显示初始状态
    print('初始状态:')
    initial_status = get_key_status()
    if initial_status:
        print(f"  总密钥数: {initial_status['total_keys']}")
        print(f"  可用密钥: {initial_status['available_keys']}")
    
    # 运行测试
    tests = [
        ("添加有效密钥", test_add_key),
        ("添加无效密钥", test_add_invalid_key),
        ("删除密钥", test_delete_key),
        ("删除最后密钥", test_delete_last_key)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f'\n{"="*20} {test_name} {"="*20}')
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"测试结果: {'✅ 通过' if result else '❌ 失败'}")
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 显示最终状态
    print('\n最终状态:')
    final_status = get_key_status()
    if final_status:
        print(f"  总密钥数: {final_status['total_keys']}")
        print(f"  可用密钥: {final_status['available_keys']}")
    
    # 测试总结
    print('\n' + '=' * 60)
    print('测试总结')
    print('=' * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = '✅ 通过' if result else '❌ 失败'
        print(f"{test_name}: {status}")
    
    print(f'\n总体结果: {passed}/{total} 测试通过')
    
    if passed == total:
        print('🎉 所有测试通过！密钥管理功能正常工作。')
    else:
        print('⚠️ 部分测试失败，请检查相关功能。')

if __name__ == "__main__":
    main()
