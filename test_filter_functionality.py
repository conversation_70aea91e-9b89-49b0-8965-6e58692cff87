#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复规则过滤功能测试脚本

测试新增的过滤功能：
1. 点击类别标签过滤显示
2. 全选和清除选择只作用于可见分组
3. 批量操作只统计可见分组中的选择
4. 过滤状态的视觉反馈

使用方法：
python test_filter_functionality.py [hospital_id]

作者: Augment Agent
日期: 2025-07-21
"""

import sys
import requests
import json
from datetime import datetime

def test_filter_api(hospital_id=1):
    """测试过滤功能的API数据结构"""
    url = f"http://localhost:5000/api/hospital-rules/duplicate-analysis/{hospital_id}"
    
    print(f"测试重复规则过滤功能")
    print(f"URL: {url}")
    print("=" * 80)
    
    try:
        response = requests.get(url, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('success'):
                print("✓ API调用成功")
                print(f"总规则数: {data.get('total_rules', 0)}")
                print(f"重复规则数: {data.get('duplicate_rules', 0)}")
                print(f"重复组数: {data.get('duplicate_groups_count', 0)}")
                
                # 分析过滤功能所需的数据结构
                groups = data.get('duplicate_groups', [])
                if groups:
                    analyze_filter_data(groups)
                    test_filter_scenarios(groups)
                else:
                    print("\n没有发现重复规则，无法测试过滤功能")
                
                return True
            else:
                print(f"✗ API返回错误: {data.get('error')}")
                return False
        else:
            print(f"✗ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return False

def analyze_filter_data(groups):
    """分析过滤功能所需的数据结构"""
    print(f"\n过滤功能数据结构分析:")
    print("=" * 80)
    
    # 统计类别分布
    category_counts = {}
    for group in groups:
        category = group.get('category', '未知类别')
        category_counts[category] = category_counts.get(category, 0) + 1
    
    print(f"类别分布:")
    for category, count in category_counts.items():
        print(f"  {category}: {count} 组")
    
    # 检查必要的字段
    print(f"\n数据结构验证:")
    required_fields = ['category', 'group_id', 'rules', 'rule_count', 'compare_ids']
    
    for i, group in enumerate(groups[:3]):  # 只检查前3组
        print(f"\n组 {i+1} 字段检查:")
        for field in required_fields:
            if field in group:
                print(f"  ✓ {field}: {type(group[field]).__name__}")
                if field == 'category':
                    print(f"    值: {group[field]}")
                elif field == 'rule_count':
                    print(f"    值: {group[field]}")
            else:
                print(f"  ✗ 缺少字段: {field}")

def test_filter_scenarios(groups):
    """测试各种过滤场景"""
    print(f"\n过滤场景测试:")
    print("=" * 80)
    
    # 场景1：全部显示
    print(f"场景1: 全部显示")
    print(f"  预期结果: 显示所有 {len(groups)} 个分组")
    
    # 场景2：只显示医保名称1重复
    names1_groups = [g for g in groups if g.get('category') == '医保名称1重复']
    print(f"\n场景2: 只显示医保名称1重复")
    print(f"  预期结果: 显示 {len(names1_groups)} 个分组")
    if names1_groups:
        print(f"  包含组ID: {[g.get('group_id') for g in names1_groups[:5]]}")
    
    # 场景3：只显示医保名称2重复
    names2_groups = [g for g in groups if g.get('category') == '医保名称2重复']
    print(f"\n场景3: 只显示医保名称2重复")
    print(f"  预期结果: 显示 {len(names2_groups)} 个分组")
    if names2_groups:
        print(f"  包含组ID: {[g.get('group_id') for g in names2_groups[:5]]}")
    
    # 场景4：过滤后的统计
    print(f"\n场景4: 过滤后的统计验证")
    total_rules_names1 = sum(g.get('rule_count', 0) for g in names1_groups)
    total_rules_names2 = sum(g.get('rule_count', 0) for g in names2_groups)
    print(f"  医保名称1重复规则总数: {total_rules_names1}")
    print(f"  医保名称2重复规则总数: {total_rules_names2}")
    print(f"  总计: {total_rules_names1 + total_rules_names2}")

def test_frontend_functionality():
    """测试前端功能要求"""
    print(f"\n前端功能测试要求:")
    print("=" * 80)
    
    print("过滤按钮功能:")
    print("1. ✓ 点击'全部显示'按钮 - 显示所有分组")
    print("2. ✓ 点击'医保名称1重复'按钮 - 只显示医保名称1重复分组")
    print("3. ✓ 点击'医保名称2重复'按钮 - 只显示医保名称2重复分组")
    print("4. ✓ 当前选中的过滤器高亮显示（bg-dark样式）")
    
    print("\n选择功能:")
    print("1. ✓ '全选重复规则'只选择当前可见分组中的规则")
    print("2. ✓ '清除选择'只清除当前可见分组中的选择")
    print("3. ✓ 批量操作按钮只统计可见分组中的选中数量")
    print("4. ✓ 分组复选框与规则复选框联动")
    
    print("\n视觉效果:")
    print("1. ✓ 过滤按钮悬停时有缩放和阴影效果")
    print("2. ✓ 分组显示/隐藏时有过渡动画")
    print("3. ✓ 不同类别使用不同颜色和图标")
    print("4. ✓ 当前过滤状态清晰可见")

def test_javascript_logic():
    """测试JavaScript逻辑"""
    print(f"\n JavaScript逻辑验证:")
    print("=" * 80)
    
    print("过滤函数 filterDuplicateGroups(category):")
    print("- 参数 'all': 显示所有分组")
    print("- 参数 '医保名称1重复': 只显示医保名称1重复分组")
    print("- 参数 '医保名称2重复': 只显示医保名称2重复分组")
    print("- 更新过滤按钮的高亮状态")
    print("- 调用 updateDuplicateSelection() 更新选择状态")
    
    print("\n选择函数优化:")
    print("- selectAllDuplicates(): 只选择可见分组中的复选框")
    print("- clearDuplicateSelection(): 只清除可见分组中的选择")
    print("- updateDuplicateSelection(): 只统计可见分组中的选中数量")
    
    print("\n DOM查询优化:")
    print("- 使用 data-category 属性进行分组过滤")
    print("- 查询可见分组: '.duplicate-group[style*=\"display: block\"]'")
    print("- 查询隐藏分组: '.duplicate-group[style*=\"display: none\"]'")

def main():
    """主测试函数"""
    hospital_id = 1
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("重复规则过滤功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试医院ID: {hospital_id}")
    
    try:
        # 1. 测试API数据结构
        api_success = test_filter_api(hospital_id)
        
        # 2. 测试前端功能要求
        test_frontend_functionality()
        
        # 3. 测试JavaScript逻辑
        test_javascript_logic()
        
        print("\n" + "=" * 80)
        if api_success:
            print("✓ 过滤功能测试完成")
            print("\n手动测试步骤:")
            print("1. 打开医院规则管理页面")
            print("2. 选择医院，点击'已采用'")
            print("3. 点击'重复规则审查'")
            print("4. 测试过滤按钮功能:")
            print("   - 点击'医保名称1重复'，验证只显示对应分组")
            print("   - 点击'医保名称2重复'，验证只显示对应分组")
            print("   - 点击'全部显示'，验证显示所有分组")
            print("5. 测试选择功能:")
            print("   - 在过滤状态下点击'全选重复规则'")
            print("   - 验证只选择可见分组中的规则")
            print("   - 验证批量操作按钮显示正确的数量")
        else:
            print("✗ 过滤功能测试发现问题，请检查API实现")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
