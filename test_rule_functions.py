#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则知识库功能测试脚本
测试新增规则、省市添加、批量复制等功能
"""

import requests
import json
import time

# 配置
BASE_URL = 'http://localhost:5001'
TEST_TIMEOUT = 10

def test_api_endpoint(url, method='GET', data=None, description=""):
    """测试API端点"""
    try:
        print(f"\n测试: {description}")
        print(f"URL: {method} {url}")
        
        if method == 'GET':
            response = requests.get(url, timeout=TEST_TIMEOUT)
        elif method == 'POST':
            response = requests.post(url, json=data, timeout=TEST_TIMEOUT)
        elif method == 'PUT':
            response = requests.put(url, json=data, timeout=TEST_TIMEOUT)
        else:
            print(f"不支持的HTTP方法: {method}")
            return False
            
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)[:200]}...")
                return True
            except:
                print(f"响应内容: {response.text[:200]}...")
                return True
        else:
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("规则知识库功能测试")
    print("=" * 60)
    
    # 测试结果统计
    total_tests = 0
    passed_tests = 0
    
    # 1. 测试获取下一个规则ID
    total_tests += 1
    if test_api_endpoint(f"{BASE_URL}/api/rules/next-id", description="获取下一个规则ID"):
        passed_tests += 1
    
    # 2. 测试获取城市列表
    total_tests += 1
    if test_api_endpoint(f"{BASE_URL}/api/city_types", description="获取城市列表"):
        passed_tests += 1
    
    # 3. 测试获取规则列表
    total_tests += 1
    if test_api_endpoint(f"{BASE_URL}/api/rules", description="获取规则列表"):
        passed_tests += 1
    
    # 4. 测试获取序列号
    total_tests += 1
    if test_api_endpoint(f"{BASE_URL}/api/sequence/规则医保编码对照_SEQ", description="获取序列号"):
        passed_tests += 1
    
    # 5. 测试批量复制API（模拟数据）
    total_tests += 1
    batch_copy_data = {
        "rule_ids": [3338, 3337],  # 使用实际存在的规则ID
        "target_city": "测试城市"
    }
    if test_api_endpoint(f"{BASE_URL}/api/rules/batch-copy", method='POST', 
                        data=batch_copy_data, description="批量复制规则"):
        passed_tests += 1
    
    # 6. 测试规则城市对照创建API（模拟数据）
    total_tests += 1
    city_mapping_data = {
        "规则id": 3338,
        "对照id": 9999,
        "城市": "测试城市2",
        "省份": "测试省份"
    }
    if test_api_endpoint(f"{BASE_URL}/api/rule_city_mapping", method='POST', 
                        data=city_mapping_data, description="创建规则城市对照"):
        passed_tests += 1
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n✅ 所有测试通过！")
    else:
        print(f"\n❌ 有 {total_tests - passed_tests} 个测试失败")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    main()
