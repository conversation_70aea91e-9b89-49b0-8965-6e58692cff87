# 飞检规则知识库系统发布指南 (CentOS 9 Stream)

## 1. 系统要求
- CentOS 9 Stream
- Python 3.9+
- Oracle Instant Client
- Nginx
- Supervisor

## 2. 安装系统依赖
```bash
# 更新系统
sudo dnf update -y

# 安装基础工具
sudo dnf install -y gcc python3-devel python3 nginx git
sudo dnf install -y epel-release
sudo dnf install -y supervisor
# 安装 Oracle Instant Client 依赖
sudo dnf install -y libaio

# 下载并安装 Oracle Instant Client (根据实际 CPU 架构选择)
# 对于 x86_64:
wget https://download.oracle.com/otn_software/linux/instantclient/219000/oracle-instantclient-basic-********.0-1.el8.x86_64.rpm
sudo dnf install -y ./oracle-instantclient-basic-********.0-1.el8.x86_64.rpm

# 配置 Oracle Instant Client 环境变量
echo "export LD_LIBRARY_PATH=/usr/lib/oracle/21/client64/lib:$LD_LIBRARY_PATH" >> ~/.bashrc
source ~/.bashrc
```

## 3. 创建项目目录和虚拟环境
```bash
# 创建项目目录
sudo mkdir -p /opt/micra
sudo chown -R $(whoami):$(whoami) /opt/micra

# 创建虚拟环境
cd /opt/micra
python3.9 -m venv venv
source venv/bin/activate

# 安装项目依赖
pip install -r requirements.txt
```

## 4. 配置 Supervisor
```bash
# 创建 Supervisor 配置文件
sudo tee /etc/supervisord.d/micra.ini << EOF
[program:micra]
directory=/opt/micra
command=/opt/micra/venv/bin/python app.py
user=$(whoami)
autostart=true
autorestart=true
stderr_logfile=/var/log/micra/err.log
stdout_logfile=/var/log/micra/out.log
environment=PYTHONUNBUFFERED=1

[supervisord]
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor
EOF

# 创建日志目录
sudo mkdir -p /var/log/micra
sudo chown -R $(whoami):$(whoami) /var/log/micra
```

## 5. 配置 Nginx
```bash
# 创建 Nginx 配置文件
sudo tee /etc/nginx/conf.d/micra.conf << EOF
server {
    listen 80;
    server_name _;  # 替换为实际域名

    location / {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# 测试 Nginx 配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

## 6. 项目部署脚本
创建 `deploy.sh`:
```bash
#!/bin/bash

# 停止服务
sudo supervisorctl stop micra

# 备份当前版本
timestamp=$(date +%Y%m%d_%H%M%S)
if [ -d "/opt/micra/backup" ]; then
    tar -czf "/opt/micra/backup/micra_${timestamp}.tar.gz" /opt/micra/app
fi

# 更新代码
cp -r * /opt/micra/app/

# 更新依赖
source /opt/micra/venv/bin/activate
pip install -r requirements.txt

# 重启服务
sudo supervisorctl start micra

echo "部署完成！"
```

## 7. 启动服务
```bash
# 启动 Supervisor
sudo systemctl start supervisord
sudo systemctl enable supervisord

# 启动应用
sudo supervisorctl start micra

# 检查状态
sudo supervisorctl status micra
```

## 8. 检查日志
```bash
# 查看应用日志
tail -f /var/log/micra/out.log
tail -f /var/log/micra/err.log

# 查看 Supervisor 日志
tail -f /var/log/supervisor/supervisord.log
```

## 9. 常见问题处理

### 9.1 权限问题
```bash
# 确保项目目录权限正确
sudo chown -R $(whoami):$(whoami) /opt/micra
sudo chmod -R 755 /opt/micra
```

### 9.2 Oracle 连接问题
```bash
# 检查 Oracle Instant Client 配置
echo $LD_LIBRARY_PATH
ldconfig -p | grep oracle

# 如果需要，手动设置 Oracle 环境变量
export LD_LIBRARY_PATH=/usr/lib/oracle/21/client64/lib:$LD_LIBRARY_PATH
```

### 9.3 端口占用问题
```bash
# 检查端口占用
sudo netstat -tulpn | grep 5001

# 如果需要关闭占用进程
sudo kill -9 $(sudo lsof -t -i:5001)
```

## 10. 备份策略

### 10.1 自动备份脚本
创建 `backup.sh`:
```bash
#!/bin/bash

# 设置备份目录
BACKUP_DIR="/opt/micra/backup"
MAX_BACKUPS=7

# 创建备份
timestamp=$(date +%Y%m%d_%H%M%S)
tar -czf "${BACKUP_DIR}/micra_${timestamp}.tar.gz" /opt/micra/app

# 删除旧备份
cd ${BACKUP_DIR}
ls -t | tail -n +${MAX_BACKUPS} | xargs -I {} rm {}
```

### 10.2 配置定时备份
```bash
# 编辑 crontab
crontab -e

# 添加每日备份任务（每天凌晨 2 点执行）
0 2 * * * /opt/micra/backup.sh
```

## 11. 监控配置

### 11.1 系统监控
```bash
# 安装监控工具
sudo dnf install -y htop iotop

# 监控系统资源
htop
```

### 11.2 应用监控
```bash
# 查看应用状态
sudo supervisorctl status micra

# 查看实时日志
tail -f /var/log/micra/out.log
```

## 12. 安全配置

### 12.1 配置防火墙
```bash
# 安装防火墙
sudo dnf install -y firewalld

# 启动防火墙
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 开放必要端口
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### 12.2 配置 SELinux
```bash
# 如果需要，可以设置 SELinux 为宽容模式
sudo setenforce 0

# 永久修改（如果需要）
sudo sed -i 's/SELINUX=enforcing/SELINUX=permissive/' /etc/selinux/config
```

## 13. 性能优化

### 13.1 Python 性能优化
```bash
# 启用 Python 优化
python -O app.py

# 使用 gunicorn 作为 WSGI 服务器
pip install gunicorn
gunicorn -w 4 -b 127.0.0.1:5001 app:app
```

### 13.2 Nginx 性能优化
```bash
# 编辑 Nginx 配置
sudo vim /etc/nginx/nginx.conf

# 添加以下配置
worker_processes auto;
worker_rlimit_nofile 65535;
events {
    worker_connections 65535;
    use epoll;
    multi_accept on;
}
```

## 14. 故障恢复

### 14.1 创建恢复脚本
```bash
#!/bin/bash

# 指定要恢复的备份文件
BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "请指定要恢复的备份文件"
    exit 1
fi

# 停止服务
sudo supervisorctl stop micra

# 恢复备份
tar -xzf $BACKUP_FILE -C /opt/micra/

# 重启服务
sudo supervisorctl start micra

echo "恢复完成！"
```

## 15. 维护指南

### 15.1 日常维护
- 定期检查日志文件
- 监控系统资源使用情况
- 定期清理临时文件
- 检查备份是否正常执行

### 15.2 更新维护
- 定期更新系统包
- 更新 Python 依赖
- 检查安全补丁

### 15.3 故障处理
1. 检查日志文件
2. 检查系统资源
3. 检查网络连接
4. 必要时回滚到上一个稳定版本 