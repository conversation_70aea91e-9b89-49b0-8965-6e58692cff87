#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的规则创建测试
"""

import requests
import json

# 测试配置
base_url = 'http://localhost:5001'

def test_minimal_rule():
    """测试最小字段集的规则创建"""
    print('=== 测试最小字段集规则创建 ===')
    
    # 最小测试数据
    test_rule_data = {
        '规则名称': '最小测试规则',
        '行为认定': '测试行为',
        '类型': '药品'
    }
    
    try:
        response = requests.post(
            f'{base_url}/api/rules',
            json=test_rule_data,
            timeout=30
        )
        
        print(f'创建规则API - 状态码: {response.status_code}')
        print(f'响应内容: {response.text}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'✅ 成功创建最小规则，ID: {data.get("id")}')
            return data.get('id')
        else:
            print(f'❌ 创建失败')
            
    except Exception as e:
        print(f'❌ 请求失败: {e}')
    
    return None

def test_with_numbers():
    """测试包含数字字段的规则创建"""
    print('\n=== 测试包含数字字段的规则创建 ===')
    
    # 包含数字字段的测试数据
    test_rule_data = {
        '规则名称': '数字字段测试规则',
        '行为认定': '测试行为',
        '类型': '药品',
        '规则类型': '0',
        '系统规则': 0
    }
    
    try:
        response = requests.post(
            f'{base_url}/api/rules',
            json=test_rule_data,
            timeout=30
        )
        
        print(f'创建规则API - 状态码: {response.status_code}')
        print(f'响应内容: {response.text}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'✅ 成功创建数字字段规则，ID: {data.get("id")}')
            return data.get('id')
        else:
            print(f'❌ 创建失败')
            
    except Exception as e:
        print(f'❌ 请求失败: {e}')
    
    return None

def test_with_all_basic_fields():
    """测试包含所有基本字段的规则创建"""
    print('\n=== 测试包含所有基本字段的规则创建 ===')
    
    # 包含所有基本字段的测试数据
    test_rule_data = {
        '序号': '001',
        '规则名称': '完整字段测试规则',
        '行为认定': '测试行为认定',
        '类型': '药品',
        '规则类型': '0',
        '系统规则': 0,
        '性别': '男',
        '年龄': '18-65',
        '备注': '测试备注',
        '涉及科室': '内科'
    }
    
    try:
        response = requests.post(
            f'{base_url}/api/rules',
            json=test_rule_data,
            timeout=30
        )
        
        print(f'创建规则API - 状态码: {response.status_code}')
        print(f'响应内容: {response.text}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'✅ 成功创建完整字段规则，ID: {data.get("id")}')
            return data.get('id')
        else:
            print(f'❌ 创建失败')
            
    except Exception as e:
        print(f'❌ 请求失败: {e}')
    
    return None

def main():
    """主测试函数"""
    print('=' * 60)
    print('简化规则创建测试')
    print('=' * 60)
    
    # 逐步测试，找出问题字段
    test_minimal_rule()
    test_with_numbers()
    test_with_all_basic_fields()
    
    print('\n' + '=' * 60)
    print('测试完成')
    print('=' * 60)

if __name__ == "__main__":
    main()
