import requests
import json

# 先获取一些实际存在的规则ID
try:
    response = requests.get("http://127.0.0.1:5001/api/rules")
    if response.status_code == 200:
        rules_data = response.json()
        print(f"规则数据类型: {type(rules_data)}")
        print(f"规则数据前3个: {rules_data[:3] if isinstance(rules_data, list) else 'Not a list'}")

        if isinstance(rules_data, list) and len(rules_data) > 0:
            # 获取第一个规则的ID
            first_rule = rules_data[0]
            rule_id = first_rule.get('ID') or first_rule.get('id')
            print(f"找到规则ID: {rule_id}")

            # 先获取规则的对照ID
            try:
                rule_detail_response = requests.get(f"http://127.0.0.1:5001/api/rules/{rule_id}")
                print(f"规则详情API状态码: {rule_detail_response.status_code}")
                if rule_detail_response.status_code == 200:
                    rule_detail = rule_detail_response.json()
                    print(f"规则详情数据结构: {type(rule_detail)}")
                    print(f"规则详情数据键: {rule_detail.keys() if isinstance(rule_detail, dict) else 'Not a dict'}")

                    if isinstance(rule_detail, dict) and '对照ID' in rule_detail:
                        compare_id = rule_detail['对照ID']
                        print(f"找到对照ID: {compare_id}")

                        # 测试API端点
                        url = "http://127.0.0.1:5001/api/generate_rule_sql"
                        data = {
                            "rule_ids": [compare_id],
                            "template_path": "rule_pg_name_outpatient",
                            "visit_type": "门诊",
                            "selected_rule_type": "name",
                            "selected_db_type": "pg"
                        }

                        try:
                            response = requests.post(url, json=data)
                            print(f"状态码: {response.status_code}")
                            print(f"响应内容: {response.text}")
                        except Exception as e:
                            print(f"请求失败: {e}")
                    else:
                        print("规则详情数据格式不正确")
                        print(f"完整响应: {rule_detail}")
                else:
                    print(f"获取规则详情失败: {rule_detail_response.status_code}")
                    print(f"错误响应: {rule_detail_response.text}")
            except Exception as e:
                print(f"获取规则详情失败: {e}")
        else:
            print("没有找到规则数据")
    else:
        print(f"获取规则列表失败: {response.status_code}")
except Exception as e:
    print(f"获取规则列表失败: {e}")

# 测试新增规则相关的API
base_url = 'http://localhost:5001'

# 1. 测试获取下一个ID
try:
    response = requests.get(f'{base_url}/api/rules/next-id', timeout=5)
    print(f'获取下一个ID - 状态码: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print(f'响应数据: {data}')
    else:
        print(f'错误响应: {response.text}')
except Exception as e:
    print(f'请求失败: {e}')

# 2. 测试获取城市列表
try:
    response = requests.get(f'{base_url}/api/city_types', timeout=5)
    print(f'获取城市列表 - 状态码: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print(f'城市数量: {len(data.get("types", []))}')
    else:
        print(f'错误响应: {response.text}')
except Exception as e:
    print(f'请求失败: {e}')
