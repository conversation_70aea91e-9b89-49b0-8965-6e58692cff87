"""
测试复杂NOT条件解析
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_not_in_condition():
    """测试NOT IN条件解析"""
    print("=== 测试NOT IN条件解析 ===")
    
    sql_content = """
    -- 规则名称: 排除特定诊断的病案提取
    -- 城市: 测试城市
    -- 行为认定: 病案提取
    SELECT A.`病案号`, A.`结算单据号`, B.`医保项目编码`, B.`医保项目名称`
    FROM ZZS_YB_ZDYFY_9LY.`医保住院结算明细` B
    JOIN ZZS_YB_ZDYFY_9LY.`医保住院结算主单` A ON A.`结算单据号` = B.`结算单据号`
    WHERE B.`医保项目编码` IN ('ZA07AAX0718010501697') 
    AND NOT A.`入院诊断编码` IN ('R40.100', 'R40.100x002', 'R40.100x003', 'R40.100x005')
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"规则类型: {rule_info.get('rule_type', 'N/A')}")
            print(f"条件数量: {deep_analysis.get('conditions_count', 'N/A')}")
            print(f"医保项目: {rule_info.get('medical_items', [])}")
            
            # 解析JSON输出查看详细信息
            json_output = deep_analysis.get('json_output', '{}')
            try:
                json_obj = json.loads(json_output)
                
                print("\n条件详情:")
                conditions = json_obj.get('conditions', [])
                for i, cond in enumerate(conditions, 1):
                    field = cond.get('field', {})
                    print(f"  {i}. {field.get('field_name', 'N/A')} ({field.get('field_type', 'N/A')}) {cond.get('operator', 'N/A')} {cond.get('value', 'N/A')}")
                
                # 验证是否提取了NOT IN条件
                not_in_found = False
                for cond in conditions:
                    if cond.get('operator') == '不包含于' and cond.get('field', {}).get('field_name') == '入院诊断编码':
                        not_in_found = True
                        print("\n✅ 成功提取NOT IN条件")
                        print(f"  字段: {cond.get('field', {}).get('field_name')}")
                        print(f"  操作符: {cond.get('operator')}")
                        print(f"  值: {cond.get('value')}")
                        break
                
                if not not_in_found:
                    print("\n❌ 未提取NOT IN条件")
                    return False
                
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
                return False
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


def test_not_like_condition():
    """测试NOT LIKE条件解析"""
    print("\n=== 测试NOT LIKE条件解析 ===")
    
    sql_content = """
    -- 规则名称: 排除特定诊断的病案提取
    -- 城市: 测试城市
    -- 行为认定: 病案提取
    SELECT A.`病案号`, A.`结算单据号`, B.`医保项目编码`, B.`医保项目名称`
    FROM ZZS_YB_ZDYFY_9LY.`医保住院结算明细` B
    JOIN ZZS_YB_ZDYFY_9LY.`医保住院结算主单` A ON A.`结算单据号` = B.`结算单据号`
    WHERE B.`医保项目编码` = 'XB02BXC117B002010101313'
    AND NOT A.`主诊断及操作名称` LIKE '%诊断性血小板减少%'
    AND NOT A.`主诊断及操作名称` LIKE '%肿瘤%'
    AND NOT A.`主诊断及操作名称` LIKE '%癌症%'
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"规则类型: {rule_info.get('rule_type', 'N/A')}")
            print(f"条件数量: {deep_analysis.get('conditions_count', 'N/A')}")
            print(f"医保项目: {rule_info.get('medical_items', [])}")
            
            # 解析JSON输出查看详细信息
            json_output = deep_analysis.get('json_output', '{}')
            try:
                json_obj = json.loads(json_output)
                
                print("\n条件详情:")
                conditions = json_obj.get('conditions', [])
                for i, cond in enumerate(conditions, 1):
                    field = cond.get('field', {})
                    print(f"  {i}. {field.get('field_name', 'N/A')} ({field.get('field_type', 'N/A')}) {cond.get('operator', 'N/A')} {cond.get('value', 'N/A')}")
                
                # 验证是否提取了NOT LIKE条件
                not_like_count = 0
                for cond in conditions:
                    if cond.get('operator') == '不匹配' and cond.get('field', {}).get('field_name') == '主诊断及操作名称':
                        not_like_count += 1
                
                if not_like_count >= 3:
                    print(f"\n✅ 成功提取{not_like_count}个NOT LIKE条件")
                    return True
                else:
                    print(f"\n❌ 只提取了{not_like_count}个NOT LIKE条件，期望至少3个")
                    return False
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
                return False
        else:
            print(f"❌ 解析失败: {result['error']}")
            return False
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        return False


if __name__ == "__main__":
    print("开始测试复杂NOT条件解析...\n")
    
    tests = [
        ("NOT IN条件解析", test_not_in_condition),
        ("NOT LIKE条件解析", test_not_like_condition)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有复杂NOT条件都能正确解析！")
        print("\n📋 验证的条件类型:")
        print("✅ NOT IN条件 - 排除特定诊断编码")
        print("✅ NOT LIKE条件 - 排除特定诊断名称")
    else:
        print("⚠️  仍有问题需要解决。")
