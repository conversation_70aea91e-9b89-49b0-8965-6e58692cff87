{"name": "promise-all-reject-late", "version": "1.0.1", "description": "Like Promise.all, but save rejections until all promises are resolved", "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "devDependencies": {"tap": "^14.10.5"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}