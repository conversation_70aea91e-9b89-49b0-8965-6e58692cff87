<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医保项目名称高亮功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f8f9fa;
        }
        .test-case { 
            margin: 20px 0; 
            padding: 20px; 
            border: 1px solid #dee2e6; 
            border-radius: 8px;
            background-color: white;
        }
        .success { 
            border-color: #198754;
            background-color: #d1e7dd;
        }
        .error { 
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        
        /* 医保项目名称高亮样式 */
        mark.bg-primary {
            border-radius: 3px;
            padding: 2px 4px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        mark.bg-success {
            border-radius: 3px;
            padding: 2px 4px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        mark.bg-warning {
            border-radius: 3px;
            padding: 2px 4px;
            font-weight: 500;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .demo-table {
            margin-top: 15px;
        }
        
        .demo-table td {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
        }
        
        .demo-table th {
            padding: 8px 12px;
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">医保项目名称高亮功能测试</h1>
        <div class="alert alert-info">
            <strong>测试目标：</strong>验证在重复规则表格中，共同医保项目名称的高亮显示效果。
        </div>
        
        <div id="testResults"></div>
    </div>

    <script>
        // 修改后的高亮函数
        function highlightCommonNames(text, commonNames, fieldType) {
            if (!text || !commonNames || commonNames.length === 0) {
                return text || '';
            }

            let highlightedText = text;
            
            // 根据字段类型确定高亮样式
            const getHighlightClass = (fieldType) => {
                switch (fieldType) {
                    case '医保名称1':
                        return 'bg-primary text-white';  // 蓝色背景
                    case '医保名称2':
                        return 'bg-success text-white';  // 绿色背景
                    default:
                        return 'bg-warning text-dark';   // 默认黄色背景
                }
            };
            
            commonNames.forEach(name => {
                try {
                    // 检查这个共同项目是否属于当前字段类型
                    const isCurrentFieldType = name.startsWith(`${fieldType}:`);
                    
                    if (isCurrentFieldType) {
                        // 提取实际的项目名称（去掉字段前缀）
                        const actualName = name.replace(`${fieldType}: `, '');
                        
                        // 转义正则表达式特殊字符
                        const escapedName = actualName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                        const regex = new RegExp(`(${escapedName})`, 'gi');
                        const highlightClass = getHighlightClass(fieldType);
                        
                        highlightedText = highlightedText.replace(regex, `<mark class="${highlightClass}">$1</mark>`);
                    }
                } catch (error) {
                    console.warn('高亮显示失败，项目名称:', name, '错误:', error);
                    // 如果正则表达式仍然失败，使用简单的字符串替换
                    if (name.startsWith(`${fieldType}:`)) {
                        const actualName = name.replace(`${fieldType}: `, '');
                        const index = highlightedText.toLowerCase().indexOf(actualName.toLowerCase());
                        if (index !== -1) {
                            const originalText = highlightedText.substring(index, index + actualName.length);
                            const highlightClass = getHighlightClass(fieldType);
                            highlightedText = highlightedText.replace(originalText, `<mark class="${highlightClass}">${originalText}</mark>`);
                        }
                    }
                }
            });
            
            return highlightedText;
        }

        // 测试用例
        const testCases = [
            {
                name: '医保名称1字段高亮测试',
                text: '血常规检查、尿常规检查、心电图检查',
                commonNames: ['医保名称1: 血常规检查', '医保名称2: 其他检查'],
                fieldType: '医保名称1',
                expected: '<mark class="bg-primary text-white">血常规检查</mark>、尿常规检查、心电图检查'
            },
            {
                name: '医保名称2字段高亮测试',
                text: 'CT扫描、MRI检查、X光检查',
                commonNames: ['医保名称1: 其他检查', '医保名称2: CT扫描'],
                fieldType: '医保名称2',
                expected: '<mark class="bg-success text-white">CT扫描</mark>、MRI检查、X光检查'
            },
            {
                name: '多个项目高亮测试',
                text: '血常规检查、尿常规检查、心电图检查',
                commonNames: ['医保名称1: 血常规检查', '医保名称1: 心电图检查'],
                fieldType: '医保名称1',
                expected: '<mark class="bg-primary text-white">血常规检查</mark>、尿常规检查、<mark class="bg-primary text-white">心电图检查</mark>'
            },
            {
                name: '包含特殊字符的项目',
                text: '经电子内镜食管胃十二指肠黏膜剥离术（ESD）、其他手术',
                commonNames: ['医保名称1: 经电子内镜食管胃十二指肠黏膜剥离术（ESD）'],
                fieldType: '医保名称1',
                expected: '<mark class="bg-primary text-white">经电子内镜食管胃十二指肠黏膜剥离术（ESD）</mark>、其他手术'
            },
            {
                name: '不匹配字段类型测试',
                text: '血常规检查、尿常规检查',
                commonNames: ['医保名称2: 血常规检查'],  // 共同项目是医保名称2，但当前字段是医保名称1
                fieldType: '医保名称1',
                expected: '血常规检查、尿常规检查'  // 应该不高亮
            }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('testResults');
            let allPassed = true;

            testCases.forEach((testCase, index) => {
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';

                try {
                    const result = highlightCommonNames(testCase.text, testCase.commonNames, testCase.fieldType);
                    const passed = result === testCase.expected;
                    
                    if (passed) {
                        testDiv.classList.add('success');
                    } else {
                        testDiv.classList.add('error');
                        allPassed = false;
                    }

                    testDiv.innerHTML = `
                        <h4>测试 ${index + 1}: ${testCase.name} ${passed ? '✅' : '❌'}</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>输入文本:</strong></p>
                                <div class="border p-2 bg-light">${testCase.text}</div>
                            </div>
                            <div class="col-md-6">
                                <p><strong>共同项目:</strong></p>
                                <div class="border p-2 bg-light">${testCase.commonNames.join(', ')}</div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <p><strong>字段类型:</strong></p>
                                <div class="border p-2 bg-light">${testCase.fieldType}</div>
                            </div>
                            <div class="col-md-6">
                                <p><strong>测试结果:</strong></p>
                                <div class="border p-2 ${passed ? 'bg-success' : 'bg-danger'} text-white">
                                    ${passed ? '通过' : '失败'}
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <p><strong>实际渲染效果:</strong></p>
                            <div class="border p-3 bg-white">${result}</div>
                        </div>
                        ${!passed ? `
                            <div class="mt-2">
                                <p><strong>预期结果:</strong></p>
                                <div class="border p-3 bg-light">${testCase.expected}</div>
                            </div>
                        ` : ''}
                    `;
                } catch (error) {
                    testDiv.classList.add('error');
                    testDiv.innerHTML = `
                        <h4>测试 ${index + 1}: ${testCase.name} ❌</h4>
                        <p><strong>错误:</strong> ${error.message}</p>
                    `;
                    allPassed = false;
                }

                resultsDiv.appendChild(testDiv);
            });

            // 添加演示表格
            const demoDiv = document.createElement('div');
            demoDiv.className = 'test-case';
            demoDiv.innerHTML = `
                <h4>演示：重复规则表格中的高亮效果</h4>
                <p>以下表格模拟了重复规则审查中的实际显示效果：</p>
                <table class="table demo-table">
                    <thead>
                        <tr>
                            <th>规则名称</th>
                            <th>医保项目名称1</th>
                            <th>医保项目名称2</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>规则A</td>
                            <td>${highlightCommonNames('血常规检查、尿常规检查', ['医保名称1: 血常规检查'], '医保名称1')}</td>
                            <td>${highlightCommonNames('CT扫描、MRI检查', ['医保名称2: CT扫描'], '医保名称2')}</td>
                        </tr>
                        <tr>
                            <td>规则B</td>
                            <td>${highlightCommonNames('血常规检查、心电图检查', ['医保名称1: 血常规检查'], '医保名称1')}</td>
                            <td>${highlightCommonNames('CT扫描、X光检查', ['医保名称2: CT扫描'], '医保名称2')}</td>
                        </tr>
                    </tbody>
                </table>
                <div class="mt-3">
                    <h5>颜色说明：</h5>
                    <ul>
                        <li><mark class="bg-primary text-white">蓝色高亮</mark>：医保名称1字段中的共同项目</li>
                        <li><mark class="bg-success text-white">绿色高亮</mark>：医保名称2字段中的共同项目</li>
                        <li><mark class="bg-warning text-dark">黄色高亮</mark>：默认高亮样式</li>
                    </ul>
                </div>
            `;
            resultsDiv.appendChild(demoDiv);

            // 总结
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `test-case ${allPassed ? 'success' : 'error'}`;
            summaryDiv.innerHTML = `
                <h3>测试总结 ${allPassed ? '✅' : '❌'}</h3>
                <p>共 ${testCases.length} 个测试用例，${allPassed ? '全部通过' : '存在失败'}</p>
                <div class="mt-3">
                    <h5>功能特点：</h5>
                    <ul>
                        <li>✅ 根据字段类型使用不同颜色高亮</li>
                        <li>✅ 只高亮匹配当前字段类型的共同项目</li>
                        <li>✅ 支持包含特殊字符的医保项目名称</li>
                        <li>✅ 提供优雅的视觉效果和样式</li>
                    </ul>
                </div>
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // 页面加载后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
