WITH tab1 AS (
  SELECT
    b.结算单据号,
    b.患者社会保障号码,
    b.医保项目编码,
    b.医保项目名称,
    (b.项目使用日期 :: DATE) AS 项目使用日期,
    COUNT(*) AS 使用次数
  FROM
    医保门诊结算明细 b
  WHERE
    b.医保项目编码 in ({医保编码1})
  GROUP BY
    b.结算单据号,
    b.患者社会保障号码,
    b.医保项目编码,
    b.医保项目名称,
    (b.项目使用日期 :: DATE)
  HAVING
    COUNT(*) > {合理次数}
)
SELECT
  a.医疗机构编码,
  a.医疗机构名称,
  a.结算日期,
  a.个人编码,
  a.患者社会保障号码,
  a.身份证号,
  a.患者姓名,
  a.患者性别,
  a.患者年龄,
  a.险种类型,
  b.就诊科室,
  b.诊断编码,
  b.诊断名称,
  b.医师姓名,
  b.项目使用日期,
  b.医院项目编码,
  b.医院项目名称,
  b.医保项目编码,
  b.医保项目名称,
  b.费用类别,
  b.支付类别,
  b.规格,
  b.单价,
  b.报销比例,
  b.自付比例,
  t.使用次数,
  t.使用次数 - {合理次数} AS 超出次数,
  SUM(b.数量) AS 使用数量,
  SUM(b.金额) AS 使用金额,
  (t.使用次数 - {合理次数}) * b.单价 AS 超出金额
FROM
  医保门诊结算明细 b
  JOIN tab1 t ON b.结算单据号 = t.结算单据号 
    AND b.医保项目编码 = t.医保项目编码
    AND (b.项目使用日期 :: DATE) = t.项目使用日期
  JOIN 医保门诊结算主单 a ON a.结算单据号 = b.结算单据号
WHERE
  b.医保项目编码 in ({医保编码1})
  AND NOT b.诊断名称 ~* '({排除诊断})'
  AND NOT b.就诊科室 ~* '({排除科室})'
GROUP BY
  a.医疗机构编码,
  a.医疗机构名称,
  a.结算日期,
  a.个人编码,
  a.患者社会保障号码,
  a.身份证号,
  a.患者姓名,
  a.患者性别,
  a.患者年龄,
  a.险种类型,
  b.就诊科室,
  b.诊断编码,
  b.诊断名称,
  b.医师姓名,
  b.项目使用日期,
  b.医院项目编码,
  b.医院项目名称,
  b.医保项目编码,
  b.医保项目名称,
  b.费用类别,
  b.支付类别,
  b.规格,
  b.单价,
  b.报销比例,
  b.自付比例,
  t.使用次数
ORDER BY
  a.患者姓名,
  b.项目使用日期; 