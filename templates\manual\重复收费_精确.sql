WITH tab1 AS (
  SELECT 结算单据号,TRUNC(项目使用日期) 项目使用日期 FROM 医保住院结算明细 WHERE 医保项目名称 = '{医保项目名称1}' 
  INTERSECT
  SELECT 结算单据号,TRUNC(项目使用日期) 项目使用日期 FROM 医保住院结算明细 WHERE 医保项目名称  = '{医保项目名称2}'
)
SELECT
  A.病案号,
  a.结算单据号,
  a.医疗机构编码,
  a.医疗机构名称,
  a.结算日期,
  a.个人编码,
  a.住院号,
  a.险种类型,
  a.入院科室,
  a.出院科室,
  a.主诊医师姓名,
  a.患者姓名,
  a.患者性别,
  a.患者出生日期,
  a.患者年龄,
  a.入院日期,
  a.出院日期,
  TRUNC(a.出院日期) - TRUNC(a.入院日期) as 住院天数,
  a.医疗总费用,
  a.基本统筹支付,
  a.入院诊断名称,
  a.出院诊断名称,
  b.项目使用日期,
  b.医保项目编码,
  b.医保项目名称,
  b.医院项目编码,
  b.医院项目名称,
  b.费用类别,
  b.支付类别,
  b.规格,
  b.单价,
  SUM(b.数量) AS 使用数量,
  SUM(b.金额) AS 使用金额,
  SUM(b.医保范围内金额) AS 医保范围内总金额
FROM
  医保住院结算明细 b
  join tab1 on b.结算单据号 = tab1.结算单据号 and tab1.项目使用日期 = TRUNC(b.项目使用日期)
  join 医保住院结算主单 a on a.结算单据号 = b.结算单据号
WHERE
  (b.医保项目名称 =   '{医保项目名称1}'    or 医保项目名称  =  '{医保项目名称2}' 
)
GROUP BY
  A.病案号,
  a.结算单据号,
  a.医疗机构编码,
  a.医疗机构名称,
  a.结算日期,
  a.个人编码,
  a.住院号,
  a.险种类型,
  a.入院科室,
  a.出院科室,
  a.主诊医师姓名,
  a.患者姓名,
  a.患者性别,
  a.患者出生日期,
  a.患者年龄,
  a.入院日期,
  a.出院日期,
  TRUNC(a.出院日期) - TRUNC(a.入院日期),
  a.医疗总费用,
  a.基本统筹支付,
  a.入院诊断名称,
  a.出院诊断名称,
  b.项目使用日期,
  b.医保项目编码,
  b.医保项目名称,
  b.医院项目编码,
  b.医院项目名称,
  b.费用类别,
  b.支付类别,
  b.规格,
  b.单价
ORDER BY
  a.患者姓名,
  b.项目使用日期;
