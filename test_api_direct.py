"""
直接测试API接口
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_age_restriction_api():
    """直接测试年龄限制规则API"""
    print("=== 直接测试年龄限制规则API ===")
    
    sql_content = """
    -- 规则名称: 儿童用药年龄限制
    -- 城市: 上海市
    -- 行为认定: 年龄限制
    SELECT * FROM 医保住院结算明细 B
    JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
    WHERE A.患者年龄 < 18 AND B.医保项目名称 = '成人专用药物'
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ API调用成功")
            
            rule_info = result['rule_info']
            deep_analysis = result['deep_analysis']
            
            print(f"规则类型: {rule_info['rule_type']}")
            print(f"深度分析规则类型: {deep_analysis['rule_type']}")
            print(f"数据源数量: {deep_analysis['data_sources_count']}")
            print(f"条件数量: {deep_analysis['conditions_count']}")
            
            # 打印JSON输出的前500字符
            json_output = deep_analysis['json_output']
            print(f"\nJSON输出前500字符:")
            print(json_output[:500])
            
        else:
            print(f"❌ API返回错误: {result['error']}")
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    test_age_restriction_api()
