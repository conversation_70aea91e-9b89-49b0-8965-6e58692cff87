SELECT
  A.结算单据号,
  a.医疗机构编码,
  a.医疗机构名称,
  A.住院号,
  A.病案号,
  A.个人编码,
  A.患者姓名,
  A.患者年龄,
  A.入院日期,
  A.出院日期,
  (a.出院日期 :: DATE) - (a.入院日期 :: DATE) as 住院天数,
  A.结算日期,
  A.患者社会保障号码,
  A.险种类型,
  A.入院诊断名称,
  A.出院诊断名称,
  A.入院科室,
  A.出院科室,
  A.大病保险,
  a.医疗总费用,
   a.基本统筹支付,
  B.医保项目编码,
  b.医保项目名称,
  B.医院项目编码,
  B.医院项目名称,
  b.执行科室名称,
  B.支付类别,
  b.报销比例,
  b.自付比例,
  B.规格,
  B.单价,
  SUM( B.数量 ) AS 使用数量,
  SUM( B.金额 ) AS 使用金额,
  sum( b.医保范围内金额 ) AS 医保范围内总金额
FROM
  医保住院结算主单 A
  JOIN 医保住院结算明细 B ON A.结算单据号 = B.结算单据号 
WHERE
  b.医保项目编码 in ({医保编码1})
 
GROUP BY
  A.结算单据号,
  a.医疗机构编码,
  a.医疗机构名称,
  A.住院号,
  A.病案号,
  A.个人编码,
  A.患者姓名,
  A.患者年龄,
  A.入院日期,
  A.出院日期,
  (a.出院日期 :: DATE) - (a.入院日期 :: DATE),
  A.结算日期,
  A.患者社会保障号码,
  A.险种类型,
  A.入院诊断名称,
  A.出院诊断名称,
  A.入院科室,
  A.出院科室,
  A.大病保险,
  a.医疗总费用,
   a.基本统筹支付,
  B.医保项目编码,
  b.医保项目名称,
  B.医院项目编码,
  B.医院项目名称,
  b.执行科室名称,
  B.支付类别,
  b.报销比例,
  b.自付比例,
  B.规格,
  B.单价
ORDER BY
  a.患者姓名;