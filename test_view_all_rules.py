#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试查看所有规则功能

验证在查看所有规则时统计按钮是否正确显示

使用方法：
python test_view_all_rules.py [hospital_id]

作者: Augment Agent
日期: 2025-07-24
"""

import sys
import requests
import json
from datetime import datetime

def test_view_all_rules_api(hospital_id=9):
    """测试查看所有规则API"""
    print("=" * 80)
    print("测试: 查看所有规则API")
    print("=" * 80)
    
    base_url = "http://localhost:5001"
    
    try:
        print(f"1. 调用查看所有规则API，医院ID: {hospital_id}")
        
        response = requests.get(f"{base_url}/api/hospital-rules/all/{hospital_id}", 
                               timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                rules = result.get('rules', [])
                
                print(f"✓ API调用成功，所有规则数量: {len(rules)}")
                
                # 统计不同状态的规则
                recommended_rules = [rule for rule in rules if rule.get('状态') == '推荐']
                adopted_rules = [rule for rule in rules if rule.get('状态') == '已采用']
                ignored_rules = [rule for rule in rules if rule.get('状态') == '已忽略']
                
                print(f"\n2. 规则状态统计:")
                print(f"   推荐规则: {len(recommended_rules)}")
                print(f"   已采用规则: {len(adopted_rules)}")
                print(f"   已忽略规则: {len(ignored_rules)}")
                print(f"   总规则数: {len(rules)}")
                
                # 统计重复筛查警告规则
                duplicate_warning_rules = []
                for rule in recommended_rules:
                    if rule.get('重复已采用规则') and len(rule.get('重复已采用规则', [])) > 0:
                        duplicate_warning_rules.append(rule)
                
                print(f"\n3. 重复筛查警告统计:")
                print(f"   重复筛查警告规则: {len(duplicate_warning_rules)}")
                
                if duplicate_warning_rules:
                    print(f"   重复筛查警告规则示例:")
                    for i, rule in enumerate(duplicate_warning_rules[:3], 1):
                        print(f"     {i}. 规则ID: {rule.get('规则ID')}")
                        print(f"        规则名称: {rule.get('规则名称')}")
                        print(f"        重复规则数量: {len(rule.get('重复已采用规则', []))}")
                
                print(f"\n4. 预期的统计按钮显示:")
                print(f"   重复筛查警告按钮: 黄色，数字 {len(duplicate_warning_rules)}")
                print(f"   推荐未采用按钮: 蓝色，数字 {len(recommended_rules)}")
                print(f"   显示全部按钮: 灰色，无数字")
                
                return True, {
                    'total_rules': len(rules),
                    'recommended_rules': len(recommended_rules),
                    'duplicate_warning_rules': len(duplicate_warning_rules),
                    'adopted_rules': len(adopted_rules),
                    'ignored_rules': len(ignored_rules)
                }
            else:
                print(f"❌ API返回错误: {result.get('error')}")
                return False, None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_view_all_rules_functionality():
    """测试查看所有规则功能"""
    print("\n" + "=" * 80)
    print("测试: 查看所有规则功能验证")
    print("=" * 80)
    
    print("验证查看所有规则的功能流程:")
    
    functionality_steps = [
        {
            'step': '1. 用户点击医院列表中的"所有规则"按钮',
            'function': 'viewAllRules(hospitalId)',
            'description': '切换到规则推荐标签页并设置医院选择'
        },
        {
            'step': '2. 调用loadAllRulesDirectly函数',
            'function': 'loadAllRulesDirectly(hospitalId)',
            'description': '加载该医院的所有规则数据'
        },
        {
            'step': '3. 调用API获取所有规则',
            'function': 'fetch(/api/hospital-rules/all/${hospitalId})',
            'description': '从后端获取医院的所有规则数据'
        },
        {
            'step': '4. 计算统计信息',
            'function': '过滤推荐规则和重复筛查警告规则',
            'description': '计算各种统计数字'
        },
        {
            'step': '5. 创建医院信息区域',
            'function': '创建包含统计按钮的HTML',
            'description': '显示医院名称和三个统计按钮'
        },
        {
            'step': '6. 显示规则列表',
            'function': 'displayRulesWithPagination',
            'description': '分页显示所有规则'
        }
    ]
    
    for step_info in functionality_steps:
        print(f"\n✓ {step_info['step']}:")
        print(f"  函数: {step_info['function']}")
        print(f"  功能: {step_info['description']}")
    
    print(f"\n✓ 统计按钮应该显示的位置:")
    print(f"  - 在医院信息区域（蓝色alert框）")
    print(f"  - 右侧的按钮组中")
    print(f"  - 包含三个按钮：重复筛查警告、推荐未采用、显示全部")
    
    return True

def test_statistics_buttons_in_all_rules():
    """测试所有规则页面中的统计按钮"""
    print("\n" + "=" * 80)
    print("测试: 所有规则页面统计按钮")
    print("=" * 80)
    
    print("验证统计按钮在所有规则页面的显示:")
    
    button_features = [
        {
            'button': '重复筛查警告按钮',
            'color': 'btn-warning (黄色)',
            'icon': 'bi-exclamation-triangle',
            'function': 'filterDuplicateWarningRules()',
            'logic': '过滤显示状态为推荐且有重复已采用规则的规则'
        },
        {
            'button': '推荐未采用按钮',
            'color': 'btn-info (蓝色)',
            'icon': 'bi-lightbulb',
            'function': 'filterRecommendedRules()',
            'logic': '过滤显示状态为推荐的所有规则'
        },
        {
            'button': '显示全部按钮',
            'color': 'btn-secondary (灰色)',
            'icon': 'bi-list',
            'function': 'showAllRules()',
            'logic': '取消过滤，显示所有规则'
        }
    ]
    
    for button in button_features:
        print(f"\n✓ {button['button']}:")
        print(f"  样式: {button['color']}")
        print(f"  图标: {button['icon']}")
        print(f"  函数: {button['function']}")
        print(f"  逻辑: {button['logic']}")
    
    print(f"\n✓ 按钮功能验证:")
    print(f"  - 所有按钮都应该能正常点击")
    print(f"  - 点击后应该正确过滤规则")
    print(f"  - 统计数字应该实时更新")
    print(f"  - 当前激活的按钮应该有不同的样式")
    
    return True

def main():
    """主测试函数"""
    hospital_id = 9
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("查看所有规则功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试医院ID: {hospital_id}")
    
    try:
        # 测试1: API功能
        success1, stats = test_view_all_rules_api(hospital_id)
        
        # 测试2: 功能流程
        success2 = test_view_all_rules_functionality()
        
        # 测试3: 统计按钮
        success3 = test_statistics_buttons_in_all_rules()
        
        # 输出测试结果
        print("\n" + "=" * 80)
        print("测试结果汇总")
        print("=" * 80)
        
        print(f"API功能测试: {'✓ 通过' if success1 else '❌ 失败'}")
        print(f"功能流程验证: {'✓ 通过' if success2 else '❌ 失败'}")
        print(f"统计按钮验证: {'✓ 通过' if success3 else '❌ 失败'}")
        
        if success1 and success2 and success3:
            print("\n🎉 查看所有规则功能测试通过！")
            
            if stats:
                print(f"\n📊 统计数据:")
                print(f"  总规则数: {stats['total_rules']}")
                print(f"  推荐规则数: {stats['recommended_rules']}")
                print(f"  重复筛查警告规则数: {stats['duplicate_warning_rules']}")
                print(f"  已采用规则数: {stats['adopted_rules']}")
                print(f"  已忽略规则数: {stats['ignored_rules']}")
            
            print(f"\n📋 用户验证步骤:")
            print(f"1. 打开浏览器访问 http://localhost:5001/hospital_rules")
            print(f"2. 在医院列表中找到医院ID {hospital_id}")
            print(f"3. 点击该医院行的'所有规则'按钮")
            print(f"4. 等待页面加载完成")
            print(f"5. 查看医院信息区域（蓝色框）")
            print(f"6. 验证右侧是否显示三个统计按钮")
            print(f"7. 点击各个按钮测试过滤功能")
            
            print(f"\n💡 如果看不到统计按钮:")
            print(f"  - 确认点击的是'所有规则'按钮，不是其他按钮")
            print(f"  - 等待页面完全加载完成")
            print(f"  - 检查浏览器控制台是否有JavaScript错误")
            print(f"  - 刷新页面重试")
            
            return True
        else:
            print("\n❌ 部分测试失败，请检查实现。")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
