#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORA-01461错误热修复脚本
立即解决医院规则推荐系统中的字段长度限制问题

使用方法：
1. 确保数据库连接正常
2. 运行此脚本：python hotfix_ora_01461.py
3. 脚本会自动修复数据库结构和现有数据

作者: Augment Agent
日期: 2025-07-20
紧急修复版本
"""

import sys
import os
import cx_Oracle
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def get_db_connection():
    """获取数据库连接"""
    try:
        # 这里需要根据实际情况修改数据库连接参数
        # 从app.py中获取连接信息
        from app import pool
        return pool.acquire()
    except Exception as e:
        print(f"数据库连接失败: {e}")
        print("请确保数据库服务正常运行")
        return None

def check_field_length(conn):
    """检查当前字段长度"""
    try:
        cursor = conn.cursor()
        query = """
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            DATA_LENGTH
        FROM USER_TAB_COLUMNS 
        WHERE TABLE_NAME = '医院适用规则表' 
        AND COLUMN_NAME IN ('匹配项目', '推荐原因')
        ORDER BY COLUMN_NAME
        """
        cursor.execute(query)
        results = cursor.fetchall()
        
        print("当前字段长度:")
        for row in results:
            print(f"  {row[0]}: {row[1]}({row[2]})")
        
        cursor.close()
        return results
    except Exception as e:
        print(f"检查字段长度失败: {e}")
        return []

def fix_database_structure(conn):
    """修复数据库结构"""
    try:
        cursor = conn.cursor()
        
        print("正在修复数据库结构...")
        
        # 修改匹配项目字段长度
        try:
            cursor.execute("ALTER TABLE 医院适用规则表 MODIFY (匹配项目 VARCHAR2(4000))")
            print("✓ 匹配项目字段长度已扩展到4000")
        except cx_Oracle.DatabaseError as e:
            if "ORA-01441" in str(e):
                print("✓ 匹配项目字段长度已经是4000或更大")
            else:
                print(f"✗ 修改匹配项目字段失败: {e}")
        
        # 修改推荐原因字段长度
        try:
            cursor.execute("ALTER TABLE 医院适用规则表 MODIFY (推荐原因 VARCHAR2(2000))")
            print("✓ 推荐原因字段长度已扩展到2000")
        except cx_Oracle.DatabaseError as e:
            if "ORA-01441" in str(e):
                print("✓ 推荐原因字段长度已经是2000或更大")
            else:
                print(f"✗ 修改推荐原因字段失败: {e}")
        
        conn.commit()
        cursor.close()
        return True
        
    except Exception as e:
        print(f"修复数据库结构失败: {e}")
        return False

def truncate_existing_data(conn):
    """截断现有超长数据"""
    try:
        cursor = conn.cursor()
        
        print("正在处理现有超长数据...")
        
        # 检查超长数据
        cursor.execute("""
            SELECT COUNT(*) FROM 医院适用规则表 
            WHERE LENGTH(匹配项目) > 4000 OR LENGTH(推荐原因) > 2000
        """)
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"发现 {count} 条超长数据，正在截断...")
            
            # 截断匹配项目
            cursor.execute("""
                UPDATE 医院适用规则表 
                SET 匹配项目 = SUBSTR(匹配项目, 1, 3997) || '...'
                WHERE LENGTH(匹配项目) > 4000
            """)
            
            # 截断推荐原因
            cursor.execute("""
                UPDATE 医院适用规则表 
                SET 推荐原因 = SUBSTR(推荐原因, 1, 1997) || '...'
                WHERE LENGTH(推荐原因) > 2000
            """)
            
            conn.commit()
            print("✓ 超长数据已截断")
        else:
            print("✓ 没有发现超长数据")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"处理超长数据失败: {e}")
        return False

def verify_fix(conn):
    """验证修复结果"""
    try:
        cursor = conn.cursor()
        
        print("正在验证修复结果...")
        
        # 检查字段长度
        cursor.execute("""
            SELECT 
                COLUMN_NAME,
                DATA_LENGTH
            FROM USER_TAB_COLUMNS 
            WHERE TABLE_NAME = '医院适用规则表' 
            AND COLUMN_NAME IN ('匹配项目', '推荐原因')
            ORDER BY COLUMN_NAME
        """)
        
        results = cursor.fetchall()
        print("修复后字段长度:")
        for row in results:
            print(f"  {row[0]}: {row[1]}")
        
        # 检查是否还有超长数据
        cursor.execute("""
            SELECT 
                COUNT(*) as 总记录数,
                COUNT(CASE WHEN LENGTH(匹配项目) > 4000 THEN 1 END) as 超长匹配项目,
                COUNT(CASE WHEN LENGTH(推荐原因) > 2000 THEN 1 END) as 超长推荐原因,
                MAX(LENGTH(匹配项目)) as 最大匹配项目长度,
                MAX(LENGTH(推荐原因)) as 最大推荐原因长度
            FROM 医院适用规则表
        """)
        
        result = cursor.fetchone()
        print(f"数据检查结果:")
        print(f"  总记录数: {result[0]}")
        print(f"  超长匹配项目: {result[1]}")
        print(f"  超长推荐原因: {result[2]}")
        print(f"  最大匹配项目长度: {result[3]}")
        print(f"  最大推荐原因长度: {result[4]}")
        
        cursor.close()
        
        # 验证是否修复成功
        if result[1] == 0 and result[2] == 0:
            print("✓ 修复验证通过，不再有超长数据")
            return True
        else:
            print("✗ 修复验证失败，仍有超长数据")
            return False
            
    except Exception as e:
        print(f"验证修复结果失败: {e}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("ORA-01461错误热修复脚本")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # 获取数据库连接
    conn = get_db_connection()
    if not conn:
        print("无法连接数据库，修复失败")
        return False
    
    try:
        # 1. 检查当前字段长度
        print("\n1. 检查当前字段长度")
        check_field_length(conn)
        
        # 2. 修复数据库结构
        print("\n2. 修复数据库结构")
        if not fix_database_structure(conn):
            print("数据库结构修复失败")
            return False
        
        # 3. 处理现有超长数据
        print("\n3. 处理现有超长数据")
        if not truncate_existing_data(conn):
            print("超长数据处理失败")
            return False
        
        # 4. 验证修复结果
        print("\n4. 验证修复结果")
        if not verify_fix(conn):
            print("修复验证失败")
            return False
        
        print("\n" + "="*60)
        print("✓ 热修复完成！")
        print("请重启后端服务以使代码修改生效")
        print("修复后应该不再出现ORA-01461错误")
        print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"热修复过程中发生异常: {e}")
        return False
        
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
