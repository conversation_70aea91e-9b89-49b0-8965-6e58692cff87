#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
规则表单修复功能测试脚本

测试修复的两个问题：
1. 所属领域字段保存问题
2. 批量AI智能获取功能的规则内涵丢失问题

使用方法：
python test_rule_form_fixes.py

作者: Augment Agent
日期: 2025-07-22
"""

import sys
import requests
import json
from datetime import datetime
import time

def test_domain_field_save():
    """测试所属领域字段保存功能"""
    print("=" * 80)
    print("测试1: 所属领域字段保存功能")
    print("=" * 80)
    
    base_url = "http://localhost:5000"
    
    # 测试数据
    test_rule = {
        "序号": "TEST001",
        "规则名称": "测试所属领域保存功能",
        "行为认定": "测试行为认定",
        "类型": "药品",
        "规则类型": "0",
        "所属领域": "药品",  # 重点测试字段
        "用途": "测试用途",
        "系统规则": 0,
        "备注": "测试所属领域字段保存"
    }
    
    try:
        print("1.1 创建新规则测试...")
        
        # 创建新规则
        response = requests.post(f"{base_url}/api/rules", 
                               json=test_rule, 
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                rule_id = result.get('id')
                print(f"✓ 规则创建成功，ID: {rule_id}")
                
                # 验证所属领域字段是否保存成功
                print("1.2 验证所属领域字段保存...")
                
                # 获取规则详情
                detail_response = requests.get(f"{base_url}/api/rules/{rule_id}")
                if detail_response.status_code == 200:
                    detail_result = detail_response.json()
                    if detail_result.get('success'):
                        rule_data = detail_result.get('rule', {})
                        saved_domain = rule_data.get('所属领域')
                        
                        if saved_domain == "药品":
                            print(f"✓ 所属领域字段保存成功: {saved_domain}")
                            
                            # 测试编辑功能
                            print("1.3 测试编辑规则的所属领域字段...")
                            
                            # 修改所属领域
                            update_data = test_rule.copy()
                            update_data['所属领域'] = '检验'
                            
                            update_response = requests.put(f"{base_url}/api/rules/{rule_id}",
                                                         json=update_data,
                                                         timeout=10)
                            
                            if update_response.status_code == 200:
                                update_result = update_response.json()
                                if update_result.get('success'):
                                    print("✓ 规则更新成功")
                                    
                                    # 再次验证
                                    verify_response = requests.get(f"{base_url}/api/rules/{rule_id}")
                                    if verify_response.status_code == 200:
                                        verify_result = verify_response.json()
                                        if verify_result.get('success'):
                                            updated_rule = verify_result.get('rule', {})
                                            updated_domain = updated_rule.get('所属领域')
                                            
                                            if updated_domain == '检验':
                                                print(f"✓ 所属领域字段更新成功: {updated_domain}")
                                                print("✓ 所属领域字段保存功能测试通过")
                                                return True, rule_id
                                            else:
                                                print(f"✗ 所属领域字段更新失败，期望: 检验，实际: {updated_domain}")
                                                return False, rule_id
                                        else:
                                            print(f"✗ 获取更新后规则详情失败: {verify_result.get('error')}")
                                            return False, rule_id
                                    else:
                                        print(f"✗ 获取更新后规则详情请求失败: {verify_response.status_code}")
                                        return False, rule_id
                                else:
                                    print(f"✗ 规则更新失败: {update_result.get('error')}")
                                    return False, rule_id
                            else:
                                print(f"✗ 规则更新请求失败: {update_response.status_code}")
                                return False, rule_id
                        else:
                            print(f"✗ 所属领域字段保存失败，期望: 药品，实际: {saved_domain}")
                            return False, rule_id
                    else:
                        print(f"✗ 获取规则详情失败: {detail_result.get('error')}")
                        return False, rule_id
                else:
                    print(f"✗ 获取规则详情请求失败: {detail_response.status_code}")
                    return False, rule_id
            else:
                print(f"✗ 规则创建失败: {result.get('error')}")
                return False, None
        else:
            print(f"✗ 规则创建请求失败: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"✗ 测试过程中发生异常: {e}")
        return False, None

def test_batch_ai_rule_content_preservation():
    """测试批量AI智能获取功能的规则内涵保留"""
    print("\n" + "=" * 80)
    print("测试2: 批量AI智能获取功能的规则内涵保留")
    print("=" * 80)
    
    base_url = "http://localhost:5000"
    
    # 创建测试规则
    test_rule = {
        "序号": "TEST002",
        "规则名称": "测试批量AI规则内涵保留",
        "行为认定": "超量使用",
        "类型": "药品",
        "规则类型": "0",
        "所属领域": "药品",
        "用途": "测试用途",
        "系统规则": 0,
        "备注": "测试批量AI处理时规则内涵保留"
    }
    
    try:
        print("2.1 创建测试规则...")
        
        # 创建规则
        response = requests.post(f"{base_url}/api/rules", 
                               json=test_rule, 
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                rule_id = result.get('id')
                print(f"✓ 测试规则创建成功，ID: {rule_id}")
                
                # 添加规则内涵（通过医保编码对照）
                print("2.2 添加规则内涵...")
                
                medical_data = {
                    "城市": "西安",
                    "规则内涵": "测试规则内涵内容 - 用于验证批量AI处理时不会丢失",
                    "规则来源": "测试来源",
                    "医保编码1": "TEST001",
                    "医保名称1": "测试医保项目1",
                    "医保编码2": "TEST002", 
                    "医保名称2": "测试医保项目2"
                }
                
                medical_response = requests.post(f"{base_url}/api/rule_medical_codes/{rule_id}",
                                               json=medical_data,
                                               timeout=10)
                
                if medical_response.status_code == 200:
                    medical_result = medical_response.json()
                    if medical_result.get('success'):
                        print("✓ 规则内涵添加成功")
                        
                        # 验证规则内涵存在
                        print("2.3 验证规则内涵存在...")
                        
                        verify_response = requests.get(f"{base_url}/api/rules/city/西安/{rule_id}")
                        if verify_response.status_code == 200:
                            verify_result = verify_response.json()
                            if verify_result.get('success'):
                                rule_data = verify_result.get('rule', {})
                                original_content = rule_data.get('规则内涵')
                                
                                if original_content == medical_data['规则内涵']:
                                    print(f"✓ 规则内涵验证成功: {original_content[:50]}...")
                                    
                                    # 模拟批量AI更新
                                    print("2.4 模拟批量AI更新...")
                                    
                                    ai_update_data = {
                                        "type": "超量使用",
                                        "violation_count": "5",
                                        "time_type": "天",
                                        "violation_amount": "1000",
                                        "age_limit": "18-65",
                                        "gender_limit": "不限",
                                        "exclude_diagnosis": "测试排除诊断",
                                        "exclude_departments": "测试排除科室",
                                        "include_diagnosis": "测试包含诊断",
                                        "include_departments": "测试包含科室"
                                    }
                                    
                                    ai_response = requests.put(f"{base_url}/api/rules/batch-update-ai-fields/{rule_id}",
                                                             json=ai_update_data,
                                                             timeout=10)
                                    
                                    if ai_response.status_code == 200:
                                        ai_result = ai_response.json()
                                        if ai_result.get('success'):
                                            print("✓ 批量AI更新成功")

                                            # 模拟批量保存中的医保编码对照更新
                                            print("2.5 模拟医保编码对照更新...")

                                            medical_update_data = {
                                                "城市": "西安",
                                                "医保编码1": "AI001",
                                                "医保名称1": "AI分析医保项目1",
                                                "医保编码2": "AI002",
                                                "医保名称2": "AI分析医保项目2",
                                                "规则内涵": original_content  # 保留原始规则内涵
                                            }

                                            medical_update_response = requests.post(f"{base_url}/api/rule_medical_codes/{rule_id}",
                                                                                   json=medical_update_data,
                                                                                   timeout=10)

                                            if medical_update_response.status_code == 200:
                                                medical_update_result = medical_update_response.json()
                                                if medical_update_result.get('success'):
                                                    print("✓ 医保编码对照更新成功")

                                                    # 验证规则内涵是否保留
                                                    print("2.6 验证规则内涵是否保留...")

                                                    final_response = requests.get(f"{base_url}/api/rules/city/西安/{rule_id}")
                                                    if final_response.status_code == 200:
                                                        final_result = final_response.json()
                                                        if final_result.get('success'):
                                                            final_rule = final_result.get('rule', {})
                                                            final_content = final_rule.get('规则内涵')

                                                            if final_content == original_content:
                                                                print(f"✓ 规则内涵保留成功: {final_content[:50]}...")
                                                                print("✓ 批量AI智能获取功能的规则内涵保留测试通过")
                                                                return True, rule_id
                                                            else:
                                                                print(f"✗ 规则内涵丢失")
                                                                print(f"  原始内容: {original_content}")
                                                                print(f"  更新后内容: {final_content}")
                                                                return False, rule_id
                                                        else:
                                                            print(f"✗ 获取最终规则详情失败: {final_result.get('error')}")
                                                            return False, rule_id
                                                    else:
                                                        print(f"✗ 获取最终规则详情请求失败: {final_response.status_code}")
                                                        return False, rule_id
                                                else:
                                                    print(f"✗ 医保编码对照更新失败: {medical_update_result.get('error')}")
                                                    return False, rule_id
                                            else:
                                                print(f"✗ 医保编码对照更新请求失败: {medical_update_response.status_code}")
                                                return False, rule_id
                                                else:
                                                    print(f"✗ 获取最终规则详情失败: {final_result.get('error')}")
                                                    return False, rule_id
                                            else:
                                                print(f"✗ 获取最终规则详情请求失败: {final_response.status_code}")
                                                return False, rule_id
                                        else:
                                            print(f"✗ 批量AI更新失败: {ai_result.get('error')}")
                                            return False, rule_id
                                    else:
                                        print(f"✗ 批量AI更新请求失败: {ai_response.status_code}")
                                        return False, rule_id
                                else:
                                    print(f"✗ 规则内涵验证失败，期望: {medical_data['规则内涵'][:30]}...，实际: {original_content}")
                                    return False, rule_id
                            else:
                                print(f"✗ 获取规则详情失败: {verify_result.get('error')}")
                                return False, rule_id
                        else:
                            print(f"✗ 获取规则详情请求失败: {verify_response.status_code}")
                            return False, rule_id
                    else:
                        print(f"✗ 规则内涵添加失败: {medical_result.get('error')}")
                        return False, rule_id
                else:
                    print(f"✗ 规则内涵添加请求失败: {medical_response.status_code}")
                    return False, rule_id
            else:
                print(f"✗ 测试规则创建失败: {result.get('error')}")
                return False, None
        else:
            print(f"✗ 测试规则创建请求失败: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"✗ 测试过程中发生异常: {e}")
        return False, None

def cleanup_test_data(rule_ids):
    """清理测试数据"""
    print("\n" + "=" * 80)
    print("清理测试数据")
    print("=" * 80)
    
    base_url = "http://localhost:5000"
    
    for rule_id in rule_ids:
        if rule_id:
            try:
                response = requests.delete(f"{base_url}/api/rules/{rule_id}")
                if response.status_code == 200:
                    print(f"✓ 清理测试规则 {rule_id} 成功")
                else:
                    print(f"⚠️  清理测试规则 {rule_id} 失败: {response.status_code}")
            except Exception as e:
                print(f"⚠️  清理测试规则 {rule_id} 异常: {e}")

def main():
    """主测试函数"""
    print("规则表单修复功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_rule_ids = []
    
    try:
        # 测试1: 所属领域字段保存
        success1, rule_id1 = test_domain_field_save()
        if rule_id1:
            test_rule_ids.append(rule_id1)
        
        # 测试2: 批量AI智能获取功能的规则内涵保留
        success2, rule_id2 = test_batch_ai_rule_content_preservation()
        if rule_id2:
            test_rule_ids.append(rule_id2)
        
        # 输出测试结果
        print("\n" + "=" * 80)
        print("测试结果汇总")
        print("=" * 80)
        
        print(f"测试1 - 所属领域字段保存: {'✓ 通过' if success1 else '✗ 失败'}")
        print(f"测试2 - 批量AI规则内涵保留: {'✓ 通过' if success2 else '✗ 失败'}")
        
        if success1 and success2:
            print("\n🎉 所有测试通过！规则表单修复功能正常工作。")
            return True
        else:
            print("\n❌ 部分测试失败，请检查修复实现。")
            return False
            
    except Exception as e:
        print(f"\n✗ 测试过程中发生异常: {e}")
        return False
    finally:
        # 清理测试数据
        if test_rule_ids:
            cleanup_test_data(test_rule_ids)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
