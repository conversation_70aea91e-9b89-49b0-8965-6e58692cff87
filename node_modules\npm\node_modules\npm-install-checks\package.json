{"name": "npm-install-checks", "version": "7.1.1", "description": "Check the engines and platform fields in package.json", "main": "lib/index.js", "dependencies": {"semver": "^7.1.1"}, "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.4", "tap": "^16.0.1"}, "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "repository": {"type": "git", "url": "git+https://github.com/npm/npm-install-checks.git"}, "keywords": ["npm,", "install"], "license": "BSD-2-<PERSON><PERSON>", "files": ["bin/", "lib/"], "engines": {"node": "^18.17.0 || >=20.5.0"}, "author": "GitHub Inc.", "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.4", "publish": "true"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}