{"name": "tuf-js", "version": "3.0.1", "description": "JavaScript implementation of The Update Framework (TUF)", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc --build", "clean": "rm -rf dist && rm tsconfig.tsbuildinfo", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/theupdateframework/tuf-js.git"}, "files": ["dist"], "keywords": ["tuf", "security", "update"], "author": "<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/theupdateframework/tuf-js/issues"}, "homepage": "https://github.com/theupdateframework/tuf-js/tree/main/packages/client#readme", "devDependencies": {"@tufjs/repo-mock": "3.0.1", "@types/debug": "^4.1.12", "@types/make-fetch-happen": "^10.0.4"}, "dependencies": {"@tufjs/models": "3.0.1", "debug": "^4.3.6", "make-fetch-happen": "^14.0.1"}, "engines": {"node": "^18.17.0 || >=20.5.0"}}