#!/bin/bash

# 设置错误时退出
set -e

# 设置变量
APP_NAME="micra"
APP_DIR="/opt/micra"
BACKUP_DIR="${APP_DIR}/backup"
VENV_DIR="${APP_DIR}/venv"
LOG_DIR="/var/log/micra"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 颜色输出函数
print_info() {
    echo -e "\e[34m[INFO] $1\e[0m"
}

print_success() {
    echo -e "\e[32m[SUCCESS] $1\e[0m"
}

print_error() {
    echo -e "\e[31m[ERROR] $1\e[0m"
}

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    print_error "请使用 root 用户运行此脚本"
    exit 1
fi

# 创建必要的目录
print_info "创建必要的目录..."
mkdir -p "${APP_DIR}"
mkdir -p "${BACKUP_DIR}"
mkdir -p "${LOG_DIR}"


# 停止服务
print_info "停止服务..."
supervisorctl stop ${APP_NAME} || true


# 设置权限
print_info "设置权限..."
chown -R $(whoami):$(whoami) "${APP_DIR}"
chmod -R 755 "${APP_DIR}"

# 创建或更新虚拟环境
if [ ! -d "${VENV_DIR}" ]; then
    print_info "创建虚拟环境..."
    python3.9 -m venv "${VENV_DIR}"
fi

# 更新依赖
print_info "更新依赖..."
source "${VENV_DIR}/bin/activate"
pip install --upgrade pip
pip install -r "${APP_DIR}/requirements.txt"

# 配置 Supervisor
print_info "配置 Supervisor..."
cat > /etc/supervisord.d/${APP_NAME}.ini << EOF
[program:${APP_NAME}]
directory=${APP_DIR}
command=${VENV_DIR}/bin/gunicorn -w 4 -b 127.0.0.1:5001 app:app
user=$(whoami)
autostart=true
autorestart=true
stderr_logfile=${LOG_DIR}/err.log
stdout_logfile=${LOG_DIR}/out.log
environment=PYTHONUNBUFFERED=1,PATH="${VENV_DIR}/bin:%(ENV_PATH)s"
EOF

# 重启 Supervisor
print_info "重启 Supervisor..."
supervisorctl reread
supervisorctl update
supervisorctl restart ${APP_NAME}

# 配置 Nginx
print_info "配置 Nginx..."
cat > /etc/nginx/conf.d/${APP_NAME}.conf << EOF
server {
    listen 8000;
    server_name _;

    location / {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# 配置防火墙
print_info "配置防火墙..."
if command -v firewall-cmd >/dev/null 2>&1; then
    firewall-cmd --permanent --add-port=8000/tcp
    firewall-cmd --reload
    print_success "防火墙端口 8000 已开放"
else
    print_info "未检测到 firewalld，跳过防火墙配置"
fi

# 测试 Nginx 配置
nginx -t

# 重启 Nginx
systemctl restart nginx

# 检查服务状态
print_info "检查服务状态..."
sleep 5
if supervisorctl status ${APP_NAME} | grep -q "RUNNING"; then
    print_success "部署完成！服务已成功启动"
    print_info "可以通过以下命令查看日志："
    print_info "tail -f ${LOG_DIR}/out.log"
    print_info "tail -f ${LOG_DIR}/err.log"
else
    print_error "服务启动失败，请检查日志文件"
    exit 1
fi 