#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逐字段测试规则创建
"""

import requests
import json

# 测试配置
base_url = 'http://localhost:5001'

def test_field(field_name, field_value):
    """测试单个字段"""
    print(f'\n=== 测试字段: {field_name} = {field_value} ===')
    
    # 基础数据 + 测试字段
    test_rule_data = {
        '规则名称': f'测试规则_{field_name}',
        '行为认定': '测试行为认定',
        '类型': '药品',
        '规则类型': '0',
        '系统规则': 0,
        field_name: field_value
    }
    
    try:
        response = requests.post(
            f'{base_url}/api/rules',
            json=test_rule_data,
            timeout=30
        )
        
        print(f'状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'✅ 字段 {field_name} 测试成功，ID: {data.get("id")}')
            return True
        else:
            print(f'❌ 字段 {field_name} 测试失败: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 字段 {field_name} 请求失败: {e}')
        return False

def main():
    """主测试函数"""
    print('=' * 60)
    print('逐字段测试规则创建')
    print('=' * 60)
    
    # 要测试的字段列表
    test_fields = [
        ('序号', '001'),
        ('性别', '男'),
        ('年龄', '18-65'),
        ('备注', '测试备注'),
        ('涉及科室', '内科'),
        ('违规数量', 10),
        ('违规金额', 1000),
        ('违规天数', 7),
        ('违规小时数', 24),
        ('国家医保编码1', 'GJ001'),
        ('国家医保名称1', '国家医保测试1'),
        ('国家医保编码2', 'GJ002'),
        ('国家医保名称2', '国家医保测试2'),
        ('排除诊断', '排除测试诊断'),
        ('排除科室', '排除测试科室'),
        ('包含诊断', '包含测试诊断'),
        ('包含科室', '包含测试科室'),
        ('时间类型', '天'),
        ('项目数量', 5),
        ('用途', '测试用途'),
        ('适用范围', '测试适用范围'),
        ('适用年份', '2024')
    ]
    
    success_fields = []
    failed_fields = []
    
    for field_name, field_value in test_fields:
        if test_field(field_name, field_value):
            success_fields.append(field_name)
        else:
            failed_fields.append(field_name)
    
    print('\n' + '=' * 60)
    print('测试结果汇总')
    print('=' * 60)
    print(f'成功字段 ({len(success_fields)}): {", ".join(success_fields)}')
    print(f'失败字段 ({len(failed_fields)}): {", ".join(failed_fields)}')

if __name__ == "__main__":
    main()
