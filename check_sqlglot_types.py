"""
检查sqlglot的类型层次结构
"""

import sqlglot
from sqlglot import expressions as exp

sql = "SELECT * FROM t WHERE a > 1 AND b < 2"
ast = sqlglot.parse_one(sql)
where_clause = ast.find(exp.Where)

print("WHERE表达式类型层次:")
expr = where_clause.this
print(f"类型: {type(expr)}")
print(f"MRO: {type(expr).__mro__}")
print(f"是否为Binary: {isinstance(expr, exp.Binary)}")
print(f"是否为And: {isinstance(expr, exp.And)}")

# 检查And的属性
print(f"\nAnd表达式的属性:")
print(f"hasattr left: {hasattr(expr, 'left')}")
print(f"hasattr right: {hasattr(expr, 'right')}")
print(f"hasattr this: {hasattr(expr, 'this')}")

if hasattr(expr, 'left'):
    print(f"left: {expr.left} (类型: {type(expr.left)})")
if hasattr(expr, 'right'):
    print(f"right: {expr.right} (类型: {type(expr.right)})")

# 检查GT表达式
gt_expr = expr.left
print(f"\nGT表达式:")
print(f"类型: {type(gt_expr)}")
print(f"是否为Binary: {isinstance(gt_expr, exp.Binary)}")
print(f"left: {gt_expr.left}")
print(f"right: {gt_expr.right}")
