"""
深度SQL解析器的数据模型
定义中间表示格式和相关数据结构
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import List, Optional, Union, Dict, Any
import json


class RuleType(Enum):
    """规则类型枚举"""
    DUPLICATE_BILLING = "重复收费"
    EXCESSIVE_USAGE = "超量使用"
    CASE_EXTRACTION = "病案提取"
    OVERCHARGE = "超标准收费"
    INAPPROPRIATE_USAGE = "不当使用"
    AGE_RESTRICTION = "年龄限制"
    GENDER_RESTRICTION = "性别限制"
    DIAGNOSIS_RESTRICTION = "诊断限制"
    UNKNOWN = "未知类型"


class FieldType(Enum):
    """字段类型枚举"""
    AGE = "年龄"
    GENDER = "性别"
    DIAGNOSIS = "诊断"
    DEPARTMENT = "科室"
    MEDICAL_ITEM = "医保项目"
    QUANTITY = "数量"
    AMOUNT = "金额"
    DATE = "日期"
    DATETIME = "日期时间"
    SETTLEMENT_ID = "结算单据号"
    PATIENT_ID = "患者ID"
    OTHER = "其他"


class OperatorType(Enum):
    """操作符类型枚举"""
    EQUAL = "等于"
    NOT_EQUAL = "不等于"
    GREATER_THAN = "大于"
    LESS_THAN = "小于"
    GREATER_EQUAL = "大于等于"
    LESS_EQUAL = "小于等于"
    BETWEEN = "介于"
    IN = "包含于"
    NOT_IN = "不包含于"
    LIKE = "模糊匹配"
    NOT_LIKE = "不匹配"
    IS_NULL = "为空"
    IS_NOT_NULL = "不为空"
    RANGE = "范围"
    TIME_MATCH = "时间匹配"
    EXISTS = "存在"
    NOT_EXISTS = "不存在"


class LogicType(Enum):
    """逻辑类型枚举"""
    AND = "且"
    OR = "或"
    NOT = "非"


class AggregationType(Enum):
    """聚合类型枚举"""
    SUM = "求和"
    COUNT = "计数"
    AVG = "平均值"
    MAX = "最大值"
    MIN = "最小值"
    GROUP_CONCAT = "字符串聚合"


@dataclass
class AgeRange:
    """年龄范围"""
    min_age: Optional[int] = None
    max_age: Optional[int] = None
    
    def __str__(self):
        if self.min_age is not None and self.max_age is not None:
            return f"{self.min_age}-{self.max_age}岁"
        elif self.min_age is not None:
            return f"≥{self.min_age}岁"
        elif self.max_age is not None:
            return f"≤{self.max_age}岁"
        return "无年龄限制"
    
    def to_dict(self):
        return {"min_age": self.min_age, "max_age": self.max_age}


@dataclass
class FieldReference:
    """字段引用"""
    table_alias: str
    field_name: str
    field_type: FieldType
    original_expression: Optional[str] = None
    
    def __str__(self):
        return f"{self.table_alias}.{self.field_name}"
    
    def to_dict(self):
        return {
            "table_alias": self.table_alias,
            "field_name": self.field_name,
            "field_type": self.field_type.value,
            "original_expression": self.original_expression
        }


@dataclass
class Condition:
    """条件表示"""
    field: FieldReference
    operator: OperatorType
    value: Union[str, int, float, List, AgeRange]
    logic_type: LogicType = LogicType.AND
    negated: bool = False
    
    def __str__(self):
        op_str = "NOT " if self.negated else ""
        return f"{op_str}{self.field} {self.operator.value} {self.value}"
    
    def to_dict(self):
        value_dict = self.value
        if isinstance(self.value, AgeRange):
            value_dict = self.value.to_dict()
        elif isinstance(self.value, list):
            value_dict = list(self.value)
        
        return {
            "field": self.field.to_dict(),
            "operator": self.operator.value,
            "value": value_dict,
            "logic_type": self.logic_type.value,
            "negated": self.negated
        }


@dataclass
class Aggregation:
    """聚合操作"""
    function: AggregationType
    field: FieldReference
    alias: Optional[str] = None
    having_condition: Optional[Condition] = None
    
    def to_dict(self):
        return {
            "function": self.function.value,
            "field": self.field.to_dict(),
            "alias": self.alias,
            "having_condition": self.having_condition.to_dict() if self.having_condition else None
        }


@dataclass
class DataSource:
    """数据源"""
    table_name: str
    alias: str
    join_type: Optional[str] = None
    join_conditions: List[Condition] = field(default_factory=list)
    operation: Optional[str] = None  # INTERSECT, UNION, etc.
    
    def to_dict(self):
        return {
            "table_name": self.table_name,
            "alias": self.alias,
            "join_type": self.join_type,
            "join_conditions": [cond.to_dict() for cond in self.join_conditions],
            "operation": self.operation
        }


@dataclass
class DuplicateBillingPattern:
    """重复收费模式"""
    primary_items: List[str]
    conflict_items: List[str]
    time_precision: str = "hour"  # hour, day, minute
    detection_method: str = "simultaneous_occurrence"
    
    def to_dict(self):
        return {
            "primary_items": self.primary_items,
            "conflict_items": self.conflict_items,
            "time_precision": self.time_precision,
            "detection_method": self.detection_method
        }


@dataclass
class RuleLogicIR:
    """规则逻辑中间表示"""
    rule_type: RuleType
    data_sources: List[DataSource] = field(default_factory=list)
    conditions: List[Condition] = field(default_factory=list)
    aggregations: List[Aggregation] = field(default_factory=list)
    duplicate_pattern: Optional[DuplicateBillingPattern] = None
    diagnosis_keywords: List[str] = field(default_factory=list)  # 诊断关键字
    confidence_score: float = 0.0
    original_sql: str = ""
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "rule_type": self.rule_type.value,
            "data_sources": [ds.to_dict() for ds in self.data_sources],
            "conditions": [cond.to_dict() for cond in self.conditions],
            "aggregations": [agg.to_dict() for agg in self.aggregations],
            "duplicate_pattern": self.duplicate_pattern.to_dict() if self.duplicate_pattern else None,
            "diagnosis_keywords": self.diagnosis_keywords,
            "confidence_score": self.confidence_score,
            "original_sql": self.original_sql
        }
    
    def to_json(self, indent=2):
        """转换为JSON格式"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=indent)


@dataclass
class ParseResult:
    """解析结果"""
    success: bool
    rule_ir: Optional[RuleLogicIR] = None
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    
    def to_dict(self):
        return {
            "success": self.success,
            "rule_ir": self.rule_ir.to_dict() if self.rule_ir else None,
            "error_message": self.error_message,
            "warnings": self.warnings
        }
