#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的重复检测调试脚本

调试具体案例：规则ID 1696已被采用，规则ID 2023应该显示重复信息
医保名称1：言语能力评定

使用方法：
python simple_debug.py [hospital_id]

作者: Augment Agent
日期: 2025-07-22
"""

import sys
import requests
import json
from datetime import datetime

def test_api_response(hospital_id=1):
    """测试API响应中的重复信息"""
    print("=" * 80)
    print("测试: API响应中的重复信息")
    print("=" * 80)
    
    base_url = "http://localhost:5001"
    
    try:
        print(f"调用推荐规则生成API，医院ID: {hospital_id}")
        
        response = requests.post(f"{base_url}/api/hospital-rules/generate",
                               json={"hospital_id": hospital_id},
                               timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                recommendations = result.get('recommendations', [])
                
                print(f"✓ API调用成功，推荐规则数量: {len(recommendations)}")
                
                # 查找规则ID 2023
                rule_2023 = None
                for rec in recommendations:
                    if rec.get('规则ID') == 2023:
                        rule_2023 = rec
                        break
                
                if rule_2023:
                    print(f"\n✓ 找到规则ID 2023:")
                    print(f"  规则名称: {rule_2023.get('规则名称')}")
                    print(f"  类型: {rule_2023.get('类型')}")
                    print(f"  状态: {rule_2023.get('状态')}")
                    print(f"  匹配项目: {rule_2023.get('匹配项目')}")
                    
                    duplicate_rules = rule_2023.get('重复已采用规则', [])
                    print(f"  重复已采用规则数量: {len(duplicate_rules)}")
                    
                    if duplicate_rules:
                        print(f"  重复规则详情:")
                        for i, dup_rule in enumerate(duplicate_rules, 1):
                            print(f"    {i}. 规则ID: {dup_rule.get('规则ID')}")
                            print(f"       规则名称: {dup_rule.get('规则名称')}")
                            print(f"       类型: {dup_rule.get('类型')}")
                            print(f"       重复项目: {dup_rule.get('重复项目')}")
                            
                            if dup_rule.get('规则ID') == 1696:
                                print(f"       ✓ 找到规则ID 1696!")
                                if '言语能力评定' in dup_rule.get('重复项目', []):
                                    print(f"       ✓ 确认包含'言语能力评定'")
                                else:
                                    print(f"       ❌ 未包含'言语能力评定'")
                                    print(f"       实际重复项目: {dup_rule.get('重复项目')}")
                    else:
                        print(f"  ❌ 未找到重复已采用规则")
                        print(f"     这表明重复检测功能可能存在问题")
                        
                        # 输出更多调试信息
                        print(f"  调试信息:")
                        print(f"    规则ID: {rule_2023.get('规则ID')}")
                        print(f"    类型: {rule_2023.get('类型')}")
                        print(f"    对照ID: {rule_2023.get('对照ID')}")
                        
                        # 检查是否有重复已采用规则字段
                        if '重复已采用规则' in rule_2023:
                            print(f"    重复已采用规则字段存在: ✓")
                            print(f"    重复已采用规则值: {rule_2023.get('重复已采用规则')}")
                        else:
                            print(f"    重复已采用规则字段存在: ❌")
                            print(f"    可用字段: {list(rule_2023.keys())}")
                else:
                    print(f"❌ 未找到规则ID 2023")
                    print(f"   可能原因：")
                    print(f"   1. 规则ID 2023不存在")
                    print(f"   2. 规则ID 2023未被推荐给该医院")
                    print(f"   3. 推荐生成逻辑有问题")
                    
                    # 显示前几个推荐规则的ID
                    print(f"\n  前10个推荐规则ID:")
                    for i, rec in enumerate(recommendations[:10], 1):
                        print(f"    {i}. 规则ID: {rec.get('规则ID')}, 名称: {rec.get('规则名称')}")
                
                return True
            else:
                print(f"❌ API返回错误: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adopted_rules(hospital_id=1):
    """测试已采用规则API"""
    print("\n" + "=" * 80)
    print("测试: 已采用规则API")
    print("=" * 80)
    
    base_url = "http://localhost:5001"
    
    try:
        print(f"调用已采用规则API，医院ID: {hospital_id}")
        
        response = requests.get(f"{base_url}/api/hospital-rules/adopted/{hospital_id}", 
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                adopted_rules = result.get('rules', [])
                
                print(f"✓ API调用成功，已采用规则数量: {len(adopted_rules)}")
                
                # 查找规则ID 1696
                rule_1696 = None
                for rule in adopted_rules:
                    if rule.get('规则ID') == 1696:
                        rule_1696 = rule
                        break
                
                if rule_1696:
                    print(f"\n✓ 找到规则ID 1696:")
                    print(f"  规则名称: {rule_1696.get('规则名称')}")
                    print(f"  类型: {rule_1696.get('类型')}")
                    print(f"  状态: {rule_1696.get('状态')}")
                    print(f"  匹配项目: {rule_1696.get('匹配项目')}")
                    print(f"  医保名称1: {rule_1696.get('医保名称1')}")
                    print(f"  医保名称2: {rule_1696.get('医保名称2')}")
                    
                    # 检查是否包含"言语能力评定"
                    med_name1 = rule_1696.get('医保名称1', '')
                    med_name2 = rule_1696.get('医保名称2', '')
                    
                    if '言语能力评定' in med_name1 or '言语能力评定' in med_name2:
                        print(f"  ✓ 确认包含'言语能力评定'")
                    else:
                        print(f"  ❌ 未包含'言语能力评定'")
                        print(f"     医保名称1: {med_name1}")
                        print(f"     医保名称2: {med_name2}")
                else:
                    print(f"❌ 未找到规则ID 1696")
                    print(f"   前10个已采用规则ID:")
                    for i, rule in enumerate(adopted_rules[:10], 1):
                        print(f"     {i}. 规则ID: {rule.get('规则ID')}, 名称: {rule.get('规则名称')}")
                
                return True
            else:
                print(f"❌ API返回错误: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主调试函数"""
    hospital_id = 1
    if len(sys.argv) > 1:
        try:
            hospital_id = int(sys.argv[1])
        except ValueError:
            print("医院ID必须是数字")
            sys.exit(1)
    
    print("重复检测调试")
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"医院ID: {hospital_id}")
    print(f"测试案例: 规则ID 1696（已采用）vs 规则ID 2023（推荐）")
    print(f"预期重复项目: 言语能力评定")
    
    try:
        # 测试1: 已采用规则API
        success1 = test_adopted_rules(hospital_id)
        
        # 测试2: 推荐规则API
        success2 = test_api_response(hospital_id)
        
        # 输出调试结果
        print("\n" + "=" * 80)
        print("调试结果汇总")
        print("=" * 80)
        
        print(f"已采用规则API测试: {'✓ 通过' if success1 else '❌ 失败'}")
        print(f"推荐规则API测试: {'✓ 通过' if success2 else '❌ 失败'}")
        
        if success1 and success2:
            print("\n✅ 调试完成，请检查上述输出找出问题原因")
        else:
            print("\n❌ 调试过程中发现问题，请检查错误信息")
            
        return success1 and success2
            
    except Exception as e:
        print(f"\n❌ 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
