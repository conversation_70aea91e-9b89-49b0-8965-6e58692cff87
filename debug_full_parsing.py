"""
调试完整解析过程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sql_deep_parser import DeepSQLParser

def debug_full_parsing():
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    WITH tab1 AS (
        SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
        FROM 医保住院结算明细 WHERE 医保项目名称 = 'A'
        INTERSECT
        SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
        FROM 医保住院结算明细 WHERE 医保项目名称 = 'B'
    )
    SELECT * FROM tab1
    """
    
    print("开始完整解析过程...")
    
    # 手动执行解析步骤
    import sqlglot
    from sqlglot import expressions as exp
    from sql_deep_parser.models import RuleLogicIR, RuleType
    
    # 1. 预处理SQL
    cleaned_sql = parser._preprocess_sql(sql)
    print(f"1. 预处理后的SQL: {len(cleaned_sql)} 字符")
    
    # 2. 解析为AST
    ast = parser._parse_to_ast(cleaned_sql)
    print(f"2. AST类型: {type(ast)}")
    
    # 3. 分析AST
    rule_ir = RuleLogicIR(rule_type=RuleType.UNKNOWN, original_sql=cleaned_sql)
    
    print("3. 开始分析AST...")
    if isinstance(ast, exp.With):
        print("   检测到WITH语句，调用_analyze_with_statement")
        parser._analyze_with_statement(ast, rule_ir)
    else:
        print(f"   AST不是WITH语句，类型: {type(ast)}")
    
    # 4. 检查结果
    print(f"4. 分析结果:")
    print(f"   规则类型: {rule_ir.rule_type.value}")
    print(f"   数据源数量: {len(rule_ir.data_sources)}")
    print(f"   条件数量: {len(rule_ir.conditions)}")
    print(f"   是否有重复收费模式: {rule_ir.duplicate_pattern is not None}")
    
    if rule_ir.duplicate_pattern:
        print(f"   重复收费模式: {rule_ir.duplicate_pattern.to_dict()}")
    
    # 5. 规则类型推断
    inferred_type = parser._infer_rule_type(rule_ir)
    print(f"5. 推断的规则类型: {inferred_type.value}")
    
    # 6. 完整解析
    print("\n6. 完整解析结果:")
    result = parser.parse(sql)
    print(f"   解析成功: {result.success}")
    if result.success:
        print(f"   最终规则类型: {result.rule_ir.rule_type.value}")
        if result.rule_ir.duplicate_pattern:
            print(f"   最终重复收费模式: {result.rule_ir.duplicate_pattern.to_dict()}")

if __name__ == "__main__":
    debug_full_parsing()
