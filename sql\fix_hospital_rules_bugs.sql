-- 修复医院规则推荐系统Bug的数据库升级脚本
-- Bug 1: 匹配项目字段显示问题 (前端修复)
-- Bug 2: 数据库字段长度限制错误 (数据库结构修复)
-- 
-- 执行日期: 2025-07-20
-- 作者: Augment Agent

-- =====================================================
-- 修复Bug 2: 扩展匹配项目字段长度
-- =====================================================

-- 1. 备份现有数据（可选，建议在生产环境执行）
-- CREATE TABLE 医院适用规则表_BACKUP AS SELECT * FROM 医院适用规则表;

-- 2. 修改匹配项目字段长度从VARCHAR2(500)扩展到VARCHAR2(4000)
-- 注意：Oracle中修改字段长度只能增加，不能减少
ALTER TABLE 医院适用规则表 MODIFY (匹配项目 VARCHAR2(4000));

-- 3. 同时扩展推荐原因字段长度，防止类似问题
ALTER TABLE 医院适用规则表 MODIFY (推荐原因 VARCHAR2(2000));

-- 4. 添加注释说明字段用途
COMMENT ON COLUMN 医院适用规则表.匹配项目 IS '匹配的医保项目列表，多个项目用"、"分隔，最大长度4000字符';
COMMENT ON COLUMN 医院适用规则表.推荐原因 IS '规则推荐原因说明，最大长度2000字符';

-- 5. 创建检查约束，确保字段长度不超过限制（可选）
-- ALTER TABLE 医院适用规则表 ADD CONSTRAINT CK_匹配项目长度 CHECK (LENGTH(匹配项目) <= 4000);
-- ALTER TABLE 医院适用规则表 ADD CONSTRAINT CK_推荐原因长度 CHECK (LENGTH(推荐原因) <= 2000);

-- 6. 重新收集表统计信息以优化查询性能
ANALYZE TABLE 医院适用规则表 COMPUTE STATISTICS;

-- 7. 验证修改结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    DATA_LENGTH,
    DATA_PRECISION,
    DATA_SCALE,
    NULLABLE
FROM USER_TAB_COLUMNS 
WHERE TABLE_NAME = '医院适用规则表' 
AND COLUMN_NAME IN ('匹配项目', '推荐原因')
ORDER BY COLUMN_NAME;

-- 8. 检查现有数据中匹配项目字段的长度分布
SELECT 
    '匹配项目长度分布' AS 统计类型,
    CASE 
        WHEN LENGTH(匹配项目) <= 100 THEN '0-100字符'
        WHEN LENGTH(匹配项目) <= 200 THEN '101-200字符'
        WHEN LENGTH(匹配项目) <= 500 THEN '201-500字符'
        WHEN LENGTH(匹配项目) <= 1000 THEN '501-1000字符'
        WHEN LENGTH(匹配项目) <= 2000 THEN '1001-2000字符'
        WHEN LENGTH(匹配项目) <= 4000 THEN '2001-4000字符'
        ELSE '超过4000字符'
    END AS 长度范围,
    COUNT(*) AS 记录数量
FROM 医院适用规则表 
WHERE 匹配项目 IS NOT NULL
GROUP BY 
    CASE 
        WHEN LENGTH(匹配项目) <= 100 THEN '0-100字符'
        WHEN LENGTH(匹配项目) <= 200 THEN '101-200字符'
        WHEN LENGTH(匹配项目) <= 500 THEN '201-500字符'
        WHEN LENGTH(匹配项目) <= 1000 THEN '501-1000字符'
        WHEN LENGTH(匹配项目) <= 2000 THEN '1001-2000字符'
        WHEN LENGTH(匹配项目) <= 4000 THEN '2001-4000字符'
        ELSE '超过4000字符'
    END
ORDER BY 
    CASE 
        WHEN LENGTH(匹配项目) <= 100 THEN 1
        WHEN LENGTH(匹配项目) <= 200 THEN 2
        WHEN LENGTH(匹配项目) <= 500 THEN 3
        WHEN LENGTH(匹配项目) <= 1000 THEN 4
        WHEN LENGTH(匹配项目) <= 2000 THEN 5
        WHEN LENGTH(匹配项目) <= 4000 THEN 6
        ELSE 7
    END;

-- 9. 显示最长的匹配项目记录（用于验证）
SELECT 
    适用ID,
    医院ID,
    规则ID,
    LENGTH(匹配项目) AS 匹配项目长度,
    SUBSTR(匹配项目, 1, 100) || '...' AS 匹配项目预览
FROM 医院适用规则表 
WHERE 匹配项目 IS NOT NULL
ORDER BY LENGTH(匹配项目) DESC
FETCH FIRST 10 ROWS ONLY;

-- =====================================================
-- 升级完成提示
-- =====================================================
SELECT '数据库结构升级完成！' AS 状态,
       '匹配项目字段已从VARCHAR2(500)扩展到VARCHAR2(4000)' AS 修改内容,
       SYSDATE AS 完成时间
FROM DUAL;

-- =====================================================
-- 回滚脚本（如果需要回滚，请谨慎使用）
-- =====================================================
-- 注意：Oracle不支持直接缩小字段长度，如果需要回滚，需要：
-- 1. 确保所有数据长度都小于500字符
-- 2. 创建新表，复制数据，删除旧表，重命名新表
-- 
-- 回滚步骤（仅在确认安全的情况下执行）：
-- 
-- -- 检查是否有超过500字符的数据
-- SELECT COUNT(*) FROM 医院适用规则表 WHERE LENGTH(匹配项目) > 500;
-- 
-- -- 如果上述查询返回0，则可以安全回滚
-- -- CREATE TABLE 医院适用规则表_NEW AS SELECT * FROM 医院适用规则表;
-- -- ALTER TABLE 医院适用规则表_NEW MODIFY (匹配项目 VARCHAR2(500));
-- -- DROP TABLE 医院适用规则表;
-- -- RENAME 医院适用规则表_NEW TO 医院适用规则表;
