#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化测试脚本 - 测试3个页面的功能
"""

import urllib.request
import urllib.parse
import json
import time
from datetime import datetime
import sys

class AutomatedTester:
    def __init__(self, base_url="http://127.0.0.1:5001"):
        self.base_url = base_url
        self.test_results = []
        
    def log_test(self, test_name, status, message="", data=None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "status": status,  # "PASS", "FAIL", "WARNING"
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        self.test_results.append(result)
        
        # 控制台输出
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {message}")
        
    def test_server_connection(self):
        """测试服务器连接"""
        try:
            with urllib.request.urlopen(f"{self.base_url}/", timeout=10) as response:
                if response.getcode() == 200:
                    self.log_test("服务器连接", "PASS", f"状态码: {response.getcode()}")
                    return True
                else:
                    self.log_test("服务器连接", "FAIL", f"状态码: {response.getcode()}")
                    return False
        except Exception as e:
            self.log_test("服务器连接", "FAIL", f"连接失败: {str(e)}")
            return False
    
    def test_page_loading(self):
        """测试页面加载"""
        pages = [
            ("主页", "/"),
            ("飞检规则知识库", "/rule_knowledge_base"),
            ("规则SQL生成器", "/rule_sql_generator"),
            ("系统规则语句", "/system_rules")
        ]
        
        all_passed = True
        for page_name, path in pages:
            try:
                with urllib.request.urlopen(f"{self.base_url}{path}", timeout=10) as response:
                    if response.getcode() == 200:
                        # 检查页面内容是否包含预期元素
                        content = response.read().decode('utf-8')
                        if "<!DOCTYPE html>" in content:
                            self.log_test(f"页面加载-{page_name}", "PASS", "页面正常加载")
                        else:
                            self.log_test(f"页面加载-{page_name}", "WARNING", "页面内容异常")
                            all_passed = False
                    else:
                        self.log_test(f"页面加载-{page_name}", "FAIL", f"状态码: {response.getcode()}")
                        all_passed = False
            except Exception as e:
                self.log_test(f"页面加载-{page_name}", "FAIL", f"加载失败: {str(e)}")
                all_passed = False
        
        return all_passed
    
    def test_api_endpoints(self):
        """测试API接口"""
        apis = [
            ("规则类型API", "/api/type_types"),
            ("行为认定API", "/api/behavior_types"),
            ("规则来源API", "/api/rule_sources"),
            ("城市类型API", "/api/city_types"),
            ("规则类型分类API", "/api/rule_type_types"),
            ("规则列表API", "/api/rules"),
            ("筛选选项API", "/api/filter_options"),
            ("SQL历史API", "/api/sql_history")
        ]
        
        all_passed = True
        for api_name, path in apis:
            try:
                with urllib.request.urlopen(f"{self.base_url}{path}", timeout=10) as response:
                    if response.getcode() == 200:
                        try:
                            content = response.read().decode('utf-8')
                            data = json.loads(content)
                            # 检查响应数据格式
                            if isinstance(data, dict) and (data.get('success') or data.get('types') or data.get('data')):
                                self.log_test(f"API测试-{api_name}", "PASS", "响应正常", {"data_keys": list(data.keys())})
                            elif isinstance(data, list):
                                self.log_test(f"API测试-{api_name}", "PASS", f"返回{len(data)}条记录")
                            else:
                                self.log_test(f"API测试-{api_name}", "WARNING", "响应格式异常", {"response": str(data)[:100]})
                        except json.JSONDecodeError:
                            self.log_test(f"API测试-{api_name}", "FAIL", "响应不是有效JSON")
                            all_passed = False
                    else:
                        self.log_test(f"API测试-{api_name}", "FAIL", f"状态码: {response.getcode()}")
                        all_passed = False
            except Exception as e:
                self.log_test(f"API测试-{api_name}", "FAIL", f"请求失败: {str(e)}")
                all_passed = False
        
        return all_passed
    
    def test_search_functionality(self):
        """测试搜索功能"""
        # 测试规则搜索API
        try:
            search_params = {
                "behavior_type": "",
                "city": "",
                "rule_source": "",
                "rule_name": "",
                "type": "",
                "rule_type": "",
                "visit_type": "住院"
            }
            
            url = f"{self.base_url}/api/rules/search?" + urllib.parse.urlencode(search_params)
            with urllib.request.urlopen(url, timeout=10) as response:
                if response.getcode() == 200:
                    content = response.read().decode('utf-8')
                    data = json.loads(content)
                    if data.get('success') and data.get('rules'):
                        rule_count = len(data['rules'])
                        self.log_test("搜索功能-规则搜索", "PASS", f"搜索成功，返回{rule_count}条规则")
                    else:
                        self.log_test("搜索功能-规则搜索", "WARNING", "搜索响应格式异常")
                else:
                    self.log_test("搜索功能-规则搜索", "FAIL", f"搜索失败，状态码: {response.getcode()}")
        except Exception as e:
            self.log_test("搜索功能-规则搜索", "FAIL", f"搜索异常: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 50)
        print("开始自动化测试")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        # 1. 基础连接测试
        print("\n1. 基础连接测试")
        if not self.test_server_connection():
            print("❌ 服务器连接失败，终止测试")
            return False
        
        # 2. 页面加载测试
        print("\n2. 页面加载测试")
        self.test_page_loading()
        
        # 3. API接口测试
        print("\n3. API接口测试")
        self.test_api_endpoints()
        
        # 4. 搜索功能测试
        print("\n4. 搜索功能测试")
        self.test_search_functionality()
        
        return True
    
    def generate_report(self):
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        warning_tests = len([r for r in self.test_results if r['status'] == 'WARNING'])
        
        report = f"""
{'=' * 60}
自动化测试报告
{'=' * 60}
测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试目标: 飞检规则知识库系统 - 3个核心页面

测试统计:
- 总测试数: {total_tests}
- 通过: {passed_tests} ✅
- 失败: {failed_tests} ❌  
- 警告: {warning_tests} ⚠️
- 成功率: {(passed_tests/total_tests*100):.1f}%

详细结果:
"""
        
        for result in self.test_results:
            status_icon = "✅" if result['status'] == "PASS" else "❌" if result['status'] == "FAIL" else "⚠️"
            report += f"{status_icon} {result['test_name']}: {result['message']}\n"
        
        report += f"\n{'=' * 60}\n"
        
        return report

if __name__ == "__main__":
    tester = AutomatedTester()
    
    if tester.run_all_tests():
        report = tester.generate_report()
        print(report)
        
        # 保存报告到文件
        with open("test_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        print("测试报告已保存到 test_report.txt")
    else:
        print("测试执行失败")
        sys.exit(1)
