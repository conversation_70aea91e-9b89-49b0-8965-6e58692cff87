"""
调试时间精度检测问题 - 第二版
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sql_deep_parser import DeepSQLParser

def debug_time_precision():
    parser = DeepSQLParser(dialect="postgres")
    
    sql = """
    WITH tab1 AS (
        SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
        FROM 医保住院结算明细 WHERE 医保项目名称 = 'A'
        INTERSECT
        SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
        FROM 医保住院结算明细 WHERE 医保项目名称 = 'B'
    )
    SELECT * FROM tab1
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        
        if rule_ir.duplicate_pattern:
            print("重复收费模式:")
            print(f"  主要项目: {rule_ir.duplicate_pattern.primary_items}")
            print(f"  冲突项目: {rule_ir.duplicate_pattern.conflict_items}")
            print(f"  时间精度: {rule_ir.duplicate_pattern.time_precision}")
        else:
            print("未检测到重复收费模式")
            
        # 手动测试时间精度提取
        import sqlglot
        ast = sqlglot.parse_one(sql)
        print(f"\n原始SQL包含hh24: {'hh24' in sql.lower()}")
        print(f"AST字符串包含hh24: {'hh24' in str(ast).lower()}")
        
        # 手动调用时间精度提取方法
        precision = parser._extract_time_precision(ast)
        print(f"手动提取的时间精度: {precision}")
    else:
        print(f"解析失败: {result.error_message}")

if __name__ == "__main__":
    debug_time_precision()
