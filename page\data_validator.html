<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据校验工具</title>
    <style>
        :root {
            --primary-color: #0078D4;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --hover-bg: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            padding: 2rem;
        }


        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        nav {
            margin-bottom: 2rem;
        }

        nav a {
            color: var(--primary);
            text-decoration: none;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: var(--transition);
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav a:hover {
            background-color: rgba(0, 120, 212, 0.1);
        }

        h2 {
            margin-bottom: 2rem;
            color: var(--text-primary);
            font-size: 1.5rem;
        }

        .upload-section {
            margin-bottom: 2rem;
        }

        .file-group {
            margin-bottom: 1.5rem;
        }

        .file-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            font-weight: 500;
        }

        .file-group input[type="file"] {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #e2e8f0;
            border-radius: var(--radius);
        }

        .validation-rules {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background-color: var(--bg-color);
            border-radius: var(--radius);
        }

        .rule-item {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .rule-item input[type="checkbox"] {
            margin-right: 0.5rem;
        }

        .submit-button {
            background-color: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .submit-button:hover {
            opacity: 0.9;
        }

        .error-message {
            background-color: #fee2e2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 1rem;
            border-radius: var(--radius);
            margin-bottom: 1.5rem;
        }

        .result-section {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e2e8f0;
        }

        .result-section h3 {
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .result-content {
            background-color: var(--bg-color);
            padding: 1.5rem;
            border-radius: var(--radius);
            line-height: 1.6;
        }
    </style>
</head>
<body>

    <div class="container">
        <nav>
            <a href="{{ url_for('index') }}">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M9.78 12.78a.75.75 0 01-1.06 0L4.47 8.53a.75.75 0 010-1.06l4.25-4.25a.75.75 0 011.06 1.06L6.06 8l3.72 3.72a.75.75 0 010 1.06z"/>
                </svg>
                返回主页
            </a>
        </nav>
        <h2>数据校验工具</h2>
        
        {% if error %}
        <div class="error-message">
            {{ error }}
        </div>
        {% endif %}
        
        <form action="{{ url_for('data_validator') }}" method="post" enctype="multipart/form-data">
            <div class="upload-section">
                <div class="file-group">
                    <label>待验证文件：</label>
                    <input type="file" name="file" accept=".xlsx,.xls,.csv" required>
                </div>
            </div>

            <div class="validation-rules">
                <h3>验证规则设置</h3>
                <div class="rule-item">
                    <input type="checkbox" name="check_empty" id="check_empty" checked>
                    <label for="check_empty">检查空值</label>
                </div>
                <div class="rule-item">
                    <input type="checkbox" name="check_format" id="check_format" checked>
                    <label for="check_format">检查数据格式</label>
                </div>
                <div class="rule-item">
                    <input type="checkbox" name="check_range" id="check_range" checked>
                    <label for="check_range">检查数值范围</label>
                </div>
            </div>

            <button type="submit" class="submit-button">开始验证</button>
        </form>

        {% if validation_result %}
        <div class="result-section">
            <h3>验证结果：</h3>
            <div class="result-content">
                {{ validation_result | safe }}
            </div>
        </div>
        {% endif %}
    </div>
</body>
</html>