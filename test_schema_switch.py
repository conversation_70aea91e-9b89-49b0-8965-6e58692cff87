#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Schema切换功能
"""

import urllib.request
import json
import time
from datetime import datetime

def test_schema_switch():
    """测试Schema切换功能"""
    base_url = "http://127.0.0.1:5001"
    
    print("=" * 70)
    print("测试Schema切换功能")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 1. 测试在默认schema中查询（应该找不到医保表）
    print("\n🔍 测试1: 在默认schema中查询医保表（应该找不到）")
    try:
        sql = "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_name = '医保住院结算明细'"
        
        execute_data = {
            'sql': sql,
            'database': 'pg',
            'host': 'default',
            'schema': ''  # 不指定schema，使用默认
        }
        
        req_data = json.dumps(execute_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=10) as response:
            content = response.read().decode('utf-8')
            data = json.loads(content)
            
            if data.get('success'):
                count = data.get('data', [[0]])[0][0]
                print(f"   ✅ 默认schema中医保表数量: {count}")
                if count == 0:
                    print("   ✅ 符合预期：默认schema中没有医保表")
                else:
                    print("   ⚠️ 意外：默认schema中有医保表")
            else:
                print(f"   ❌ 查询失败: {data.get('error', 'Unknown error')}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
    
    # 2. 测试在指定schema中查询（应该找到医保表）
    print("\n🔍 测试2: 在指定schema中查询医保表（应该找到）")
    try:
        sql = "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'ZQS_YY_ZQDXFSFLYY_7ZD' AND table_name = '医保住院结算明细'"
        
        execute_data = {
            'sql': sql,
            'database': 'pg',
            'host': 'default',
            'schema': 'ZQS_YY_ZQDXFSFLYY_7ZD'  # 指定schema
        }
        
        req_data = json.dumps(execute_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=10) as response:
            content = response.read().decode('utf-8')
            data = json.loads(content)
            
            if data.get('success'):
                count = data.get('data', [[0]])[0][0]
                print(f"   ✅ 指定schema中医保表数量: {count}")
                if count > 0:
                    print("   ✅ 符合预期：指定schema中有医保表")
                else:
                    print("   ❌ 意外：指定schema中没有医保表")
            else:
                print(f"   ❌ 查询失败: {data.get('error', 'Unknown error')}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
    
    # 3. 测试在指定schema中直接查询医保表（验证search_path是否生效）
    print("\n🔍 测试3: 在指定schema中直接查询医保表（验证search_path）")
    try:
        sql = "SELECT COUNT(*) as record_count FROM 医保住院结算明细 LIMIT 1"
        
        execute_data = {
            'sql': sql,
            'database': 'pg',
            'host': 'default',
            'schema': 'ZQS_YY_ZQDXFSFLYY_7ZD'  # 指定schema
        }
        
        req_data = json.dumps(execute_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=15) as response:
            content = response.read().decode('utf-8')
            data = json.loads(content)
            
            if data.get('success'):
                print("   🎉 成功！可以直接查询医保表，说明schema切换生效")
                print(f"   数据库: {data.get('database', 'Unknown')}")
                print(f"   Schema: {data.get('schema', 'Unknown')}")
                print(f"   返回行数: {data.get('affected_rows', 0)}")
            else:
                error_msg = data.get('error', 'Unknown error')
                if '不存在' in error_msg:
                    print("   ❌ Schema切换未生效：仍然找不到医保表")
                else:
                    print(f"   ❌ 其他错误: {error_msg}")
    except Exception as e:
        print(f"   ❌ 异常: {str(e)}")
    
    # 4. 测试Oracle的schema切换
    print("\n🔍 测试4: Oracle的schema切换功能")
    try:
        sql = "SELECT SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA') as current_schema FROM dual"
        
        execute_data = {
            'sql': sql,
            'database': 'oracle',
            'host': 'default',
            'schema': 'DATACHANGE'  # 指定Oracle schema
        }
        
        req_data = json.dumps(execute_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=10) as response:
            content = response.read().decode('utf-8')
            data = json.loads(content)
            
            if data.get('success'):
                current_schema = data.get('data', [['']])[0][0]
                print(f"   ✅ Oracle当前schema: {current_schema}")
                if current_schema == 'DATACHANGE':
                    print("   ✅ Oracle schema切换成功")
                else:
                    print(f"   ❌ Oracle schema切换失败，期望DATACHANGE，实际{current_schema}")
            else:
                print(f"   ❌ Oracle查询失败: {data.get('error', 'Unknown error')}")
    except Exception as e:
        print(f"   ❌ Oracle测试异常: {str(e)}")
    
    print("\n" + "=" * 70)
    print("Schema切换功能测试完成!")

if __name__ == "__main__":
    test_schema_switch()
