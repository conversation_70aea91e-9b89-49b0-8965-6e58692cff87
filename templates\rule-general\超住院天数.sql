with tab1 as(
  select 
    结算单据号, 
    医保项目编码, 
    医保项目名称,
    医院项目编码,
    医院项目名称,
    报销比例,
    单价,
    规格,
    费用类别,
    支付类别,
    sum(数量) as 使用数量
   FROM 医保住院结算明细
   WHERE 医保项目编码 in ({医保编码1})
   group by 
    结算单据号, 
    医保项目编码, 
    医保项目名称,
    医院项目编码,
    医院项目名称,
    报销比例,
    单价,
    规格,
    费用类别,
    支付类别
)
select 
 a.结算单据号,
 a.住院号,
 a.病案号,
 a.个人编码,
 a.患者姓名,
 a.患者性别,
 a.患者社会保障号码,
 a.患者年龄,
 a.险种类型,
 a.入院科室,
 a.入院诊断名称,
 a.入院日期,
 a.出院科室,
 a.出院日期,
 a.出院诊断名称,
 TRUNC(a.出院日期) - TRUNC(a.入院日期)+1 as 住院天数,
 a.结算日期,
 b.医保项目编码,
 b.医保项目名称,
 b.医院项目编码,
 b.医院项目名称,
 b.报销比例,
 b.费用类别,
 b.支付类别,
 b.使用数量,
 b.单价,
 b.规格,
 (b.使用数量 - ( TRUNC(a.出院日期) - TRUNC(a.入院日期)+1)) as 超出数量,
 (b.使用数量 - ( TRUNC(a.出院日期) - TRUNC(a.入院日期)+1)) * b.单价 as 超出金额
from 医保住院结算主单 a
join tab1 B on a.结算单据号 = b.结算单据号
   and b.使用数量 >  TRUNC(a.出院日期) - TRUNC(a.入院日期)+1
   ;
