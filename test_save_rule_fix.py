#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试保存规则功能修复
"""

import requests
import json

# 测试配置
base_url = 'http://localhost:5001'

def test_create_rule():
    """测试创建新规则"""
    print('=== 测试创建新规则 ===')
    
    # 测试数据 - 只包含主规则表的字段（根据导入规则的字段映射）
    test_rule_data = {
        '序号': '001',
        '规则名称': '测试规则_修复版',
        '行为认定': '测试行为认定',
        '类型': '药品',
        '规则类型': '0',
        '用途': '测试用途',
        '系统规则': 0,
        '性别': '男',
        '年龄': '18-65',
        '违规数量': 10,
        '违规金额': 1000,
        '违规天数': 7,
        '违规小时数': 24,
        '备注': '测试备注',
        '涉及科室': '内科',
        '国家医保编码1': 'GJ001',
        '国家医保名称1': '国家医保测试1',
        '国家医保编码2': 'GJ002',
        '国家医保名称2': '国家医保测试2',
        '排除诊断': '排除测试诊断',
        '排除科室': '排除测试科室',
        '包含诊断': '包含测试诊断',
        '包含科室': '包含测试科室',
        '时间类型': '天',
        '项目数量': 5
    }
    
    try:
        response = requests.post(
            f'{base_url}/api/rules',
            json=test_rule_data,
            timeout=30
        )
        
        print(f'创建规则API - 状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'创建结果: {json.dumps(data, ensure_ascii=False, indent=2)}')
            
            if data.get('success'):
                print(f'✅ 成功创建规则，ID: {data.get("id")}')
                return data.get('id')
            else:
                print(f'❌ 创建失败: {data.get("error")}')
        else:
            print(f'❌ API错误: {response.text}')
            
    except Exception as e:
        print(f'❌ 请求失败: {e}')
    
    return None

def test_update_rule(rule_id):
    """测试更新规则"""
    if not rule_id:
        print('⚠️ 跳过更新测试，因为没有有效的规则ID')
        return
        
    print(f'\n=== 测试更新规则 {rule_id} ===')
    
    # 测试数据 - 只包含主规则表的字段
    test_rule_data = {
        '序号': '002',
        '规则名称': '测试规则_修复版_已更新',
        '行为认定': '更新后的行为认定',
        '类型': '诊疗',
        '规则类型': '1',
        '用途': '更新后的用途',
        '系统规则': 1,
        '性别': '女',
        '年龄': '20-60',
        '违规数量': 20,
        '违规金额': 2000,
        '违规天数': 14,
        '违规小时数': 48,
        '备注': '更新后的备注',
        '涉及科室': '外科',
        '国家医保编码1': 'GJ003',
        '国家医保名称1': '更新国家医保测试1',
        '国家医保编码2': 'GJ004',
        '国家医保名称2': '更新国家医保测试2'
    }
    
    try:
        response = requests.put(
            f'{base_url}/api/rules/{rule_id}',
            json=test_rule_data,
            timeout=30
        )
        
        print(f'更新规则API - 状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'更新结果: {json.dumps(data, ensure_ascii=False, indent=2)}')
            
            if data.get('success'):
                print(f'✅ 成功更新规则 {rule_id}')
            else:
                print(f'❌ 更新失败: {data.get("error")}')
        else:
            print(f'❌ API错误: {response.text}')
            
    except Exception as e:
        print(f'❌ 请求失败: {e}')

def test_api_availability():
    """测试API可用性"""
    print('\n=== 测试API可用性 ===')
    
    try:
        # 测试创建规则API
        response = requests.post(f'{base_url}/api/rules', json={}, timeout=5)
        if response.status_code in [200, 400, 500]:  # 这些状态码说明端点存在
            print('✅ 创建规则 API 可用')
        else:
            print(f'❌ 创建规则 API 不可用 (状态码: {response.status_code})')
            
    except Exception as e:
        print(f'❌ 创建规则 API 测试失败: {e}')

def main():
    """主测试函数"""
    print('=' * 60)
    print('保存规则功能修复测试')
    print('=' * 60)
    print('修复说明：')
    print('1. 将医保相关字段从主规则数据中分离')
    print('2. 医保字段只在保存医保编码对照时使用')
    print('3. 主规则表只保存基本规则信息')
    print('=' * 60)
    
    # 运行测试
    test_api_availability()
    rule_id = test_create_rule()
    test_update_rule(rule_id)
    
    print('\n' + '=' * 60)
    print('测试完成')
    print('=' * 60)
    print('注意：')
    print('1. 如果创建成功，说明医保字段分离修复有效')
    print('2. 医保编码对照需要在前端选择城市后单独保存')
    print('3. 前端表单的医保字段现在不会影响主规则保存')

if __name__ == "__main__":
    main()
