WITH tab1 AS (
  SELECT 
    结算单据号||TO_CHAR(项目使用日期,'yyyy-mm-dd') guanlian
  FROM ZZS_YY_ZDYFY_4NG.医保住院结算明细
  WHERE 
    医保项目编码 IN ('003107010030000-310701003')
    AND 结算单据号||TO_CHAR(项目使用日期,'yyyy-mm-dd') IN (
      SELECT 结算单据号||TO_CHAR(项目使用日期,'yyyy-mm-dd')
      FROM ZZS_YY_ZDYFY_4NG.医保住院结算明细
      WHERE 医保项目编码 IN ('003107010180000-310701018')
      GROUP BY 结算单据号||TO_CHAR(项目使用日期,'yyyy-mm-dd')
      HAVING SUM(数量) > 0
    )
  GROUP BY 结算单据号||TO_CHAR(项目使用日期,'yyyy-mm-dd')
  HAVING SUM(数量) > 0
)
SELECT
  A. 病案号,
  A.结算单据号 结算单据号,
  A.医疗机构编码 医疗机构编码,
  A.医疗机构名称 医疗机构名称,
  A.结算日期 结算日期,
  A.住院号 住院号,
  A.个人编码 个人编码,
  A.患者社会保障号码 患者社会保障号码,
  A.身份证号 身份证号,
  A.险种类型 险种类型,
  A.入院科室 入院科室,
  A.出院科室 出院科室,
  A.主诊医师姓名 主诊医师姓名,
  A.患者姓名 患者姓名,
  A.患者年龄 患者年龄,
  A.患者性别 患者性别,
  A.异地标志 异地标志,
  A.入院日期 入院日期,
  A.出院日期 出院日期,
  A.住院天数  住院天数,
  A.医疗总费用 医疗总费用,
  A.基本统筹支付 基本统筹支付,
  A.个人自付 个人自付,
  A.个人自费 个人自费,
  A.符合基本医疗保险的费用 符合基本医疗保险的费用,
  A.入院诊断编码 入院诊断编码,
  A.入院诊断名称 入院诊断名称,
  A.出院诊断编码 出院诊断编码,
  A.出院诊断名称 出院诊断名称,
  A.主手术及操作编码 主手术及操作编码,
  A.主手术及操作名称 主手术及操作名称,
  A.其他手术及操作编码 其他手术及操作编码,
  A.其他手术及操作名称 其他手术及操作名称,
  B.开单科室名称 开单科室名称,
  B.执行科室名称 执行科室名称,
  B.开单医师姓名 开单医师姓名, 
  B.费用类别 费用类别,
  B.项目使用日期 项目使用日期,
  B.医院项目编码 医院项目编码,
  B.医院项目名称 医院项目名称,
  B.医保项目编码 医保项目编码,
  B.医保项目名称 医保项目名称,
  B.支付类别 支付类别,
  B.报销比例 报销比例, 
  B.自付比例 自付比例,
  B.支付地点类别 支付地点类别,
  B.明细流水号  明细流水号,
  B.规格 规格,
  B.单价 单价,
  B.数量 数量,
  B.金额 金额,
  B.医保范围内金额 医保范围内金额,
	CASE WHEN B.医保项目编码 IN ('003107010180000-310701018') THEN B.数量 ELSE 0 END AS 使用总数量,
	CASE WHEN B.医保项目编码 IN ('003107010180000-310701018') THEN B.金额 ELSE 0 END AS 使用总金额,
	CASE WHEN B.医保项目编码 IN ('003107010180000-310701018') THEN B.数量 ELSE 0 END AS 违规数量,
	CASE WHEN B.医保项目编码 IN ('003107010180000-310701018') THEN B.金额 ELSE 0 END AS 违规金额
FROM ZZS_YY_ZDYFY_4NG.医保住院结算明细 B
JOIN ZZS_YY_ZDYFY_4NG.医保住院结算主单 A ON A.结算单据号 = B.结算单据号
JOIN   住院诊断信息 diag ON diag.HISID =A.结算单据号  
 JOIN tab1 C ON B.结算单据号||TO_CHAR(项目使用日期,'yyyy-mm-dd') = C.guanlian
 WHERE  B.医保项目编码 IN ('003107010030000-310701003', '003107010180000-310701018' )  
 
 AND    B.开单科室名称 not LIKE '%心%'   AND  B.开单科室名称 not LIKE '%icu%' 
 AND    B.开单科室名称 not LIKE '%ICU%'  AND   B.开单科室名称 not LIKE '%重症%' 
 
  AND   A.入院科室  not LIKE '%心%'   AND  A.入院科室  not LIKE '%icu%' 
 AND    A.入院科室 not LIKE '%ICU%'  AND    A.入院科室 not LIKE '%重症%' 
 
  AND   A.出院科室  not LIKE '%心%'   AND    A.出院科室  not LIKE '%icu%' 
 AND    A.出院科室 not LIKE '%ICU%'  AND    A.出院科室 not LIKE '%重症%' 
 
 AND diag.ADMISSION_DISEASE_NAME  NOT LIKE '%心%'  AND diag.DISCHARGE_DISEASE_NAME_MAIN   NOT LIKE '%心%'  
AND diag.DISCHARGE_DISEASE_NAME_OTHER1||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER2||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER3||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER4||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER5||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER6||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER7||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER8||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER9||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER10||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER11||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER12||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER13||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER14||'@'||diag.DISCHARGE_DISEASE_NAME_OTHER15	NOT LIKE  '%心%'   

ORDER BY 
  A.患者姓名, 
  B.项目使用日期

