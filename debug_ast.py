"""
调试AST结构
"""

import sqlglot
from sqlglot import expressions as exp

sql = """
SELECT * FROM 医保住院结算明细 B
JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
WHERE A.患者年龄 > 50 AND A.患者年龄 < 75 AND A.患者性别 = '男'
"""

# 解析SQL
ast = sqlglot.parse_one(sql)
print("AST结构:")
print(ast)
print("\n" + "="*50)

# 查找WHERE子句
where_clause = ast.find(exp.Where)
if where_clause:
    print("WHERE子句:")
    print(where_clause)
    print(f"WHERE子句类型: {type(where_clause.this)}")
    print("\n" + "="*50)
    
    # 递归打印表达式结构
    def print_expression(expr, indent=0):
        prefix = "  " * indent
        print(f"{prefix}{type(expr).__name__}: {expr}")
        
        if hasattr(expr, 'left') and expr.left:
            print(f"{prefix}  LEFT:")
            print_expression(expr.left, indent + 2)
        if hasattr(expr, 'right') and expr.right:
            print(f"{prefix}  RIGHT:")
            print_expression(expr.right, indent + 2)
        if hasattr(expr, 'this') and expr.this:
            print(f"{prefix}  THIS:")
            print_expression(expr.this, indent + 2)
    
    print("表达式结构:")
    print_expression(where_clause.this)
