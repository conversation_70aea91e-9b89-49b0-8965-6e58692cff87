import os
import re
import pandas as pd
import oracledb
import logging
import unicodedata
import datetime
from typing import List, Dict, Any, Optional, Union, Tuple, Generator
from werkzeug.utils import secure_filename
from contextlib import contextmanager
import zipfile
import io

# ================ 数据库操作相关 ================

@contextmanager
def connect_to_oracle(username: str, password: str, dsn: str) -> Generator[oracledb.Connection, None, None]:
    """
    创建Oracle数据库连接的上下文管理器
    
    Args:
        username: 数据库用户名
        password: 数据库密码
        dsn: 数据库连接字符串
        
    Yields:
        oracledb.Connection: 数据库连接对象
    """
    connection = None
    try:
        connection = oracledb.connect(user=username, password=password, dsn=dsn)
        yield connection
    finally:
        if connection:
            connection.close()

def init_connection_pool(pool_config: Dict[str, Any]) -> oracledb.ConnectionPool:
    """
    初始化数据库连接池
    
    Args:
        pool_config: 连接池配置信息
        
    Returns:
        oracledb.ConnectionPool: 数据库连接池对象
    """
    try:
        pool = oracledb.create_pool(
            user=pool_config['username'],
            password=pool_config['password'],
            dsn=pool_config['dsn'],
            min=2,
            max=5,
            increment=1,
            getmode=oracledb.POOL_GETMODE_WAIT,
            wait_timeout=10000,
            timeout=300,
            retry_count=3,
            retry_delay=2,
            max_lifetime_session=28800
        )
        return pool
    except Exception as e:
        logging.error(f"Failed to initialize connection pool: {str(e)}")
        raise

def fetch_data(connection: oracledb.Connection, query: str, params: Optional[Dict] = None) -> pd.DataFrame:
    """
    执行SQL查询并返回DataFrame
    
    Args:
        connection: 数据库连接对象
        query: SQL查询语句
        params: 查询参数字典
        
    Returns:
        pd.DataFrame: 查询结果DataFrame
    """
    try:
        return pd.read_sql(query, connection, params=params)
    except Exception as e:
        logging.error(f"Error executing query: {str(e)}")
        raise

# ================ 文件操作相关 ================

def sanitize_filename(filename: str) -> str:
    """
    清理文件名，保留合法字符（包括中文）
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 清理后的文件名
    """
    filename = unicodedata.normalize('NFKC', filename)
    filename = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9_\-\.]', '_', filename)
    return filename[:255]

def allowed_file(filename: str, allowed_extensions: set) -> bool:
    """
    检查文件扩展名是否允许
    
    Args:
        filename: 文件名
        allowed_extensions: 允许的扩展名集合
        
    Returns:
        bool: 是否允许
    """
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

def save_excel_to_zip(dataframes: List[Tuple[str, pd.DataFrame]], zip_filename: str) -> str:
    """
    将多个DataFrame保存为Excel文件并打包成zip
    
    Args:
        dataframes: 包含(文件名, DataFrame)元组的列表
        zip_filename: ZIP文件名
        
    Returns:
        str: ZIP文件路径
    """
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for filename, df in dataframes:
            excel_buffer = io.BytesIO()
            df.to_excel(excel_buffer, index=False)
            zip_file.writestr(f"{filename}.xlsx", excel_buffer.getvalue())
    
    # 保存ZIP文件
    output_path = os.path.join('output', zip_filename)
    os.makedirs('output', exist_ok=True)
    with open(output_path, 'wb') as f:
        f.write(zip_buffer.getvalue())
    
    return output_path

# ================ SQL操作相关 ================

def generate_create_statement(table_name: str, table_structure: Dict[str, Any]) -> str:
    """
    生成CREATE TABLE SQL语句
    
    Args:
        table_name: 表名
        table_structure: 表结构定义
        
    Returns:
        str: CREATE TABLE语句
    """
    create_statement = f"CREATE TABLE {table_name} (\n"
    for field_name, field_info in table_structure['fields'].items():
        create_statement += f"    {field_name} {field_info['type']}"
        if field_info.get('required', False):
            create_statement += " NOT NULL"
        create_statement += ",\n"
    create_statement = create_statement.rstrip(",\n") + "\n);"
    return create_statement

def read_sql_template(template_path: str) -> str:
    """
    读取SQL模板文件
    
    Args:
        template_path: 模板文件路径
        
    Returns:
        str: 模板内容
    """
    with open(template_path, 'r', encoding='utf-8') as file:
        return file.read()

def generate_sql_from_template(template: str, variables: Dict[str, Any]) -> str:
    """
    根据Sql板和变量生成SQL
    
    Args:
        template: SQL模板
        variables: 变量字典
        
    Returns:
        str: 生成的SQL语句
    """
    for var_name, value in variables.items():
        if isinstance(value, list):
            value = value[0]
        template = template.replace(f'{{{var_name}}}', str(value).strip())
    return template

# ================ 日志相关 ================

def setup_logging(log_file: str = 'app.log') -> None:
    """
    配置日志系统
    
    Args:
        log_file: 日志文件路径
    """
    logging.basicConfig(
        filename=log_file,
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    ) 

# ================ Excel 处理相关 ================

def excel_to_sql(excel_path: str, output_dir: str = 'sql_files') -> None:
    """
    将Excel文件转换为SQL文件
    
    Args:
        excel_path: Excel文件路径
        output_dir: 输出目录
    """
    try:
        df = pd.read_excel(excel_path)
        os.makedirs(output_dir, exist_ok=True)

        for index, row in df.iterrows():
            rule_name = str(row['规则名称']).strip().replace('/', '_').replace('\\', '_')
            sql_column = 'SQL' if 'SQL' in row else 'sql'
            rule_content = str(row[sql_column]).strip()
            rule_description = str(row.get('规则内涵', '')).strip()
            policy_basis = str(row.get('政策依据', '')).strip()

            # 移除空字符
            rule_name = rule_name.replace('\0', '')
            rule_content = rule_content.replace('\0', '')
            rule_description = rule_description.replace('\0', '')
            policy_basis = policy_basis.replace('\0', '')

            filename = f"{rule_name}.sql"
            sql_content = construct_sql_content(rule_name, rule_description, policy_basis, rule_content)
            
            with open(os.path.join(output_dir, filename), 'w', encoding='utf-8') as file:
                file.write(sql_content)
    except Exception as e:
        logging.error(f"Excel转SQL出错: {str(e)}")
        raise

def compare_excel_files(file1: str, file2: str, compare_mode: str = 'columns') -> pd.DataFrame:
    """
    比较两个Excel文件的差异
    
    Args:
        file1: 第一个Excel文件路径
        file2: 第二个Excel文件路径
        compare_mode: 比较模式 ('columns' 或 'rows')
        
    Returns:
        pd.DataFrame: 差异结果
    """
    df1 = pd.read_excel(file1)
    df2 = pd.read_excel(file2)
    
    if compare_mode == 'columns':
        return compare_excel_columns(df1, df2)
    else:
        return compare_excel_rows(df1, df2)

def split_excel_file(input_file: str, split_columns: List[str], output_dir: str) -> List[str]:
    """
    根据指定列拆分Excel文件
    
    Args:
        input_file: 输入Excel文件路径
        split_columns: 用于拆分的列名列表
        output_dir: 输出目录
        
    Returns:
        List[str]: 生成的文件路径列表
    """
    df = pd.read_excel(input_file)
    output_files = []
    
    for _, group in df.groupby(split_columns):
        filename = construct_split_filename(group, split_columns)
        output_path = os.path.join(output_dir, filename)
        group.to_excel(output_path, index=False)
        output_files.append(output_path)
    
    return output_files

# ================ 数据验证相关 ================

def validate_data(df: pd.DataFrame, rules: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    根据规则验证数据
    
    Args:
        df: 待验证的DataFrame
        rules: 验证规则字典
        
    Returns:
        List[Dict[str, Any]]: 验证结果列表
    """
    validation_results = []
    for column, rule in rules.items():
        if column in df.columns:
            result = apply_validation_rule(df[column], rule)
            validation_results.append({
                'column': column,
                'rule': rule,
                'result': result
            })
    return validation_results

def query_YYJG(db_username: str, db_password: str, db_dsn: str) -> set:
    """
    查询医疗机构编码
    
    Args:
        db_username: 数据库用户名
        db_password: 数据库密码
        db_dsn: 数据库连接字符串
        
    Returns:
        set: 机构编码集合
    """
    try:
        with connect_to_oracle(db_username, db_password, db_dsn) as connection:
            df = fetch_data(connection, "select 定点机构编码 as 机构编码 from 住院医院编码 order by 定点机构编码")
            return set(df['机构编码'])
    except Exception as e:
        logging.error(f"查询医疗机构编码出错: {str(e)}")
        return set()

# ================ 辅助函数 ================

def construct_sql_content(rule_name: str, description: str, policy: str, content: str) -> str:
    """构造SQL文件内容"""
    sql_content = f"-- 规则名称: {rule_name}\n"
    if description:
        sql_content += f"-- 规则内涵: {description}\n"
    if policy:
        sql_content += f"-- 政策依据: {policy}\n"
    sql_content += content
    return sql_content

def compare_excel_columns(df1: pd.DataFrame, df2: pd.DataFrame) -> pd.DataFrame:
    """比较Excel列差异"""
    differences = []
    all_columns = set(df1.columns) | set(df2.columns)
    
    for col in all_columns:
        if col not in df1.columns:
            differences.append({'列名': col, '差异类型': '仅在第二个文件中存在'})
        elif col not in df2.columns:
            differences.append({'列名': col, '差异类型': '仅在第一个文件中存在'})
    
    return pd.DataFrame(differences)

def compare_excel_rows(df1: pd.DataFrame, df2: pd.DataFrame) -> pd.DataFrame:
    """比较Excel行差异"""
    merged = pd.merge(df1, df2, how='outer', indicator=True)
    differences = merged[merged['_merge'] != 'both']
    return differences.drop('_merge', axis=1)

def construct_split_filename(group: pd.DataFrame, split_columns: List[str]) -> str:
    """构造拆分后的文件名"""
    parts = []
    for col in split_columns:
        value = str(group[col].iloc[0])
        parts.append(f"{col}_{value}")
    return f"{'_'.join(parts)}.xlsx"

def apply_validation_rule(series: pd.Series, rule: Dict[str, Any]) -> Dict[str, Any]:
    """应用数据验证规则"""
    rule_type = rule.get('type')
    if rule_type == 'range':
        return validate_range(series, rule.get('min'), rule.get('max'))
    elif rule_type == 'pattern':
        return validate_pattern(series, rule.get('pattern'))
    elif rule_type == 'unique':
        return validate_unique(series)
    return {'valid': True, 'errors': []}

def validate_range(series: pd.Series, min_val: float, max_val: float) -> Dict[str, Any]:
    """验证数值范围"""
    invalid = series[(series < min_val) | (series > max_val)]
    return {
        'valid': len(invalid) == 0,
        'errors': invalid.index.tolist()
    }

def validate_pattern(series: pd.Series, pattern: str) -> Dict[str, Any]:
    """验证正则表达式模式"""
    invalid = series[~series.str.match(pattern)]
    return {
        'valid': len(invalid) == 0,
        'errors': invalid.index.tolist()
    }

def validate_unique(series: pd.Series) -> Dict[str, Any]:
    """验证唯一性"""
    duplicates = series[series.duplicated()]
    return {
        'valid': len(duplicates) == 0,
        'errors': duplicates.index.tolist()
    } 