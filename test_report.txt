
============================================================
自动化测试报告
============================================================
测试时间: 2025-06-09 23:05:18
测试目标: 飞检规则知识库系统 - 3个核心页面

测试统计:
- 总测试数: 14
- 通过: 13 ✅
- 失败: 0 ❌  
- 警告: 1 ⚠️
- 成功率: 92.9%

详细结果:
✅ 服务器连接: 状态码: 200
✅ 页面加载-主页: 页面正常加载
✅ 页面加载-飞检规则知识库: 页面正常加载
✅ 页面加载-规则SQL生成器: 页面正常加载
✅ 页面加载-系统规则语句: 页面正常加载
✅ API测试-规则类型API: 响应正常
✅ API测试-行为认定API: 响应正常
✅ API测试-规则来源API: 响应正常
✅ API测试-城市类型API: 响应正常
✅ API测试-规则类型分类API: 响应正常
✅ API测试-规则列表API: 返回2629条记录
⚠️ API测试-筛选选项API: 响应格式异常
✅ API测试-SQL历史API: 返回4386条记录
✅ 搜索功能-规则搜索: 搜索成功，返回566条规则

============================================================
