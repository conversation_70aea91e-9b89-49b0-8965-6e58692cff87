"""
条件参数提取器
从WHERE子句中提取年龄、性别、诊断、科室等业务条件参数
"""

import re
from typing import List, Optional, Tuple
from .models import RuleConditions, AgeRange


class ConditionExtractor:
    """条件参数提取器"""
    
    def __init__(self):
        # 年龄条件模式
        self.age_patterns = [
            r'患者年龄\s*([><=]+)\s*[\'"]?(\d+)[\'"]?',
            r'A\.患者年龄\s*([><=]+)\s*[\'"]?(\d+)[\'"]?',
            r'年龄\s*([><=]+)\s*(\d+)',
        ]
        
        # 性别条件模式
        self.gender_patterns = [
            r'患者性别\s*=\s*[\'"]([男女])[\'"]',
            r'A\.患者性别\s*=\s*[\'"]([男女])[\'"]',
            r'性别\s*=\s*[\'"]([男女])[\'"]',
        ]
        
        # 诊断条件模式
        self.diagnosis_patterns = [
            r'COALESCE\(A\.出院诊断名称,\s*[\'"][\'"]?\)\s*ILIKE\s+ANY\s*\(\s*ARRAY\[(.*?)\]\)',
            r'COALESCE\(A\.入院诊断名称,\s*[\'"][\'"]?\)\s*ILIKE\s+ANY\s*\(\s*ARRAY\[(.*?)\]\)',
            r'出院诊断名称.*?ILIKE\s+ANY\s*\(\s*ARRAY\[(.*?)\]\)',
            r'入院诊断名称.*?ILIKE\s+ANY\s*\(\s*ARRAY\[(.*?)\]\)',
        ]
        
        # 科室条件模式
        self.department_patterns = [
            r'入院科室\s*=\s*[\'"]([^\'\"]+)[\'"]',
            r'出院科室\s*=\s*[\'"]([^\'\"]+)[\'"]',
            r'开单科室名称\s*=\s*[\'"]([^\'\"]+)[\'"]',
            r'执行科室名称\s*=\s*[\'"]([^\'\"]+)[\'"]',
        ]
        
        # 数量阈值模式
        self.quantity_patterns = [
            r'SUM\(数量\)\s*>\s*([\d.]+)',
            r'数量\s*>\s*([\d.]+)',
            r'数量\s*>=\s*([\d.]+)',
        ]
    
    def extract_conditions(self, sql_content: str) -> RuleConditions:
        """提取规则条件"""
        conditions = RuleConditions()
        
        # 提取年龄条件
        conditions.age_range = self._extract_age_conditions(sql_content)
        
        # 提取性别条件
        conditions.gender = self._extract_gender_condition(sql_content)
        
        # 提取诊断条件
        include_diag, exclude_diag = self._extract_diagnosis_conditions(sql_content)
        conditions.include_diagnoses = include_diag
        conditions.exclude_diagnoses = exclude_diag
        
        # 提取科室条件
        conditions.include_departments = self._extract_department_conditions(sql_content)
        
        # 提取数量阈值
        conditions.quantity_threshold = self._extract_quantity_threshold(sql_content)
        
        # 提取时间条件
        conditions.time_conditions = self._extract_time_conditions(sql_content)
        
        # 提取其他条件
        conditions.other_conditions = self._extract_other_conditions(sql_content)
        
        return conditions
    
    def _extract_age_conditions(self, sql_content: str) -> Optional[AgeRange]:
        """提取年龄条件"""
        age_range = AgeRange()
        found_conditions = []
        
        for pattern in self.age_patterns:
            matches = re.finditer(pattern, sql_content, re.IGNORECASE)
            for match in matches:
                operator = match.group(1).strip()
                age_value = int(match.group(2))
                found_conditions.append((operator, age_value))
        
        # 解析年龄条件
        for operator, age_value in found_conditions:
            if operator in ['>', '>=']:
                age_range.min_age = age_value if operator == '>' else age_value
            elif operator in ['<', '<=']:
                age_range.max_age = age_value if operator == '<' else age_value
            elif operator == '=':
                age_range.min_age = age_value
                age_range.max_age = age_value
        
        # 如果没有找到任何年龄条件，返回None
        if age_range.min_age is None and age_range.max_age is None:
            return None
        
        return age_range
    
    def _extract_gender_condition(self, sql_content: str) -> Optional[str]:
        """提取性别条件"""
        for pattern in self.gender_patterns:
            match = re.search(pattern, sql_content, re.IGNORECASE)
            if match:
                return match.group(1)
        return None
    
    def _extract_diagnosis_conditions(self, sql_content: str) -> Tuple[List[str], List[str]]:
        """提取诊断条件"""
        include_diagnoses = []
        exclude_diagnoses = []
        
        # 查找所有诊断条件
        for pattern in self.diagnosis_patterns:
            matches = re.finditer(pattern, sql_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                array_content = match.group(1)
                diagnoses = self._parse_array_content(array_content)
                
                # 判断是包含还是排除条件
                full_match = match.group(0)
                if 'not (' in sql_content[:match.start()].lower()[-20:]:
                    exclude_diagnoses.extend(diagnoses)
                else:
                    include_diagnoses.extend(diagnoses)
        
        return include_diagnoses, exclude_diagnoses
    
    def _extract_department_conditions(self, sql_content: str) -> List[str]:
        """提取科室条件"""
        departments = []
        
        for pattern in self.department_patterns:
            matches = re.finditer(pattern, sql_content, re.IGNORECASE)
            for match in matches:
                department = match.group(1).strip()
                if department not in departments:
                    departments.append(department)
        
        return departments
    
    def _extract_quantity_threshold(self, sql_content: str) -> Optional[float]:
        """提取数量阈值"""
        for pattern in self.quantity_patterns:
            match = re.search(pattern, sql_content, re.IGNORECASE)
            if match:
                return float(match.group(1))
        return None
    
    def _extract_time_conditions(self, sql_content: str) -> List[str]:
        """提取时间条件"""
        time_conditions = []
        
        # 时间格式化条件
        time_format_pattern = r"to_char\([^)]*,\s*['\"]([^'\"]+)['\"]\)"
        matches = re.finditer(time_format_pattern, sql_content, re.IGNORECASE)
        for match in matches:
            time_format = match.group(1)
            time_conditions.append(f"时间格式: {time_format}")
        
        # 时间范围条件
        time_range_patterns = [
            r'项目使用日期.*?BETWEEN',
            r'住院期间',
            r'同时段',
            r'同一.*?时间',
        ]
        
        for pattern in time_range_patterns:
            if re.search(pattern, sql_content, re.IGNORECASE):
                time_conditions.append(f"时间限制: {pattern}")
        
        return time_conditions
    
    def _extract_other_conditions(self, sql_content: str) -> List[str]:
        """提取其他业务条件"""
        other_conditions = []
        
        # 医保项目名称条件
        item_pattern = r'医保项目名称\s+IN\s*\((.*?)\)'
        matches = re.finditer(item_pattern, sql_content, re.IGNORECASE | re.DOTALL)
        for match in matches:
            items = self._parse_array_content(match.group(1))
            if items:
                other_conditions.append(f"医保项目: {', '.join(items[:3])}{'...' if len(items) > 3 else ''}")
        
        # 特殊业务逻辑
        special_patterns = [
            (r'无指征', "无指征使用"),
            (r'常规检查', "作为常规检查"),
            (r'普遍.*?检测', "普遍检测"),
            (r'超说明书', "超说明书使用"),
            (r'串换', "项目串换"),
            (r'分解.*?收费', "分解收费"),
        ]
        
        for pattern, description in special_patterns:
            if re.search(pattern, sql_content, re.IGNORECASE):
                other_conditions.append(description)
        
        return other_conditions
    
    def _parse_array_content(self, array_content: str) -> List[str]:
        """解析数组内容"""
        if not array_content:
            return []
        
        # 移除多余的空格和换行
        array_content = re.sub(r'\s+', ' ', array_content.strip())
        
        # 提取引号内的内容
        items = []
        pattern = r"['\"]([^'\"]*)['\"]"
        matches = re.finditer(pattern, array_content)
        
        for match in matches:
            item = match.group(1).strip()
            if item and item not in items:
                # 清理通配符
                item = item.replace('%', '').strip()
                if item:
                    items.append(item)
        
        return items
    
    def extract_medical_items(self, sql_content: str) -> List[str]:
        """专门提取医保项目名称"""
        items = []
        
        # 从WHERE子句中提取
        patterns = [
            r'医保项目名称\s+IN\s*\((.*?)\)',
            r'B\.医保项目名称\s+IN\s*\((.*?)\)',
            r"医保项目名称\s*=\s*['\"]([^'\"]+)['\"]",
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, sql_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                if 'IN' in pattern:
                    # 处理IN子句
                    array_items = self._parse_array_content(match.group(1))
                    items.extend(array_items)
                else:
                    # 处理单个项目
                    item = match.group(1).strip()
                    if item not in items:
                        items.append(item)
        
        return items
