"""
数据模型定义
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import List, Optional, Union


class RuleType(Enum):
    """规则类型枚举"""
    DUPLICATE_BILLING = "重复收费"
    EXCESSIVE_USAGE = "超量使用"
    CASE_EXTRACTION = "病例提取"
    OVERCHARGE = "超标准收费"
    INAPPROPRIATE_USAGE = "不当使用"
    UNKNOWN = "未知类型"

    def __lt__(self, other):
        """支持排序比较"""
        if not isinstance(other, RuleType):
            return NotImplemented
        return self.value < other.value


@dataclass
class AgeRange:
    """年龄范围"""
    min_age: Optional[int] = None
    max_age: Optional[int] = None
    
    def __str__(self):
        if self.min_age is not None and self.max_age is not None:
            return f"{self.min_age}-{self.max_age}岁"
        elif self.min_age is not None:
            return f"≥{self.min_age}岁"
        elif self.max_age is not None:
            return f"≤{self.max_age}岁"
        return "无年龄限制"


@dataclass
class RuleConditions:
    """规则条件"""
    age_range: Optional[AgeRange] = None
    gender: Optional[str] = None
    include_diagnoses: List[str] = field(default_factory=list)
    exclude_diagnoses: List[str] = field(default_factory=list)
    include_departments: List[str] = field(default_factory=list)
    exclude_departments: List[str] = field(default_factory=list)
    quantity_threshold: Optional[float] = None
    time_conditions: List[str] = field(default_factory=list)
    other_conditions: List[str] = field(default_factory=list)


@dataclass
class RuleInfo:
    """规则信息数据结构"""
    rule_name: str = ""
    rule_type: RuleType = RuleType.UNKNOWN
    city: str = ""
    source: str = ""
    behavior: str = ""
    medical_items: List[str] = field(default_factory=list)
    violation_items: List[str] = field(default_factory=list)
    conditions: RuleConditions = field(default_factory=RuleConditions)
    sql_content: str = ""
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "rule_name": str(self.rule_name),
            "rule_type": str(self.rule_type.value),
            "city": str(self.city),
            "source": str(self.source),
            "behavior": str(self.behavior),
            "medical_items": list(self.medical_items),
            "violation_items": list(self.violation_items),
            "conditions": {
                "age_range": str(self.conditions.age_range) if self.conditions.age_range else None,
                "gender": str(self.conditions.gender) if self.conditions.gender else None,
                "include_diagnoses": list(self.conditions.include_diagnoses),
                "exclude_diagnoses": list(self.conditions.exclude_diagnoses),
                "include_departments": list(self.conditions.include_departments),
                "exclude_departments": list(self.conditions.exclude_departments),
                "quantity_threshold": float(self.conditions.quantity_threshold) if self.conditions.quantity_threshold is not None else None,
                "time_conditions": list(self.conditions.time_conditions),
                "other_conditions": list(self.conditions.other_conditions)
            },
            "sql_content": str(self.sql_content)
        }


@dataclass
class SQLToken:
    """SQL词法单元"""
    type: str
    value: str
    position: int
    
    
class TokenType:
    """词法单元类型常量"""
    KEYWORD = "KEYWORD"
    IDENTIFIER = "IDENTIFIER"
    STRING = "STRING"
    NUMBER = "NUMBER"
    OPERATOR = "OPERATOR"
    PUNCTUATION = "PUNCTUATION"
    WHITESPACE = "WHITESPACE"
    COMMENT = "COMMENT"
    EOF = "EOF"
