
================================================================================
功能按钮深度测试报告
================================================================================
测试时间: 2025-06-09 23:16:37
测试目标: 飞检规则知识库系统 - 功能按钮深度测试

测试统计:
- 总测试数: 12
- 通过: 12 ✅
- 失败: 0 ❌  
- 警告: 0 ⚠️
- 成功率: 100.0%

详细结果:
✅ 新增规则-获取下一个ID: 下一个ID: 3228
✅ 搜索功能-场景1: 搜索条件 {'ruleName': '超', 'expected_min': 10} 返回 329 条结果
✅ 搜索功能-场景2: 搜索条件 {'ruleSource': '国家', 'expected_min': 5} 返回 1386 条结果
✅ 搜索功能-场景3: 搜索条件 {'city': '北京', 'expected_min': 5} 返回 314 条结果
✅ 导入功能-API可用性: 导入API正常响应错误请求
✅ 编辑功能-获取规则详情: 成功获取规则 3226 的详情
✅ SQL生成器-搜索按钮: 搜索功能正常，返回 563 条规则
✅ SQL生成器-生成SQL按钮: SQL生成成功，生成了 7826 字符的SQL
✅ SQL生成器-执行SQL按钮: SQL执行API存在（状态码: 404）
✅ 系统规则-导出SQL按钮: 导出功能正常，返回ZIP文件
✅ 系统规则-导入PG按钮: 导入PG功能API正常
✅ 系统规则-筛选功能: 筛选功能正常，可用筛选项: ['cities', 'rule_sources', 'rule_types']

================================================================================
