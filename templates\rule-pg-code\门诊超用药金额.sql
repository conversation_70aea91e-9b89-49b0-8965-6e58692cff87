WITH tab1 AS (
  SELECT
    b.结算单据号,
    b.患者姓名,
    b.患者社会保障号码,
    to_char(b.项目使用日期, 'YYYY-MM') AS 使用月份,
    SUM(b.金额) AS 月用药金额
  FROM
    医保门诊结算明细 b
  WHERE
    b.医保项目编码 in ({医保编码1})
    AND b.费用类别 = '药品'
  GROUP BY
    b.结算单据号,
    b.患者姓名,
    b.患者社会保障号码,
    to_char(b.项目使用日期, 'YYYY-MM')
  HAVING
    SUM(b.金额) > {违规金额}
)
SELECT
  a.医疗机构编码,
  a.医疗机构名称,
  a.结算日期,
  a.个人编码,
  a.患者社会保障号码,
  a.身份证号,
  a.患者姓名,
  a.患者性别,
  a.患者年龄,
  a.险种类型,
  b.就诊科室,
  b.诊断名称,
  b.医师姓名,
  b.项目使用日期,
  t.使用月份,
  b.医院项目编码,
  b.医院项目名称,
  b.医保项目编码,
  b.医保项目名称,
  b.费用类别,
  b.支付类别,
  b.规格,
  b.单价,
  b.报销比例,
  b.自付比例,
  SUM(b.数量) AS 使用数量,
  SUM(b.金额) AS 使用金额,
  t.月用药金额,
  t.月用药金额 - {违规金额} AS 超出金额
FROM
  医保门诊结算明细 b
  JOIN tab1 t ON b.患者社会保障号码 = t.患者社会保障号码 
    AND to_char(b.项目使用日期, 'YYYY-MM') = t.使用月份
  JOIN 医保门诊结算主单 a ON a.结算单据号 = b.结算单据号
WHERE
  b.医保项目编码 in ({医保编码1})
  AND b.费用类别 = '药品'
  AND NOT b.诊断名称 ~* '({排除诊断})'
  AND NOT b.就诊科室 ~* '({排除科室})'
GROUP BY
  a.医疗机构编码,
  a.医疗机构名称,
  a.结算日期,
  a.个人编码,
  a.患者社会保障号码,
  a.身份证号,
  a.患者姓名,
  a.患者性别,
  a.患者年龄,
  a.险种类型,
  b.就诊科室,
  b.诊断名称,
  b.医师姓名,
  b.项目使用日期,
  t.使用月份,
  b.医院项目编码,
  b.医院项目名称,
  b.医保项目编码,
  b.医保项目名称,
  b.费用类别,
  b.支付类别,
  b.规格,
  b.单价,
  b.报销比例,
  b.自付比例,
  t.月用药金额
ORDER BY
  a.患者姓名,
  b.项目使用日期; 