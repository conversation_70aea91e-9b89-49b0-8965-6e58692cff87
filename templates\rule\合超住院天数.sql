WITH tab1 AS (
    SELECT 
        结算单据号, 
        医保项目编码, 
        医保项目名称,
        医院项目编码,
        医院项目名称,
        报销比例,
        单价,
        规格,
        费用类别,
        支付类别,
        开单科室名称,
        执行科室名称,
        开单医师姓名,
        项目使用日期,
        结算日期,
        自付比例,
        SUM(数量) AS 使用数量
    FROM 医保住院结算明细
    WHERE 医保项目名称 IN  ({医保名称1})
    GROUP BY 
        结算单据号, 
        医保项目编码, 
        医保项目名称,
        医院项目编码,
        医院项目名称,
        报销比例,
        单价,
        规格,
        费用类别,
        支付类别,
        开单科室名称,
        执行科室名称,
        开单医师姓名,
        项目使用日期,
        结算日期,
        自付比例,
),
tab2 AS (
    SELECT 
        结算单据号, 
        SUM(数量) AS 数量2
    FROM 医保住院结算明细
    WHERE 医保项目名称 IN  ({医保名称1})
    GROUP BY 结算单据号
)
select
  A.病案号,
  A.结算单据号,
  A.医疗机构编码,
  A.医疗机构名称,
  A<PERSON>结算日期,
  A<PERSON>住院号,
  A.个人编码,
  A.患者社会保障号码,
  A.身份证号,
  A.险种类型,
  A.入院科室,
  A.出院科室,
  A.主诊医师姓名,
  A.患者姓名,
  A.患者年龄,
  A.异地标志,
  A.入院日期,
  A.出院日期,
 (a.出院日期 :: DATE) - (a.入院日期 :: DATE) as 住院天数,
  A.医疗总费用,
  A.基本统筹支付,
  A.个人自付,
  A.个人自费,
  A.符合基本医疗保险的费用,
  A.入院诊断编码,
  A.入院诊断名称,
  A.出院诊断编码,
  A.出院诊断名称,
  A.主手术及操作编码,
  A.主手术及操作名称,
  A.其他手术及操作编码,
  A.其他手术及操作名称,
  B.开单科室名称,
  B.执行科室名称,
  B.开单医师姓名,
  B.费用类别,
  B.项目使用日期,
  B.结算日期,
  B.医院项目编码,
  B.医院项目名称,
  B.医保项目编码,
  B.医保项目名称,
  B.规格,
  B.单价,
  B.支付类别,
  B.报销比例,
  B.自付比例,
  A.支付地点类别,
  --A.记账流水号,
  SUM(b.数量) AS 使用数量,
  SUM(b.金额) AS 使用金额,
  SUM(b.医保范围内金额) AS 医保范围内总金额,
FROM 医保住院结算主单 a
JOIN tab1 b ON a.结算单据号 = b.结算单据号
JOIN tab2 c ON a.结算单据号 = c.结算单据号
    AND c.数量2 > (a.出院日期::DATE) - (a.入院日期::DATE)+1
ORDER BY
  A.患者姓名,
  B.项目使用日期

