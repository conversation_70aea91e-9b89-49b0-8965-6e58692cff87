{"name": "minipass", "version": "5.0.0", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "files": ["index.d.ts", "index.js", "index.mjs"], "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}}