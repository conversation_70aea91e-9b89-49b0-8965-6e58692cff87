SELECT
/*+PARALLEL(8)*/
A.病案号,
A.结算单据号,
A.医疗机构编码,
A.医疗机构名称,
A.结算日期,
A.住院号,
A.个人编码,
A.患者社会保障号码,
A.身份证号,
A.险种类型,
A<PERSON>入院科室,
A.出院科室,
A.主诊医师姓名,
A.患者姓名,
A.患者性别,
A.患者出生日期,
A.患者年龄,
A.异地标志,
A.入院日期,
A.出院日期,
A.医疗总费用,
A.基本统筹支付,
A.个人自付,
A.个人自费,
A.符合基本医疗保险的费用,
A.入院诊断编码,
A.入院诊断名称,
A.出院诊断编码,
A.出院诊断名称,
A.主手术及操作编码,
A.主手术及操作名称,
A.其他手术及操作编码,
A.其他手术及操作名称,
B.费用类别,
B.医院项目编码,
B.医院项目名称,
B.医保项目编码,
B.医保项目名称,
B.支付类别,
B.报销比例,
B.自付比例,
B.支付地点类别,
B.规格,
B.单价,
B.项目使用日期 ,
SUM(B.数量),
SUM(B.金额),
SUM(B.医保范围内金额)                    
FROM
  ZZS_YY_ZDYFY_4NG.医保住院结算明细 B
JOIN
  ZZS_YY_ZDYFY_4NG.医保住院结算主单 A
ON
  A.结算单据号 = B.结算单据号
WHERE
  B.医院项目编码 IN ('F00000085492','80302353')
  AND TO_CHAR(B.项目使用日期,'YYYY-mm-dd')>='2024-02-25' 
GROUP BY 
A.病案号,
A.结算单据号,
A.医疗机构编码,
A.医疗机构名称,
A.结算日期,
A.住院号,
A.个人编码,
A.患者社会保障号码,
A.身份证号,
A.险种类型,
A.入院科室,
A.出院科室,
A.主诊医师姓名,
A.患者姓名,
A.患者性别,
A.患者出生日期,
A.患者年龄,
A.异地标志,
A.入院日期,
A.出院日期,
A.医疗总费用,
A.基本统筹支付,
A.个人自付,
A.个人自费,
A.符合基本医疗保险的费用,
A.入院诊断编码,
A.入院诊断名称,
A.出院诊断编码,
A.出院诊断名称,
A.主手术及操作编码,
A.主手术及操作名称,
A.其他手术及操作编码,
A.其他手术及操作名称,
B.费用类别,
B.医院项目编码,
B.医院项目名称,
B.医保项目编码,
B.医保项目名称,
B.支付类别,
B.报销比例,
B.自付比例,
B.支付地点类别,
B.规格,
B.单价,
B.项目使用日期 