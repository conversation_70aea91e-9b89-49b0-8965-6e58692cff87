#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库选择功能的脚本
"""

import urllib.request
import json
import time
from datetime import datetime

def test_sql_execution():
    """测试SQL执行功能的数据库选择"""
    base_url = "http://127.0.0.1:5001"
    
    print("=" * 60)
    print("测试SQL执行功能的数据库选择")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试用的SQL语句
    test_sqls = [
        {
            "name": "Oracle测试SQL",
            "sql": "SELECT 1 as test_column FROM dual",
            "database": "oracle"
        },
        {
            "name": "PostgreSQL测试SQL", 
            "sql": "SELECT 1 as test_column",
            "database": "pg"
        }
    ]
    
    for test_case in test_sqls:
        print(f"\n🔍 测试: {test_case['name']}")
        print(f"数据库: {test_case['database']}")
        print(f"SQL: {test_case['sql']}")
        
        try:
            test_data = {
                "sql": test_case['sql'],
                "database": test_case['database']
            }
            
            req_data = json.dumps(test_data).encode('utf-8')
            req = urllib.request.Request(
                f"{base_url}/api/rules/execute_sql",
                data=req_data,
                headers={'Content-Type': 'application/json'},
                method='POST'
            )
            
            with urllib.request.urlopen(req, timeout=15) as response:
                if response.getcode() == 200:
                    content = response.read().decode('utf-8')
                    data = json.loads(content)
                    
                    if data.get('success'):
                        print(f"✅ 执行成功!")
                        print(f"   数据库: {data.get('database', 'Unknown')}")
                        print(f"   返回行数: {data.get('affected_rows', 0)}")
                        print(f"   列名: {data.get('columns', [])}")
                        if data.get('data'):
                            print(f"   数据: {data['data'][:3]}...")  # 只显示前3行
                    else:
                        print(f"❌ 执行失败: {data.get('error', 'Unknown error')}")
                else:
                    print(f"❌ HTTP错误: {response.getcode()}")
                    
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
        
        time.sleep(1)  # 避免请求过快
    
    # 测试安全限制
    print(f"\n🔒 测试安全限制 (非SELECT语句)")
    try:
        test_data = {
            "sql": "INSERT INTO test_table VALUES (1)",
            "database": "pg"
        }
        
        req_data = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=10) as response:
            content = response.read().decode('utf-8')
            data = json.loads(content)
            
            if not data.get('success') and '只允许执行SELECT查询' in data.get('error', ''):
                print("✅ 安全限制正常工作 - 拒绝非SELECT语句")
            else:
                print("❌ 安全限制异常 - 应该拒绝非SELECT语句")
                
    except Exception as e:
        print(f"❌ 安全测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print("测试完成!")

if __name__ == "__main__":
    test_sql_execution()
