{"name": "treeverse", "version": "3.0.0", "description": "Walk any kind of tree structure depth- or breadth-first. Supports promises and advanced map-reduce operations with a very small API.", "author": "GitHub Inc.", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/npm/treeverse.git"}, "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "tap": {"100": true, "coverage-map": "test/coverage-map.js", "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "4.5.1", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "main": "lib/index.js", "keywords": ["tree", "traversal", "depth first search", "breadth first search"], "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.5.1"}}