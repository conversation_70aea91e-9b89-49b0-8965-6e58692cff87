#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试取消合并显示后的规则推荐效果
"""

import requests
import json
import sys
from collections import defaultdict

def test_no_merge_display():
    """测试取消合并显示的效果"""
    base_url = 'http://localhost:5001'
    
    print("=== 测试取消合并显示功能 ===")
    
    try:
        # 1. 获取医院列表
        print("1. 获取医院列表...")
        response = requests.get(f'{base_url}/api/hospitals', timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 获取医院列表失败: HTTP {response.status_code}")
            return False
            
        hospitals_data = response.json()
        if not hospitals_data.get('success') or not hospitals_data.get('hospitals'):
            print(f"❌ 医院列表API返回失败: {hospitals_data.get('error', '无数据')}")
            return False
            
        hospitals = hospitals_data['hospitals']
        print(f"✅ 成功获取 {len(hospitals)} 家医院")
        
        # 选择第一家医院进行测试
        test_hospital = hospitals[0]
        hospital_id = test_hospital['医院ID']
        hospital_name = test_hospital['医院名称']
        print(f"📋 测试医院: {hospital_name} (ID: {hospital_id})")
        
        # 2. 生成规则推荐
        print("\n2. 生成规则推荐...")
        response = requests.post(f'{base_url}/api/hospital-rules/generate', 
                               json={'hospital_id': hospital_id}, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ 生成规则推荐失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
        recommendations_data = response.json()
        if not recommendations_data.get('success'):
            print(f"❌ 规则推荐API返回失败: {recommendations_data.get('error', '无数据')}")
            return False
            
        recommendations = recommendations_data.get('recommendations', [])
        total_count = recommendations_data.get('total_count', 0)
        returned_count = recommendations_data.get('returned_count', 0)
        
        print(f"✅ 成功生成规则推荐")
        print(f"   总推荐数: {total_count}")
        print(f"   返回数: {returned_count}")
        print(f"   实际记录数: {len(recommendations)}")
        
        if not recommendations:
            print("⚠️  没有生成任何推荐，无法测试显示效果")
            return True
        
        # 3. 分析推荐结果的独立性
        print("\n3. 分析推荐结果...")
        
        # 按规则ID分组统计
        rule_id_groups = defaultdict(list)
        rule_name_groups = defaultdict(list)
        
        for rec in recommendations:
            rule_id = rec.get('规则ID')
            rule_name = rec.get('规则名称')
            city = rec.get('城市', '未知')
            match_item = rec.get('匹配项目', '未知')
            
            rule_id_groups[rule_id].append({
                'city': city,
                'match_item': match_item,
                'rule_name': rule_name
            })
            
            rule_name_groups[rule_name].append({
                'rule_id': rule_id,
                'city': city,
                'match_item': match_item
            })
        
        # 统计同规则ID的多条记录
        multi_record_rule_ids = {rule_id: records for rule_id, records in rule_id_groups.items() if len(records) > 1}
        
        print(f"   规则ID总数: {len(rule_id_groups)}")
        print(f"   有多条记录的规则ID数: {len(multi_record_rule_ids)}")
        
        if multi_record_rule_ids:
            print("\n   同规则ID的多条记录详情 (显示前5个):")
            for i, (rule_id, records) in enumerate(list(multi_record_rule_ids.items())[:5]):
                print(f"   规则ID {rule_id} ({len(records)} 条记录):")
                print(f"     规则名称: {records[0]['rule_name']}")
                for j, record in enumerate(records):
                    print(f"     记录 {j+1}: 城市={record['city']}, 匹配项目={record['match_item']}")
                print()
        
        # 统计同规则名称的多条记录
        multi_record_rule_names = {rule_name: records for rule_name, records in rule_name_groups.items() if len(records) > 1}
        
        print(f"   规则名称总数: {len(rule_name_groups)}")
        print(f"   有多条记录的规则名称数: {len(multi_record_rule_names)}")
        
        if multi_record_rule_names:
            print("\n   同规则名称的多条记录详情 (显示前3个):")
            for i, (rule_name, records) in enumerate(list(multi_record_rule_names.items())[:3]):
                print(f"   规则名称: {rule_name} ({len(records)} 条记录)")
                rule_ids = list(set(record['rule_id'] for record in records))
                print(f"     涉及规则ID: {rule_ids}")
                for j, record in enumerate(records):
                    print(f"     记录 {j+1}: 规则ID={record['rule_id']}, 城市={record['city']}, 匹配项目={record['match_item']}")
                print()
        
        # 4. 验证是否真正取消了合并
        print("4. 验证合并状态...")
        
        # 检查是否有合并标记
        merged_count = sum(1 for rec in recommendations if rec.get('合并规则数量', 1) > 1)
        
        if merged_count == 0:
            print("✅ 确认已取消合并显示，所有记录都是独立的")
        else:
            print(f"⚠️  仍有 {merged_count} 条记录显示为合并状态")
            
            # 显示仍然合并的记录
            print("   仍然合并的记录:")
            for rec in recommendations:
                if rec.get('合并规则数量', 1) > 1:
                    print(f"     规则名称: {rec.get('规则名称')}")
                    print(f"     合并数量: {rec.get('合并规则数量')}")
                    print(f"     城市: {rec.get('城市')}")
                    print()
        
        # 5. 测试结果总结
        print("\n=== 测试结果总结 ===")
        print(f"医院: {hospital_name}")
        print(f"推荐记录总数: {len(recommendations)}")
        print(f"独立规则ID数: {len(rule_id_groups)}")
        print(f"独立规则名称数: {len(rule_name_groups)}")
        print(f"多记录规则ID数: {len(multi_record_rule_ids)}")
        print(f"多记录规则名称数: {len(multi_record_rule_names)}")
        print(f"合并记录数: {merged_count}")
        
        # 判断测试结果
        if merged_count == 0 and len(recommendations) > len(rule_name_groups):
            print("✅ 取消合并显示功能测试成功！")
            print("   - 所有记录都是独立显示")
            print("   - 同规则ID/名称的不同记录都能看到")
            print("   - 没有不当的合并操作")
            return True
        elif merged_count == 0:
            print("✅ 基本功能正常，但可能没有足够的测试数据")
            return True
        else:
            print("❌ 仍存在合并显示问题")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == '__main__':
    print("开始测试取消合并显示功能...\n")
    
    success = test_no_merge_display()
    
    if success:
        print("\n🎉 测试通过！取消合并显示功能正常工作。")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，需要进一步检查。")
        sys.exit(1)
