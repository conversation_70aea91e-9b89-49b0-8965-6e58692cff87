"""
调试sqlglot转换问题
"""

import sqlglot
from sqlglot import expressions as exp

sql = """
WITH tab1 AS (
    SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
    FROM 医保住院结算明细 WHERE 医保项目名称 = 'A'
    INTERSECT
    SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
    FROM 医保住院结算明细 WHERE 医保项目名称 = 'B'
)
SELECT * FROM tab1
"""

print("原始SQL:")
print(sql)
print("\n" + "="*50)

# 使用不同方言解析
dialects = ["postgres", "oracle", "mysql", "standard"]

for dialect_name in dialects:
    print(f"\n使用 {dialect_name} 方言解析:")
    try:
        if dialect_name == "postgres":
            ast = sqlglot.parse_one(sql, dialect=sqlglot.dialects.Postgres)
        elif dialect_name == "oracle":
            ast = sqlglot.parse_one(sql, dialect=sqlglot.dialects.Oracle)
        elif dialect_name == "mysql":
            ast = sqlglot.parse_one(sql, dialect=sqlglot.dialects.MySQL)
        else:
            ast = sqlglot.parse_one(sql)
        
        print(f"  AST类型: {type(ast)}")
        print(f"  是否为WITH: {isinstance(ast, exp.With)}")
        print(f"  是否为SELECT: {isinstance(ast, exp.Select)}")
        
        # 查找WITH节点
        with_nodes = list(ast.find_all(exp.With))
        print(f"  找到的WITH节点: {len(with_nodes)}")
        
        # 查找INTERSECT节点
        intersect_nodes = list(ast.find_all(exp.Intersect))
        print(f"  找到的INTERSECT节点: {len(intersect_nodes)}")
        
        if intersect_nodes:
            print(f"  第一个INTERSECT: {intersect_nodes[0]}")
        
    except Exception as e:
        print(f"  解析失败: {e}")

# 尝试不指定方言
print(f"\n不指定方言解析:")
ast = sqlglot.parse_one(sql)
print(f"AST类型: {type(ast)}")
print(f"AST内容: {ast}")

# 查看AST的详细结构
print(f"\nAST详细结构:")
def print_ast_structure(node, indent=0):
    prefix = "  " * indent
    print(f"{prefix}{type(node).__name__}: {str(node)[:100]}...")
    
    if hasattr(node, 'expressions') and node.expressions:
        for i, expr in enumerate(node.expressions):
            print(f"{prefix}  expressions[{i}]:")
            print_ast_structure(expr, indent + 2)
    
    if hasattr(node, 'this') and node.this:
        print(f"{prefix}  this:")
        print_ast_structure(node.this, indent + 2)

print_ast_structure(ast)
