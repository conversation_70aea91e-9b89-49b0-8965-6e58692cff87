{"name": "npm-packlist", "version": "10.0.0", "description": "Get a list of the files to add from a folder into an npm package", "directories": {"test": "test"}, "main": "lib/index.js", "dependencies": {"ignore-walk": "^7.0.0"}, "author": "GitHub Inc.", "license": "ISC", "files": ["bin/", "lib/"], "devDependencies": {"@npmcli/arborist": "^8.0.0", "@npmcli/eslint-config": "^5.0.1", "@npmcli/template-oss": "4.23.4", "mutate-fs": "^2.1.1", "tap": "^16.0.1"}, "scripts": {"test": "tap", "posttest": "npm run lint", "snap": "tap", "postsnap": "npm run lintfix --", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lint": "npm run eslint", "lintfix": "npm run eslint -- --fix", "npmclilint": "npmcli-lint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "git+https://github.com/npm/npm-packlist.git"}, "tap": {"test-env": ["LC_ALL=sk"], "nyc-arg": ["--exclude", "tap-snapshots/**"], "files": ["test/*.js"]}, "engines": {"node": "^20.17.0 || >=22.9.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.4", "publish": true}}