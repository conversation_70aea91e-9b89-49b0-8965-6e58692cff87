#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试跨城市已采用规则的禁用功能
"""

import requests
import json
import sys
from collections import defaultdict

def test_cross_city_disable():
    """测试跨城市已采用规则的禁用功能"""
    base_url = 'http://localhost:5001'
    
    print("=== 测试跨城市已采用规则的禁用功能 ===")
    
    try:
        # 1. 获取医院列表
        print("1. 获取医院列表...")
        response = requests.get(f'{base_url}/api/hospitals', timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 获取医院列表失败: HTTP {response.status_code}")
            return False
            
        hospitals_data = response.json()
        if not hospitals_data.get('success') or not hospitals_data.get('hospitals'):
            print(f"❌ 医院列表API返回失败")
            return False
            
        hospitals = hospitals_data['hospitals']
        print(f"✅ 成功获取 {len(hospitals)} 家医院")
        
        # 按城市分组医院
        city_hospitals = defaultdict(list)
        for hospital in hospitals:
            city = hospital.get('所在城市', '未知')
            city_hospitals[city].append(hospital)
        
        print(f"   涉及城市: {list(city_hospitals.keys())}")
        
        # 2. 获取所有医院的已采用规则
        print("\n2. 获取所有医院的已采用规则...")
        all_adopted_rules = []
        adopted_rule_ids = set()
        
        for hospital in hospitals:
            hospital_id = hospital['医院ID']
            hospital_name = hospital['医院名称']
            city = hospital.get('所在城市', '未知')
            
            response = requests.get(f'{base_url}/api/hospital-rules/adopted/{hospital_id}', timeout=10)
            
            if response.status_code == 200:
                adopted_data = response.json()
                if adopted_data.get('success') and adopted_data.get('rules'):
                    rules = adopted_data['rules']
                    print(f"   {hospital_name} ({city}): {len(rules)} 条已采用规则")
                    
                    for rule in rules:
                        rule['医院ID'] = hospital_id
                        rule['医院名称'] = hospital_name
                        rule['医院城市'] = city
                        all_adopted_rules.append(rule)
                        adopted_rule_ids.add(rule.get('规则ID'))
                else:
                    print(f"   {hospital_name} ({city}): 0 条已采用规则")
            else:
                print(f"   ❌ {hospital_name} ({city}): 获取失败")
        
        print(f"\n   全局已采用规则总数: {len(all_adopted_rules)}")
        print(f"   全局已采用规则ID数: {len(adopted_rule_ids)}")
        
        if len(adopted_rule_ids) == 0:
            print("⚠️  没有已采用规则，无法测试跨城市禁用功能")
            return True
        
        # 显示一些已采用规则的示例
        print("\n   已采用规则示例 (前5条):")
        for i, rule in enumerate(all_adopted_rules[:5]):
            print(f"   规则 {i+1}:")
            print(f"     规则ID: {rule.get('规则ID')}")
            print(f"     规则名称: {rule.get('规则名称')}")
            print(f"     医院: {rule.get('医院名称')} ({rule.get('医院城市')})")
            print()
        
        # 3. 测试不同城市的医院生成推荐
        print("3. 测试不同城市医院的推荐生成...")
        
        test_results = []
        
        # 选择几家不同城市的医院进行测试
        test_hospitals = []
        tested_cities = set()
        
        for hospital in hospitals:
            city = hospital.get('所在城市', '未知')
            if city not in tested_cities and len(test_hospitals) < 3:
                test_hospitals.append(hospital)
                tested_cities.add(city)
        
        for hospital in test_hospitals:
            hospital_id = hospital['医院ID']
            hospital_name = hospital['医院名称']
            city = hospital.get('所在城市', '未知')
            
            print(f"\n   测试医院: {hospital_name} ({city})")
            
            # 生成推荐
            response = requests.post(f'{base_url}/api/hospital-rules/generate', 
                                   json={'hospital_id': hospital_id, 'limit': 100}, timeout=60)
            
            if response.status_code != 200:
                print(f"     ❌ 生成推荐失败: HTTP {response.status_code}")
                continue
                
            recommendations_data = response.json()
            if not recommendations_data.get('success'):
                print(f"     ❌ 推荐API返回失败: {recommendations_data.get('error', '无数据')}")
                continue
                
            recommendations = recommendations_data.get('recommendations', [])
            print(f"     ✅ 生成 {len(recommendations)} 条推荐")
            
            # 检查推荐中是否包含已采用的规则ID
            conflicting_rules = []
            for rec in recommendations:
                rule_id = rec.get('规则ID')
                if rule_id in adopted_rule_ids:
                    conflicting_rules.append(rec)
            
            if len(conflicting_rules) > 0:
                print(f"     ❌ 发现问题：推荐中包含 {len(conflicting_rules)} 条已采用规则ID的记录")
                print("     问题记录详情:")
                for i, rec in enumerate(conflicting_rules[:3]):
                    print(f"       记录 {i+1}:")
                    print(f"         规则ID: {rec.get('规则ID')}")
                    print(f"         规则名称: {rec.get('规则名称')}")
                    print(f"         状态: {rec.get('状态')}")
                    print(f"         城市: {rec.get('城市')}")
                    
                    # 找到这个规则ID被哪个医院采用了
                    adopted_by = [r for r in all_adopted_rules if r.get('规则ID') == rec.get('规则ID')]
                    if adopted_by:
                        print(f"         已被采用: {adopted_by[0].get('医院名称')} ({adopted_by[0].get('医院城市')})")
                    print()
                
                test_results.append({
                    'hospital': hospital_name,
                    'city': city,
                    'status': 'FAILED',
                    'conflicting_count': len(conflicting_rules)
                })
            else:
                print(f"     ✅ 没有发现问题，所有已采用规则ID都被正确排除")
                test_results.append({
                    'hospital': hospital_name,
                    'city': city,
                    'status': 'PASSED',
                    'conflicting_count': 0
                })
        
        # 4. 测试结果总结
        print("\n=== 测试结果总结 ===")
        print(f"全局已采用规则ID数: {len(adopted_rule_ids)}")
        print(f"测试医院数: {len(test_results)}")
        
        passed_count = sum(1 for r in test_results if r['status'] == 'PASSED')
        failed_count = sum(1 for r in test_results if r['status'] == 'FAILED')
        
        print(f"通过测试: {passed_count} 家医院")
        print(f"未通过测试: {failed_count} 家医院")
        
        if failed_count > 0:
            print("\n未通过测试的医院:")
            for result in test_results:
                if result['status'] == 'FAILED':
                    print(f"  - {result['hospital']} ({result['city']}): {result['conflicting_count']} 条冲突记录")
        
        # 判断测试结果
        if failed_count == 0:
            print("\n✅ 跨城市已采用规则禁用功能测试成功！")
            print("   - 所有已采用规则ID都被正确排除")
            print("   - 不同城市的医院无法选择已被其他医院采用的规则")
            return True
        else:
            print("\n❌ 发现问题，需要进一步修复")
            print(f"   - 有 {failed_count} 家医院仍可选择已采用的规则ID")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == '__main__':
    print("开始测试跨城市已采用规则的禁用功能...\n")
    
    success = test_cross_city_disable()
    
    if success:
        print("\n🎉 测试通过！跨城市已采用规则禁用功能正常工作。")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，需要进一步检查。")
        sys.exit(1)
