import os
import subprocess
from typing import Optional, List, Tuple
import sys
from datetime import datetime

class GitHelper:
    def __init__(self):
        self.check_git_installed()
    
    def check_git_installed(self) -> None:
        """检查是否安装了Git"""
        try:
            subprocess.run(['git', '--version'], check=True, capture_output=True)
        except subprocess.CalledProcessError:
            print("错误: 未检测到Git，请先安装Git")
            sys.exit(1)
        except FileNotFoundError:
            print("错误: Git未安装或不在系统路径中")
            sys.exit(1)

    def run_git_command(self, command: List[str]) -> Tuple[bool, str]:
        """执行Git命令并返回结果"""
        try:
            result = subprocess.run(
                ['git'] + command, 
                capture_output=True, 
                text=True,
                encoding='utf-8',  # 明确指定编码为 UTF-8
                errors='replace'   # 处理无法解码的字符
            )
            if result.returncode == 0:
                return True, result.stdout
            return False, result.stderr
        except Exception as e:
            return False, str(e)

    def init_repository(self) -> None:
        """初始化Git仓库"""
        success, message = self.run_git_command(['init'])
        if success:
            print("Git仓库初始化成功！")
        else:
            print(f"初始化失败: {message}")

    def configure_git(self) -> None:
        """配置Git用户信息"""
        name = 'zxl78530100'
        email = '<EMAIL>'
        
        success1, _ = self.run_git_command(['config', '--global', 'user.name', name])
        success2, _ = self.run_git_command(['config', '--global', 'user.email', email])
        
        if success1 and success2:
            print("Git配置成功！")
        else:
            print("Git配置失败，请重试")

    def show_status(self) -> None:
        """显示仓库状态"""
        success, message = self.run_git_command(['status'])
        print(message if success else f"获取状态失败: {message}")

    def add_files(self) -> None:
        """添加文件到暂存区"""
        print("\n选择添加方式:")
        print("1. 添加所有文件")
        print("2. 添加指定文件")
        choice = input("请选择 (1/2): ")
        
        if choice == '1':
            success, message = self.run_git_command(['add', '.'])
            print("所有文件已添加到暂存区" if success else f"添加失败: {message}")
        elif choice == '2':
            file_path = input("请输入要添加的文件路径: ")
            success, message = self.run_git_command(['add', file_path])
            print(f"文件 {file_path} 已添加到暂存区" if success else f"添加失败: {message}")

    def commit_changes(self) -> None:
        """提交更改"""
        message = input("请输入提交信息: ")
        success, output = self.run_git_command(['commit', '-m', message])
        print("更改已提交！" if success else f"提交失败: {output}")

    def create_branch(self) -> None:
        """创建新分支"""
        branch_name = input("请输入新分支名称: ")
        success, message = self.run_git_command(['branch', branch_name])
        if success:
            print(f"分支 {branch_name} 创建成功！")
            switch = input("是否切换到新分支？(y/n): ")
            if switch.lower() == 'y':
                self.switch_branch(branch_name)
        else:
            print(f"分支创建失败: {message}")

    def switch_branch(self, branch_name: Optional[str] = None) -> None:
        """切换分支"""
        if not branch_name:
            success, branches = self.run_git_command(['branch'])
            if success:
                print("\n可用分支:")
                print(branches)
                branch_name = input("请输入要切换的分支名称: ")
            
        success, message = self.run_git_command(['checkout', branch_name])
        print(f"已切换到分支 {branch_name}" if success else f"切换失败: {message}")

    def merge_branch(self) -> None:
        """合并分支"""
        success, branches = self.run_git_command(['branch'])
        if success:
            print("\n可用分支:")
            print(branches)
            branch_name = input("请输入要合并的分支名称: ")
            success, message = self.run_git_command(['merge', branch_name])
            print(f"分支 {branch_name} 已合并" if success else f"合并失败: {message}")

    def pull_changes(self) -> None:
        """拉取更改"""
        success, message = self.run_git_command(['pull'])
        print("更改已拉取" if success else f"拉取失败: {message}")

    def push_changes(self) -> None:
        """推送更改"""
        success, message = self.run_git_command(['push'])
        print("更改已推送" if success else f"推送失败: {message}")

    def show_log(self) -> None:
        """显示提交日志"""
        print("\n选择日志显示方式:")
        print("1. 简要日志")
        print("2. 详细日志")
        print("3. 图形化日志")
        choice = input("请选择 (1/2/3): ")
        
        if choice == '1':
            success, message = self.run_git_command(['log', '--oneline', '--no-decorate'])
        elif choice == '2':
            success, message = self.run_git_command(['log'])
        else:
            success, message = self.run_git_command(['log', '--graph', '--oneline', '--all'])
        
        print(message if success else f"获取日志失败: {message}")

    def show_menu(self) -> None:
        """显示主菜单"""
        menu = """
Git 操作助手
===========
1.  初始化仓库
2.  配置Git信息
3.  查看仓库状态
4.  添加文件
5.  提交更改
6.  创建分支
7.  切换分支
8.  合并分支
9.  拉取更改
10. 推送更改
11. 查看日志
0.  退出
"""
        print(menu)

    def backup_repository(self) -> None:
        """备份仓库"""
        backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        success, _ = self.run_git_command(['bundle', 'create', f"{backup_dir}/repo.bundle", '--all'])
        if success:
            print(f"仓库已备份到 {backup_dir}/repo.bundle")
        else:
            print("备份失败")

    def run(self) -> None:
        """运行Git助手"""
        while True:
            self.show_menu()
            choice = input("请选择操作 (0-11): ")
            
            if choice == '0':
                print("感谢使用！再见！")
                break
            elif choice == '1':
                self.init_repository()
            elif choice == '2':
                self.configure_git()
            elif choice == '3':
                self.show_status()
            elif choice == '4':
                self.add_files()
            elif choice == '5':
                self.commit_changes()
            elif choice == '6':
                self.create_branch()
            elif choice == '7':
                self.switch_branch()
            elif choice == '8':
                self.merge_branch()
            elif choice == '9':
                self.pull_changes()
            elif choice == '10':
                self.push_changes()
            elif choice == '11':
                self.show_log()
            else:
                print("无效的选择，请重试")
            
            input("\n按回车键继续...")

if __name__ == "__main__":
    helper = GitHelper()
    helper.run() 