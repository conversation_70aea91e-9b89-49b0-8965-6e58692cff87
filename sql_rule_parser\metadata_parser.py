"""
规则元数据解析器
解析SQL注释中的规则元数据信息
"""

import re
from typing import Dict, Optional, List
from .models import RuleInfo, RuleType


class MetadataParser:
    """规则元数据解析器"""
    
    def __init__(self):
        # 元数据字段映射
        self.field_patterns = {
            'rule_name': [
                r'规则名称[：:]\s*(.+)',
                r'-- 规则名称[：:]\s*(.+)',
            ],
            'city': [
                r'城市[：:]\s*(.+)',
                r'-- 城市[：:]\s*(.+)',
            ],
            'source': [
                r'规则来源[：:]\s*(.+)',
                r'-- 规则来源[：:]\s*(.+)',
            ],
            'behavior': [
                r'行为认定[：:]\s*(.+)',
                r'-- 行为认定[：:]\s*(.+)',
            ],
            'medical_items': [
                r'医保名称1[：:]\s*(.+)',
                r'-- 医保名称1[：:]\s*(.+)',
            ],
            'violation_items': [
                r'医保名称2\(违规项\)[：:]\s*(.+)',
                r'-- 医保名称2\(违规项\)[：:]\s*(.+)',
                r'医保名称2（违规项）[：:]\s*(.+)',
                r'-- 医保名称2（违规项）[：:]\s*(.+)',
            ]
        }
        
        # 行为认定到规则类型的映射
        self.behavior_to_type = {
            '重复收费': RuleType.DUPLICATE_BILLING,
            '超标准收费': RuleType.OVERCHARGE,
            '过度检查': RuleType.CASE_EXTRACTION,
            '不当使用': RuleType.INAPPROPRIATE_USAGE,
            '超量使用': RuleType.EXCESSIVE_USAGE,
        }
    
    def parse_metadata(self, sql_content: str) -> RuleInfo:
        """解析SQL内容中的元数据"""
        rule_info = RuleInfo()
        rule_info.sql_content = sql_content
        
        # 提取注释内容
        comments = self._extract_comments(sql_content)
        comment_text = '\n'.join(comments)
        
        # 解析各个字段
        rule_info.rule_name = self._extract_field(comment_text, 'rule_name')
        rule_info.city = self._extract_field(comment_text, 'city')
        rule_info.source = self._extract_field(comment_text, 'source')
        rule_info.behavior = self._extract_field(comment_text, 'behavior')
        
        # 解析医保项目名称
        medical_items_str = self._extract_field(comment_text, 'medical_items')
        if medical_items_str:
            rule_info.medical_items = self._parse_item_list(medical_items_str)
            
        # 解析违规项目名称
        violation_items_str = self._extract_field(comment_text, 'violation_items')
        if violation_items_str:
            rule_info.violation_items = self._parse_item_list(violation_items_str)
        
        # 根据行为认定推断规则类型
        if rule_info.behavior:
            rule_info.rule_type = self.behavior_to_type.get(rule_info.behavior, RuleType.UNKNOWN)
        
        # 如果从注释中无法获取规则名称，尝试从文件名推断
        if not rule_info.rule_name:
            rule_info.rule_name = self._extract_rule_name_from_filename(sql_content)
            
        return rule_info
    
    def _extract_comments(self, sql_content: str) -> List[str]:
        """提取SQL中的注释"""
        comments = []
        
        # 提取单行注释 (-- 开头)
        single_line_pattern = r'--\s*(.+)'
        for match in re.finditer(single_line_pattern, sql_content, re.MULTILINE):
            comments.append(match.group(1).strip())
        
        # 提取多行注释 (/* ... */)
        multi_line_pattern = r'/\*(.*?)\*/'
        for match in re.finditer(multi_line_pattern, sql_content, re.DOTALL):
            comment_lines = match.group(1).strip().split('\n')
            for line in comment_lines:
                line = line.strip()
                if line and not line.startswith('='):  # 跳过分隔线
                    comments.append(line)
        
        return comments
    
    def _extract_field(self, text: str, field_name: str) -> str:
        """从文本中提取指定字段的值"""
        patterns = self.field_patterns.get(field_name, [])
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                value = match.group(1).strip()
                # 清理值中的多余字符
                value = re.sub(r'\s+', ' ', value)  # 合并多个空格
                value = value.rstrip('。，,.')  # 移除末尾标点
                return value
        
        return ""
    
    def _parse_item_list(self, items_str: str) -> List[str]:
        """解析项目列表字符串"""
        if not items_str:
            return []
        
        # 按逗号、分号或中文逗号分割
        items = re.split(r'[,，;；]', items_str)
        
        # 清理每个项目名称
        cleaned_items = []
        for item in items:
            item = item.strip()
            if item:
                cleaned_items.append(item)
        
        return cleaned_items
    
    def _extract_rule_name_from_filename(self, sql_content: str) -> str:
        """从SQL内容或文件名中推断规则名称"""
        # 尝试从SQL内容的开头注释中提取
        lines = sql_content.split('\n')
        for line in lines[:10]:  # 只检查前10行
            line = line.strip()
            if line.startswith('--') and ('规则' in line or '检查' in line or '收费' in line):
                # 移除注释符号和序号
                rule_name = re.sub(r'^--\s*\d*[._-]*\s*', '', line)
                rule_name = rule_name.strip()
                if len(rule_name) > 5:  # 确保是有意义的规则名称
                    return rule_name
        
        return ""
    
    def extract_filename_info(self, filename: str) -> Dict[str, str]:
        """从文件名中提取信息"""
        info = {}
        
        # 提取序号
        seq_match = re.search(r'^(\d+)', filename)
        if seq_match:
            info['sequence'] = seq_match.group(1)
        
        # 提取规则类型标识
        if '重复收费' in filename:
            info['type_hint'] = '重复收费'
        elif '超每日数量' in filename or '超标准' in filename:
            info['type_hint'] = '超标准收费'
        elif '病例提取' in filename:
            info['type_hint'] = '病例提取'
        elif '限年龄' in filename:
            info['type_hint'] = '年龄限制'
        elif '超频次' in filename:
            info['type_hint'] = '超频次使用'
        
        # 提取城市信息
        city_match = re.search(r'([^_]+)_', filename)
        if city_match:
            info['city'] = city_match.group(1)
        
        return info
