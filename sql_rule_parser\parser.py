"""
SQL规则解析器主类
整合所有组件，提供统一的解析接口
"""

import os
import pandas as pd
from typing import List, Dict, Optional
from .models import RuleInfo, RuleType
from .lexer import SQLLexer
from .metadata_parser import MetadataParser
from .rule_classifier import RuleClassifier
from .condition_extractor import ConditionExtractor


class SQLRuleParser:
    """SQL规则解析器主类"""
    
    def __init__(self):
        self.lexer = SQLLexer()
        self.metadata_parser = MetadataParser()
        self.rule_classifier = RuleClassifier()
        self.condition_extractor = ConditionExtractor()
    
    def parse_file(self, file_path: str) -> RuleInfo:
        """解析SQL文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        return self.parse_content(sql_content, file_path)
    
    def parse_content(self, sql_content: str, file_path: str = "") -> RuleInfo:
        """解析SQL内容"""
        # 1. 解析元数据
        rule_info = self.metadata_parser.parse_metadata(sql_content)
        
        # 2. 词法分析
        tokens = self.lexer.tokenize(sql_content)
        
        # 3. 规则类型识别
        if rule_info.rule_type == RuleType.UNKNOWN:
            rule_info.rule_type = self.rule_classifier.classify_rule(sql_content, tokens)
        
        # 4. 条件参数提取
        rule_info.conditions = self.condition_extractor.extract_conditions(sql_content)
        
        # 5. 提取医保项目名称（如果元数据中没有）
        if not rule_info.medical_items:
            rule_info.medical_items = self.condition_extractor.extract_medical_items(sql_content)
        
        # 6. 从文件名补充信息
        if file_path:
            filename_info = self.metadata_parser.extract_filename_info(os.path.basename(file_path))
            if not rule_info.city and 'city' in filename_info:
                rule_info.city = filename_info['city']
            if rule_info.rule_type == RuleType.UNKNOWN and 'type_hint' in filename_info:
                rule_info.rule_type = self._map_type_hint(filename_info['type_hint'])
        
        return rule_info
    
    def parse_directory(self, directory_path: str) -> List[RuleInfo]:
        """解析目录中的所有SQL文件"""
        if not os.path.exists(directory_path):
            raise FileNotFoundError(f"目录不存在: {directory_path}")

        results = []

        for filename in os.listdir(directory_path):
            if filename.endswith(('.sql', '.txt')):
                file_path = os.path.join(directory_path, filename)
                try:
                    rule_info = self.parse_file(file_path)
                    rule_info.sql_content = filename  # 保存文件名用于标识
                    results.append(rule_info)
                except Exception as e:
                    print(f"解析文件 {filename} 时出错: {e}")
                    continue
            elif filename.endswith(('.xlsx', '.xls')):
                # 解析Excel文件
                file_path = os.path.join(directory_path, filename)
                try:
                    excel_rules = self.parse_excel_file(file_path)
                    results.extend(excel_rules)
                except Exception as e:
                    print(f"解析Excel文件 {filename} 时出错: {e}")
                    continue

        return results

    def parse_excel_file(self, file_path: str) -> List[RuleInfo]:
        """解析Excel文件中的SQL规则"""
        try:
            df = pd.read_excel(file_path)
            results = []

            # 查找包含SQL的列
            sql_column = None
            for col in df.columns:
                if 'sql' in col.lower() or 'SQL' in col:
                    sql_column = col
                    break

            if sql_column is None:
                print(f"Excel文件 {file_path} 中未找到SQL列")
                return results

            # 查找其他相关列
            name_column = None
            category_column = None
            source_column = None

            for col in df.columns:
                if '规则名称' in col or '名称' in col:
                    name_column = col
                elif '分类' in col or '类型' in col:
                    category_column = col
                elif '来源' in col or '源' in col:
                    source_column = col

            # 解析每一行
            for index, row in df.iterrows():
                sql_content = row[sql_column]

                if pd.isna(sql_content) or not str(sql_content).strip():
                    continue

                try:
                    # 解析SQL内容
                    rule_info = self.parse_content(str(sql_content))

                    # 从Excel中补充元数据
                    if name_column and pd.notna(row[name_column]):
                        rule_info.rule_name = str(row[name_column])

                    if category_column and pd.notna(row[category_column]):
                        rule_info.behavior = str(row[category_column])
                        # 根据分类更新规则类型
                        if '重复收费' in rule_info.behavior:
                            rule_info.rule_type = RuleType.DUPLICATE_BILLING
                        elif '超标准' in rule_info.behavior or '超量' in rule_info.behavior:
                            rule_info.rule_type = RuleType.EXCESSIVE_USAGE

                    if source_column and pd.notna(row[source_column]):
                        rule_info.source = str(row[source_column])

                    # 标识来源
                    rule_info.sql_content = f"Excel行{index+1}: {os.path.basename(file_path)}"

                    results.append(rule_info)

                except Exception as e:
                    print(f"解析Excel第{index+1}行SQL时出错: {e}")
                    continue

            return results

        except Exception as e:
            print(f"读取Excel文件 {file_path} 时出错: {e}")
            return []

    def analyze_rule_patterns(self, rules: List[RuleInfo]) -> Dict[str, any]:
        """分析规则模式统计"""
        stats = {
            'total_rules': len(rules),
            'rule_types': {},
            'cities': {},
            'behaviors': {},
            'common_conditions': {
                'age_restrictions': 0,
                'gender_restrictions': 0,
                'diagnosis_conditions': 0,
                'department_conditions': 0,
                'quantity_thresholds': 0
            },
            'medical_items_count': 0
        }
        
        for rule in rules:
            # 统计规则类型
            rule_type = rule.rule_type.value
            stats['rule_types'][rule_type] = stats['rule_types'].get(rule_type, 0) + 1
            
            # 统计城市
            if rule.city:
                stats['cities'][rule.city] = stats['cities'].get(rule.city, 0) + 1
            
            # 统计行为认定
            if rule.behavior:
                stats['behaviors'][rule.behavior] = stats['behaviors'].get(rule.behavior, 0) + 1
            
            # 统计条件类型
            if rule.conditions.age_range:
                stats['common_conditions']['age_restrictions'] += 1
            if rule.conditions.gender:
                stats['common_conditions']['gender_restrictions'] += 1
            if rule.conditions.include_diagnoses or rule.conditions.exclude_diagnoses:
                stats['common_conditions']['diagnosis_conditions'] += 1
            if rule.conditions.include_departments:
                stats['common_conditions']['department_conditions'] += 1
            if rule.conditions.quantity_threshold:
                stats['common_conditions']['quantity_thresholds'] += 1
            
            # 统计医保项目
            stats['medical_items_count'] += len(rule.medical_items)
        
        return stats
    
    def export_to_structured_data(self, rules: List[RuleInfo]) -> List[Dict]:
        """导出为结构化数据"""
        return [rule.to_dict() for rule in rules]

    def export_to_excel(self, rules: List[RuleInfo], output_path: str):
        """导出规则到Excel文件"""
        data = []

        for rule in rules:
            row = {
                '规则名称': rule.rule_name,
                '规则类型': rule.rule_type.value,
                '城市': rule.city,
                '规则来源': rule.source,
                '行为认定': rule.behavior,
                '医保项目': ', '.join(rule.medical_items),
                '违规项目': ', '.join(rule.violation_items),
                '年龄限制': str(rule.conditions.age_range) if rule.conditions.age_range else '',
                '性别限制': rule.conditions.gender or '',
                '包含诊断': ', '.join(rule.conditions.include_diagnoses),
                '排除诊断': ', '.join(rule.conditions.exclude_diagnoses),
                '包含科室': ', '.join(rule.conditions.include_departments),
                '排除科室': ', '.join(rule.conditions.exclude_departments),
                '数量阈值': rule.conditions.quantity_threshold or '',
                '时间条件': ', '.join(rule.conditions.time_conditions),
                '其他条件': ', '.join(rule.conditions.other_conditions),
                '文件来源': rule.sql_content
            }
            data.append(row)

        df = pd.DataFrame(data)
        df.to_excel(output_path, index=False, engine='openpyxl')
        print(f"规则数据已导出到: {output_path}")
    
    def get_classification_confidence(self, sql_content: str) -> Dict[str, float]:
        """获取各规则类型的分类置信度"""
        confidences = {}

        for rule_type in RuleType:
            if rule_type == RuleType.UNKNOWN:
                continue
            confidence = self.rule_classifier.get_confidence_score(sql_content, rule_type)
            confidences[rule_type.value] = confidence

        return confidences
    
    def explain_rule_classification(self, sql_content: str) -> Dict[str, any]:
        """解释规则分类结果"""
        # 分类规则
        predicted_type = self.rule_classifier.classify_rule(sql_content)
        
        # 获取置信度
        confidence = self.rule_classifier.get_confidence_score(sql_content, predicted_type)
        
        # 获取解释
        explanations = self.rule_classifier.explain_classification(sql_content, predicted_type)
        
        return {
            'predicted_type': predicted_type.value,
            'confidence': confidence,
            'explanations': explanations,
            'all_confidences': self.get_classification_confidence(sql_content)
        }
    
    def _map_type_hint(self, type_hint: str) -> RuleType:
        """将类型提示映射为规则类型"""
        mapping = {
            '重复收费': RuleType.DUPLICATE_BILLING,
            '超标准收费': RuleType.OVERCHARGE,
            '病例提取': RuleType.CASE_EXTRACTION,
            '年龄限制': RuleType.INAPPROPRIATE_USAGE,
            '超频次使用': RuleType.EXCESSIVE_USAGE,
        }
        return mapping.get(type_hint, RuleType.UNKNOWN)
    
    def validate_rule_completeness(self, rule_info: RuleInfo) -> Dict[str, bool]:
        """验证规则信息完整性"""
        completeness = {
            'has_rule_name': bool(rule_info.rule_name),
            'has_rule_type': rule_info.rule_type != RuleType.UNKNOWN,
            'has_medical_items': bool(rule_info.medical_items),
            'has_conditions': any([
                rule_info.conditions.age_range,
                rule_info.conditions.gender,
                rule_info.conditions.include_diagnoses,
                rule_info.conditions.exclude_diagnoses,
                rule_info.conditions.quantity_threshold
            ]),
            'has_metadata': bool(rule_info.city or rule_info.source or rule_info.behavior)
        }
        
        completeness['overall_complete'] = all(completeness.values())
        return completeness
