{"name": "promise-call-limit", "version": "3.0.2", "files": ["dist"], "description": "Call an array of promise-returning functions, restricting concurrency to a specified limit.", "repository": {"type": "git", "url": "git+https://github.com/isaacs/promise-call-limit"}, "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --log-level warn --cache"}, "devDependencies": {"prettier": "^3.3.3", "tap": "^21.0.1", "tshy": "^3.0.2", "typedoc": "^0.26.6"}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "module": "./dist/esm/index.js"}