# SQL生成器

这是一个基于Flask的Web应用程序，用于根据模板和Excel文件生成SQL语句。

## 功能

- 上传SQL模板文件
- 根据模板生成单个SQL语句
- 批量生成SQL语句（合并或分离）
- 下载生成的SQL文件

## 安装

1. 克隆仓库：
   ```
   git clone https://github.com/your-username/sql-generator.git
   cd sql-generator
   ```

2. 创建虚拟环境并激活：
   ```
   python -m venv venv
   source venv/bin/activate  # 在Windows上使用 venv\Scripts\activate
   ```

3. 安装依赖：
   ```
   pip install -r requirements.txt
   ```

## 使用方法

1. 启动应用：
   ```
   python app.py
   ```

2. 在浏览器中打开 `http://localhost:5000`

3. 上传SQL模板：
   - 点击"选择文件"按钮，选择一个.sql文件
   - 点击"上传模板"按钮

4. 生成单个SQL：
   - 从下拉菜单中选择一个模板
   - 填写所有必要的变量
   - 点击"生成SQL"按钮

5. 批量生成SQL：
   - 从下拉菜单中选择一个模板
   - 准备一个Excel文件，其中包含所有必要的变量列
   - 选择Excel文件
   - 选择"批量生成SQL"（合并）或"批量生成单独SQL"（分离）
   - 点击相应的按钮

6. 下载生成的SQL：
   - 单个SQL：直接在页面上查看或复制
   - 批量SQL：点击"下载"链接下载ZIP文件

## 注意事项

- SQL模板文件应放在`templates`文件夹中
- 生成的SQL文件将保存在`output`文件夹中
- 确保Excel文件的列名与SQL模板中的变量名匹配
- 所有文件操作都使用UTF-8编码

## 贡献

如果您想为这个项目做出贡献，请遵循以下步骤：

1. Fork 该仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 将您的更改推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启一个 Pull Request

## 许可证

该项目使用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

您的姓名 - [@您的推特](https://twitter.com/您的推特)

项目链接: [https://github.com/您的用户名/sql-generator](https://github.com/您的用户名/sql-generator)
