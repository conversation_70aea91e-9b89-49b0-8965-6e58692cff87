"""
SQL规则解析器Web界面
使用Streamlit创建的前端操作页面
"""

import streamlit as st
import os
import pandas as pd
import json
from datetime import datetime
from sql_rule_parser import SQLRuleParser
import zipfile
import tempfile


def main():
    st.set_page_config(
        page_title="SQL规则解析器",
        page_icon="🔍",
        layout="wide"
    )
    
    st.title("🔍 SQL规则解析器")
    st.markdown("用于分析医保飞检规则的SQL语句并提取其业务逻辑")
    
    # 侧边栏
    st.sidebar.title("操作选项")
    
    # 创建解析器实例
    if 'parser' not in st.session_state:
        st.session_state.parser = SQLRuleParser()
    
    # 选择操作模式
    mode = st.sidebar.selectbox(
        "选择操作模式",
        ["文件上传解析", "目录批量解析", "单个SQL解析", "解析结果查看"]
    )
    
    if mode == "文件上传解析":
        file_upload_mode()
    elif mode == "目录批量解析":
        directory_batch_mode()
    elif mode == "单个SQL解析":
        single_sql_mode()
    elif mode == "解析结果查看":
        result_view_mode()


def file_upload_mode():
    """文件上传解析模式"""
    st.header("📁 文件上传解析")
    
    # 文件上传
    uploaded_files = st.file_uploader(
        "选择要解析的文件",
        type=['sql', 'txt', 'xlsx', 'xls'],
        accept_multiple_files=True,
        help="支持SQL文件(.sql, .txt)和Excel文件(.xlsx, .xls)"
    )
    
    if uploaded_files:
        st.write(f"已选择 {len(uploaded_files)} 个文件")
        
        # 显示文件列表
        for file in uploaded_files:
            st.write(f"- {file.name} ({file.size} bytes)")
        
        if st.button("开始解析", type="primary"):
            with st.spinner("正在解析文件..."):
                results = []
                
                # 创建临时目录
                with tempfile.TemporaryDirectory() as temp_dir:
                    for uploaded_file in uploaded_files:
                        # 保存上传的文件到临时目录
                        file_path = os.path.join(temp_dir, uploaded_file.name)
                        with open(file_path, 'wb') as f:
                            f.write(uploaded_file.getbuffer())
                        
                        try:
                            if uploaded_file.name.endswith(('.xlsx', '.xls')):
                                # 解析Excel文件
                                excel_rules = st.session_state.parser.parse_excel_file(file_path)
                                results.extend(excel_rules)
                            else:
                                # 解析SQL文件
                                rule_info = st.session_state.parser.parse_file(file_path)
                                rule_info.sql_content = uploaded_file.name
                                results.append(rule_info)
                        except Exception as e:
                            st.error(f"解析文件 {uploaded_file.name} 时出错: {e}")
                
                # 保存结果到session state
                st.session_state.parse_results = results
                
                # 显示解析结果
                display_parse_results(results)


def directory_batch_mode():
    """目录批量解析模式"""
    st.header("📂 目录批量解析")
    
    # 目录路径输入
    directory_path = st.text_input(
        "输入目录路径",
        value="郑州第一附属医院规则",
        help="输入包含SQL文件的目录路径"
    )
    
    if st.button("开始批量解析", type="primary"):
        if not os.path.exists(directory_path):
            st.error(f"目录不存在: {directory_path}")
            return
        
        with st.spinner("正在批量解析目录中的文件..."):
            try:
                results = st.session_state.parser.parse_directory(directory_path)
                st.session_state.parse_results = results
                display_parse_results(results)
            except Exception as e:
                st.error(f"批量解析时出错: {e}")


def single_sql_mode():
    """单个SQL解析模式"""
    st.header("📝 单个SQL解析")
    
    # SQL输入
    sql_content = st.text_area(
        "输入SQL内容",
        height=300,
        placeholder="请输入要解析的SQL语句..."
    )
    
    if st.button("解析SQL", type="primary") and sql_content.strip():
        with st.spinner("正在解析SQL..."):
            try:
                rule_info = st.session_state.parser.parse_content(sql_content)
                rule_info.sql_content = "手动输入"
                
                # 显示解析结果
                display_single_rule_result(rule_info)
                
                # 保存到session state
                if 'parse_results' not in st.session_state:
                    st.session_state.parse_results = []
                st.session_state.parse_results.append(rule_info)
                
            except Exception as e:
                st.error(f"解析SQL时出错: {e}")


def result_view_mode():
    """解析结果查看模式"""
    st.header("📊 解析结果查看")
    
    if 'parse_results' not in st.session_state or not st.session_state.parse_results:
        st.info("暂无解析结果，请先进行解析操作。")
        return
    
    results = st.session_state.parse_results
    
    # 统计信息
    st.subheader("📈 统计信息")
    stats = st.session_state.parser.analyze_rule_patterns(results)
    
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("总规则数", stats['total_rules'])
    with col2:
        st.metric("医保项目数", stats['medical_items_count'])
    with col3:
        st.metric("年龄限制规则", stats['common_conditions']['age_restrictions'])
    with col4:
        st.metric("诊断条件规则", stats['common_conditions']['diagnosis_conditions'])
    
    # 规则类型分布
    st.subheader("📊 规则类型分布")
    rule_type_df = pd.DataFrame(list(stats['rule_types'].items()), 
                               columns=['规则类型', '数量'])
    st.bar_chart(rule_type_df.set_index('规则类型'))
    
    # 详细结果表格
    st.subheader("📋 详细解析结果")
    display_results_table(results)
    
    # 导出功能
    st.subheader("💾 导出功能")
    export_results(results)


def display_parse_results(results):
    """显示解析结果"""
    if not results:
        st.warning("没有解析到任何规则")
        return
    
    st.success(f"成功解析 {len(results)} 个规则")
    
    # 统计信息
    stats = st.session_state.parser.analyze_rule_patterns(results)
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("总规则数", stats['total_rules'])
    with col2:
        st.metric("规则类型数", len(stats['rule_types']))
    with col3:
        st.metric("医保项目数", stats['medical_items_count'])
    
    # 规则类型分布
    st.write("**规则类型分布:**")
    for rule_type, count in stats['rule_types'].items():
        percentage = (count / stats['total_rules']) * 100
        st.write(f"- {rule_type}: {count} ({percentage:.1f}%)")
    
    # 显示前几个规则的详细信息
    st.subheader("解析结果预览")
    for i, rule in enumerate(results[:3]):
        with st.expander(f"规则 {i+1}: {rule.rule_name or '未命名规则'}"):
            display_single_rule_result(rule)


def display_single_rule_result(rule):
    """显示单个规则的解析结果"""
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**基本信息:**")
        st.write(f"- 规则名称: {rule.rule_name}")
        st.write(f"- 规则类型: {rule.rule_type.value}")
        st.write(f"- 城市: {rule.city}")
        st.write(f"- 行为认定: {rule.behavior}")
        st.write(f"- 规则来源: {rule.source}")
    
    with col2:
        st.write("**医保项目:**")
        if rule.medical_items:
            for item in rule.medical_items:
                st.write(f"- {item}")
        else:
            st.write("无")
        
        if rule.violation_items:
            st.write("**违规项目:**")
            for item in rule.violation_items:
                st.write(f"- {item}")
    
    # 条件信息
    if any([rule.conditions.age_range, rule.conditions.gender, 
            rule.conditions.include_diagnoses, rule.conditions.exclude_diagnoses,
            rule.conditions.quantity_threshold]):
        st.write("**规则条件:**")
        
        if rule.conditions.age_range:
            st.write(f"- 年龄限制: {rule.conditions.age_range}")
        if rule.conditions.gender:
            st.write(f"- 性别限制: {rule.conditions.gender}")
        if rule.conditions.quantity_threshold:
            st.write(f"- 数量阈值: {rule.conditions.quantity_threshold}")
        if rule.conditions.include_diagnoses:
            st.write(f"- 包含诊断: {', '.join(rule.conditions.include_diagnoses[:3])}{'...' if len(rule.conditions.include_diagnoses) > 3 else ''}")
        if rule.conditions.exclude_diagnoses:
            st.write(f"- 排除诊断: {', '.join(rule.conditions.exclude_diagnoses[:3])}{'...' if len(rule.conditions.exclude_diagnoses) > 3 else ''}")


def display_results_table(results):
    """显示结果表格"""
    # 准备表格数据
    table_data = []
    for rule in results:
        row = {
            '规则名称': rule.rule_name,
            '规则类型': rule.rule_type.value,
            '城市': rule.city,
            '行为认定': rule.behavior,
            '医保项目数': len(rule.medical_items),
            '年龄限制': str(rule.conditions.age_range) if rule.conditions.age_range else '',
            '性别限制': rule.conditions.gender or '',
            '数量阈值': rule.conditions.quantity_threshold or '',
            '文件来源': rule.sql_content
        }
        table_data.append(row)
    
    df = pd.DataFrame(table_data)
    st.dataframe(df, use_container_width=True)


def export_results(results):
    """导出结果"""
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("导出为Excel"):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"规则解析结果_{timestamp}.xlsx"
            
            try:
                st.session_state.parser.export_to_excel(results, output_path)
                st.success(f"已导出到: {output_path}")
                
                # 提供下载链接
                with open(output_path, 'rb') as f:
                    st.download_button(
                        label="下载Excel文件",
                        data=f.read(),
                        file_name=output_path,
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    )
            except Exception as e:
                st.error(f"导出Excel时出错: {e}")
    
    with col2:
        if st.button("导出为JSON"):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"规则解析结果_{timestamp}.json"
            
            try:
                structured_data = st.session_state.parser.export_to_structured_data(results)
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(structured_data, f, ensure_ascii=False, indent=2)
                
                st.success(f"已导出到: {output_path}")
                
                # 提供下载链接
                with open(output_path, 'r', encoding='utf-8') as f:
                    st.download_button(
                        label="下载JSON文件",
                        data=f.read(),
                        file_name=output_path,
                        mime="application/json"
                    )
            except Exception as e:
                st.error(f"导出JSON时出错: {e}")


if __name__ == "__main__":
    main()
