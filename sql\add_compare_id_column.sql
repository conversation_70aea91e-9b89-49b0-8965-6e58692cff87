-- 为医院适用规则表添加对照ID字段
-- 执行前请确保已备份数据

-- 1. 添加对照ID字段
ALTER TABLE 医院适用规则表 ADD 对照ID NUMBER;

-- 2. 添加注释
COMMENT ON COLUMN 医院适用规则表.对照ID IS '规则对照ID，用于系统规则筛选';

-- 3. 创建索引以提高查询性能
CREATE INDEX IDX_医院适用规则_对照ID ON 医院适用规则表(对照ID);

-- 4. 更新现有数据的对照ID（如果需要）
-- 这个脚本会根据规则ID和医院所在城市来查找对应的对照ID
UPDATE 医院适用规则表 h
SET 对照ID = (
    SELECT MIN(c.对照ID)
    FROM 规则医保编码对照 c
    JOIN 医院信息表 hos ON hos.医院ID = h.医院ID
    WHERE c.规则ID = h.规则ID
    AND (c.城市 = hos.所在城市 OR c.城市 IS NULL)
)
WHERE 对照ID IS NULL;

-- 5. 提交更改
COMMIT;

-- 6. 验证更新结果
SELECT 
    COUNT(*) as 总记录数,
    COUNT(对照ID) as 有对照ID的记录数,
    COUNT(*) - COUNT(对照ID) as 缺少对照ID的记录数
FROM 医院适用规则表;

-- 7. 显示一些示例数据
SELECT 
    适用ID,
    医院ID,
    规则ID,
    对照ID,
    状态,
    创建时间
FROM 医院适用规则表
WHERE ROWNUM <= 10
ORDER BY 创建时间 DESC;
