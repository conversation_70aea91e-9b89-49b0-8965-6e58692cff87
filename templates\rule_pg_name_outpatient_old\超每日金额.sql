  WITH TAB1 AS (
 SELECT 
  b.结算单据号,
  b.医保项目名称,
  to_char(项目使用日期,'yyyy-MM-dd') as 项目使用日期,
  sum(金额) as 使用金额
   FROM 医保门诊结算明细 B
  WHERE 医保项目名称 in ({医保名称1})
  GROUP BY b.结算单据号, b.医保项目名称,to_char(项目使用日期,'yyyy-MM-dd')
 HAVING sum(金额) > {违规金额}
)
SELECT
  A.门诊号,
  A.结算单据号,
  A.医疗机构编码,
  A.医疗机构名称,
  A.结算日期,
  A.个人编码,
  A.患者社会保障号码,
  A.险种类型,
  A.科室名称,
  A.医师名称,
  A.患者姓名,
  A.患者性别,
  A.患者出生日期,
  A.患者年龄,
  A.医疗类别,
  A.异地标志,
  A.医疗总费用,
  A.基本统筹支付,
  A.现金支付,
  A.个人账户支付,
  A.符合基本医疗保险的费用,
  A.诊断编码,
  A.诊断名称,
  B.开单科室名称,
  B.执行科室名称,
  B.开单医师姓名,
  B.费用类别,
  B.项目使用日期,
  B.医院项目编码,
  B.医院项目名称,
  B.医保项目编码,
  B.医保项目名称,
  B.规格,
  B.单价,
  B.支付类别,
  B.报销比例,
  B.自付比例,
  --A.记账流水号,
  SUM(b.数量) AS 使用数量,
  SUM(b.金额) AS 使用金额,
  SUM(b.医保范围内金额) AS 医保范围内总金额,
  (SUM(b.金额)  - {违规金额}) AS 超出金额
FROM
  医保门诊结算明细 b
  join tab1 on b.结算单据号 = tab1.结算单据号  and to_char(b.项目使用日期,'yyyy-MM-dd')=tab1.项目使用日期
 JOIN 医保门诊结算主单 a ON a.结算单据号 = b.结算单据号
 WHERE
 b.医保项目名称 in ({医保名称1})
 and not A.诊断名称 ~* '({排除诊断})'
  and not B.开单科室名称 ~* '({排除科室})'
 group by 
  A.门诊号,
  A.结算单据号,
  A.医疗机构编码,
  A.医疗机构名称,
  A.结算日期,
  A.个人编码,
  A.患者社会保障号码,
  A.险种类型,
  A.科室名称,
  A.医师名称,
  A.患者姓名,
  A.患者性别,
  A.患者出生日期,
  A.患者年龄,
  A.医疗类别,
  A.异地标志,
  A.医疗总费用,
  A.基本统筹支付,
  A.现金支付,
  A.个人账户支付,
  A.符合基本医疗保险的费用,
  A.诊断编码,
  A.诊断名称,
  B.开单科室名称,
  B.执行科室名称,
  B.开单医师姓名,
  B.费用类别,
  B.项目使用日期,
  B.医院项目编码,
  B.医院项目名称,
  B.医保项目编码,
  B.医保项目名称,
  B.规格,
  B.单价,
  B.支付类别,
  B.报销比例,
  B.自付比例
  --A.记账流水号,
ORDER BY
  A.患者姓名,
  B.项目使用日期