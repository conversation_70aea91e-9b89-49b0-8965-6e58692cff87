#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量复制规则功能 - 只复制医保名称，不复制医保编码
"""

import requests
import json
import time

# 测试配置
base_url = 'http://localhost:5001'

def test_batch_copy_medical_names_only():
    """测试批量复制功能 - 验证只复制医保名称"""
    print('=== 测试批量复制功能（只复制医保名称）===')
    
    # 创建一个唯一的测试城市名称
    test_city = f'测试城市_医保名称_{int(time.time())}'
    test_rule_ids = [3338, 3337]  # 使用已知存在的规则ID
    
    try:
        # 执行批量复制
        print(f'正在将规则 {test_rule_ids} 复制到城市: {test_city}')
        
        response = requests.post(
            f'{base_url}/api/rules/batch-copy',
            json={
                'rule_ids': test_rule_ids,
                'target_city': test_city
            },
            timeout=30
        )
        
        print(f'批量复制API - 状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'复制结果: {json.dumps(data, ensure_ascii=False, indent=2)}')
            
            if data.get('success'):
                print(f'✅ 成功复制 {data.get("copied_count")} 条规则')
                
                # 验证复制的数据 - 检查是否只有医保名称，没有医保编码
                print('\n验证复制的数据...')
                verify_copied_data(test_rule_ids, test_city)
                
            else:
                print(f'❌ 复制失败: {data.get("error")}')
        else:
            print(f'❌ API错误: {response.text}')
            
    except Exception as e:
        print(f'❌ 请求失败: {e}')

def verify_copied_data(rule_ids, city_name):
    """验证复制的数据是否符合要求（只有医保名称，没有医保编码）"""
    try:
        # 获取复制后的规则数据
        for rule_id in rule_ids:
            print(f'\n检查规则 {rule_id} 在城市 {city_name} 的数据...')
            
            # 这里我们通过规则详情API来检查数据
            response = requests.get(f'{base_url}/api/rules/{rule_id}', timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # 检查是否包含目标城市
                if city_name in data.get('cities', []):
                    print(f'✅ 规则 {rule_id} 已成功关联到城市 {city_name}')
                    
                    # 注意：由于API返回的是汇总数据，我们无法直接验证医保编码是否为空
                    # 在实际使用中，可以通过数据库查询来验证
                    print('ℹ️ 数据复制完成，医保编码应该为空（只复制了医保名称）')
                    
                else:
                    print(f'❌ 规则 {rule_id} 未关联到城市 {city_name}')
            else:
                print(f'❌ 获取规则 {rule_id} 详情失败: {response.status_code}')
                
    except Exception as e:
        print(f'❌ 验证数据失败: {e}')

def test_database_verification():
    """通过数据库查询验证复制的数据"""
    print('\n=== 数据库验证说明 ===')
    print('要验证批量复制是否只复制了医保名称而没有复制医保编码，')
    print('可以在数据库中执行以下SQL查询：')
    print()
    print('SELECT 规则id, 城市, 医保编码1, 医保名称1, 医保编码2, 医保名称2')
    print('FROM 规则医保编码对照')
    print('WHERE 城市 LIKE \'测试城市_医保名称_%\'')
    print('ORDER BY 规则id, 城市;')
    print()
    print('预期结果：')
    print('- 医保名称1 和 医保名称2 应该有值（从原规则复制）')
    print('- 医保编码1 和 医保编码2 应该为空（NULL）')

def main():
    """主测试函数"""
    print('=' * 60)
    print('批量复制规则功能测试 - 只复制医保名称')
    print('=' * 60)
    
    test_batch_copy_medical_names_only()
    test_database_verification()
    
    print('\n' + '=' * 60)
    print('测试完成')
    print('=' * 60)
    print('修改说明：')
    print('1. 批量复制功能已修改为只复制医保名称')
    print('2. 医保编码1和医保编码2字段在复制时会保持为空')
    print('3. 其他字段（规则来源、规则内涵、物价编码等）仍会正常复制')

if __name__ == "__main__":
    main()
