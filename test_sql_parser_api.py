"""
测试SQL规则解析器API接口
"""

import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:5001"

def test_parse_sql_content():
    """测试解析SQL内容API"""
    print("=== 测试解析SQL内容API ===")
    
    # 测试SQL内容
    sql_content = """
    -- 规则名称: 重复收费检测测试
    -- 行为认定: 重复收费
    -- 城市: 测试城市
    WITH tab1 AS (
      SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
      FROM 医保住院结算明细 B
      WHERE B.医保项目名称 IN ('血液灌流')
      INTERSECT
      SELECT 结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
      FROM 医保住院结算明细 B
      WHERE B.医保项目名称 IN ('血液透析')
    )
    SELECT * FROM 医保住院结算明细 B
    JOIN tab1 C ON B.结算单据号 = C.结算单据号
    """
    
    response = requests.post(
        f"{BASE_URL}/api/parse_sql_content",
        json={"sql_content": sql_content},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ API调用成功")
            rule_info = result['rule_info']
            print(f"规则名称: {rule_info['rule_name']}")
            print(f"规则类型: {rule_info['rule_type']}")
            print(f"城市: {rule_info['city']}")
            print(f"医保项目: {rule_info['medical_items']}")
            print(f"分类置信度: {result['explanation']['confidence']:.2f}")
        else:
            print(f"❌ API返回错误: {result['error']}")
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        print(response.text)

def test_parse_directory():
    """测试解析目录API"""
    print("\n=== 测试解析目录API ===")
    
    response = requests.post(
        f"{BASE_URL}/api/parse_directory",
        json={"directory_path": "郑州第一附属医院规则"},
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            print("✅ API调用成功")
            print(f"解析规则数量: {result['total_count']}")
            print(f"规则类型统计: {result['stats']['rule_types']}")
        else:
            print(f"❌ API返回错误: {result['error']}")
    else:
        print(f"❌ HTTP错误: {response.status_code}")
        print(response.text)

def main():
    """主函数"""
    print("开始测试SQL规则解析器API接口...")
    
    try:
        # 测试解析SQL内容
        test_parse_sql_content()
        
        # 测试解析目录
        test_parse_directory()
        
        print("\n✅ 所有API测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
