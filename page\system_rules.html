<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>已保存的规则SQL</title>
    
    <!-- 修改为 Bootstrap 样式 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    
    <!-- JavaScript 文件 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <style>
        :root {
            --primary-color: #0078D4;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
        }
        
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            padding: 10px; /* 减少padding以获得更多空间 */
            font-size: 16px; /* 稍微减小字体 */
        }
        
        .container-fluid {
            max-width: 2400px; /* 增加最大宽度 */
            margin: 0 auto;
        }
        
        .card {
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 15px 20px;
        }
        
        .btn-group .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .btn-group .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .btn-group .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        
        .btn-group .btn:hover {
            opacity: 0.9;
        }
        
        .btn-group .bi {
            font-size: 1rem;
        }
        
        /* 调整列宽度 */
        .table th.narrow-column,
        .table td.narrow-column {
            width: 90px;
            max-width: 90px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 16px;
        }

        .table th.medium-column,
        .table td.medium-column {
            width: 140px;
            max-width: 140px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 16px;
        }

        .table th.date-column,
        .table td.date-column {
            width: 160px;
            max-width: 160px;
            white-space: nowrap;
            font-size: 16px;
        }

        /* 规则名称列 */
        .table th.rule-name-column,
        .table td.rule-name-column {
            min-width: 200px;
            max-width: 300px;
            white-space: normal;
            word-break: break-word;
            font-size: 16px;
        }
        
        /* 增加规则内涵列的宽度 */
        .table th.rule-content,
        .table td.rule-content {
            min-width: 600px;  /* 适当减小以平衡整体布局 */
            max-width: none;
            white-space: normal;
            word-break: break-word;
            line-height: 1.3;
            padding: 6px 10px;
            font-size: 16px;
        }
        
        /* 操作列样式 */
        .table th.action-column,
        .table td.action-column {
            width: 100px;
            max-width: 100px;
            white-space: nowrap;
            font-size: 16px;
        }

        /* 添加表格容器样式以支持水平滚动 */
        .table-responsive {
            overflow-x: auto;
            margin: 0 -20px;  /* 增加负边距以获得更多空间 */
            padding: 0 20px;
        }

        /* 表格整体样式优化 */
        .table {
            font-size: 15px;
            margin-bottom: 0;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            padding: 8px 6px;
            vertical-align: middle;
        }

        .table td {
            padding: 6px 6px;
            vertical-align: middle;
        }

        /* 紧凑模式 */
        .table-sm th,
        .table-sm td {
            padding: 4px 6px;
        }

        .alert {
            max-width: 500px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <nav>
            <a href="/" class="btn btn-link">
                <i class="bi bi-arrow-left"></i> 返回主页
            </a>
        </nav>

        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">系统规则SQL</h3>
                    <div class="toolbar">
                        <span class="me-3">已选择 <span id="selectedCount" class="fw-bold">0</span> 条规则</span>
                        <button id="batchDeleteBtn" class="btn btn-danger me-2" onclick="batchDeleteSql()" disabled>
                            <i class="bi bi-trash"></i> 批量删除
                        </button>
                        <button id="importToPGBtn" class="btn btn-primary" onclick="importToPG()">
                            <i class="bi bi-download"></i> 导入需求到PG
                        </button>
                        <button id="importRuleToPGBtn" class="btn btn-primary" onclick="importRuleToPG()">
                            <i class="bi bi-download"></i> 导入SQL到PG
                        </button>
                        <button id="exportSQLBtn" onclick="exportSQLFile()" class="btn btn-success me-2">
                            <i class="bi bi-download"></i> 导出SQL文件
                        </button>
                        <button id="dbConfigBtn" class="btn btn-secondary me-2" onclick="showDbConfigModal()">
                            <i class="bi bi-gear"></i> 数据库设置
                        </button>
                        <button onclick="refreshList()" class="btn btn-light">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <!-- 搜索区域 -->
                <div class="row mb-4">
                    <div class="col-md-2 mb-1">
                        <select class="form-select" id="visitType">
                            <option value="">选择就诊类型</option>
                            <option value="住院">住院</option>
                            <option value="门诊">门诊</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <select class="form-select" id="city">
                            <option value="">选择城市</option>
                        </select>
                    </div>

                    <div class="col-md-2 mb-1">
                        <select class="form-select" id="ruleType">
                            <option value="">选择规则类型</option>
                        </select>
                    </div>

                    <div class="col-md-2 mb-2">
                        <select class="form-select" id="ruleSource">
                            <option value="">选择规则来源</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <select class="form-select" id="hospitalFilter">
                            <option value="">全部医院</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <input type="text" class="form-control" id="ruleName" placeholder="规则名称">
                    </div>

                    <div class="col-md-2 mb-1">
                        <select class="form-select" id="type">
                            <option value="">选择类型</option>
                            <option value="定量">定量</option>
                            <option value="定性">定性</option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-2">
                        <input type="text" class="form-control" id="ruleIdInput" placeholder="规则ID（支持多个，用;或|分隔）">
                    </div>

                    <div class="col-md-3 mb-2">
                        <button class="btn btn-primary me-2" onclick="searchRules()">搜索</button>
                        <button class="btn btn-secondary" onclick="resetFilters()">重置</button>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-responsive">
                    <table id="rulesTable" class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                                </th>
                                <th class="narrow-column">对照ID</th>
                                <th class="narrow-column">规则ID</th>
                                <th class="rule-name-column">规则名称</th>
                                <th class="narrow-column">城市</th>
                                <th class="medium-column">规则来源</th>
                                <th class="narrow-column">就诊类型</th>
                                <th class="medium-column">规则类型</th>
                                <th class="narrow-column">类型</th>
                                <th class="medium-column">适用范围</th>
                                <th class="narrow-column">数据库类型</th>
                                <th class="rule-content">规则内涵</th>
                                <th class="medium-column">模板名称</th>
                                <th class="medium-column">未生成SQL</th>
                                <th class="date-column">创建时间</th>
                                <th class="action-column">操作</th>
                            </tr>
                        </thead>
                        <tbody id="rulesTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- SQL编辑弹窗 -->
    <div class="modal fade" id="sqlEditModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑SQL</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <textarea id="sqlEditor" class="form-control font-monospace" rows="15"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveSqlChanges()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加模态框 -->
    <div class="modal fade" id="pgImportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="pgImportForm">
                        <div class="mb-3">
                            <label class="form-label">省份ID</label>
                            <input type="text" class="form-control" name="province_id" value="34" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">省份名称</label>
                            <input type="text" class="form-control" name="province_name" value="安徽" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">城市ID</label>
                            <input type="text" class="form-control" name="city_id" value="3401" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">城市名称</label>
                            <input type="text" class="form-control" name="city_name" value="合肥" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">医院编码</label>
                            <input type="text" class="form-control" name="hospital_code" value="H34010000001" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">医院名称</label>
                            <input type="text" class="form-control" name="hospital_name" value="合肥市第一人民医院" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">机构编码</label>
                            <input type="text" class="form-control" name="org_code" value="ZQS_YY_ZQDXFSFLYY_7ZD" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">医保/医院</label>
                            <select class="form-select" name="yb_or_yy" required>
                                <option value="2">医院</option>
                                <option value="1">医保</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitImport()">确认导入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据库设置模态框 -->
    <div class="modal fade" id="dbConfigModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">PG数据库设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="dbConfigForm">
                        <div class="mb-3">
                            <label class="form-label">主机</label>
                            <input type="text" class="form-control" name="host" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">端口</label>
                            <input type="text" class="form-control" name="port" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">数据库名</label>
                            <input type="text" class="form-control" name="dbname" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" name="user" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <input type="password" class="form-control" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Schema</label>
                            <input type="text" class="form-control" name="schema" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveDbConfig()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 在页面加载时初始化模态框对象
        let sqlEditModal;
        $(document).ready(function() {
            // 初始化模态框
            sqlEditModal = new bootstrap.Modal(document.getElementById('sqlEditModal'));

            // 完全重写表格初始化
            initializeRulesTable();

            // 初始化各个下拉框
            loadFilterOptions();

            // 加载医院列表
            loadHospitalOptions();

            // 检查URL参数，如果有ruleId参数则自动过滤
            checkUrlParameters();
        });

        let currentEditingId = null;
        let hospitalAdoptedRuleIds = []; // 存储当前选中医院的已采用规则对照ID列表

        // 完全重写表格初始化
        function initializeRulesTable() {
            // 如果表格已经初始化，先销毁它
            if ($.fn.DataTable.isDataTable('#rulesTable')) {
                $('#rulesTable').DataTable().destroy();
            }
            
            // 重新初始化表格
            $('#rulesTable').DataTable({
                ajax: {
                    url: '/api/sql_history',
                    dataSrc: ''
                },
                scrollX: true,  // 启用水平滚动
                autoWidth: false,  // 禁用自动宽度
                pageLength: 25,  // 每页显示更多行
                lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                columns: [
                    {
                        // 复选框列
                        data: null,
                        orderable: false,
                        width: "40px",
                        render: function (data, type, row) {
                            return `<input type="checkbox" class="rule-checkbox" value="${row.compare_id}" onchange="handleCheckboxChange()">`;
                        }
                    },
                    { data: 'compare_id', width: "80px" },      // 对照ID
                    { data: 'rule_id', width: "80px" },         // 规则ID
                    { data: 'rule_name', width: "250px" },       // 规则名称
                    { data: 'city', width: "80px" },            // 城市
                    { data: 'rule_source', width: "120px" },     // 规则来源
                    { data: 'visit_type', width: "80px" },      // 就诊类型
                    { data: 'rule_type', width: "120px" },       // 规则类型
                    { data: 'types', width: "80px" },           // 类型
                    { data: 'appl_scope', width: "120px" },         // 适用范围
                    { data: 'sql_type', width: "100px" },   // 选择数据库类型
                    { data: 'rule_content', width: "600px" },    // 规则内涵
                    { data: 'template_name', width: "150px" },   // 模板名称
                    {
                        // 未生成SQL列
                        data: 'missing_sql',
                        width: "120px",
                        render: function (data, type, row) {
                            if (data && data.trim() !== '') {
                                return `<span class="badge bg-warning text-dark">${data}</span>`;
                            }
                            return '<span class="badge bg-success">完整</span>';
                        }
                    },
                    { data: 'create_time', width: "150px" },     // 创建时间
                    
                    {
                        // 操作列
                        data: null,
                        orderable: false,
                        width: "100px",
                        render: function (data, type, row) {
                            return `<div class="btn-group">
                                <button class="btn btn-sm btn-primary" onclick="editSql(${row.compare_id}, '${row.visit_type}')">
                                    <i class="bi bi-pencil-square"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteSql(${row.compare_id}, '${row.visit_type}')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>`;
                        }
                    }
                ],
                language: {
                    "sProcessing": "处理中...",
                    "sLengthMenu": "显示 _MENU_ 条",
                    "sZeroRecords": "没有匹配结果",
                    "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
                    "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
                    "sInfoPostFix": "",
                    "sSearch": "搜索:",
                    "sUrl": "",
                    "sEmptyTable": "表中数据为空",
                    "sLoadingRecords": "载入中...",
                    "sInfoThousands": ",",
                    "oPaginate": {
                        "sFirst": "首页",
                        "sPrevious": "上页",
                        "sNext": "下页",
                        "sLast": "末页"
                    }
                }
            });
            
            // 初始化后更新选中计数
            updateSelectedCount();
        }

        // 刷新列表函数
        function refreshList() {
            $('#rulesTable').DataTable().ajax.reload();
            document.getElementById('selectAll').checked = false;
            updateSelectedCount();
        }

        // 修改编辑SQL函数
        function editSql(ruleId, visitType) {
            currentEditingId = ruleId;
            
            // 先显示模态框
            sqlEditModal.show();
            
            // 显示加载状态
            $('#sqlEditor').val('加载中...');
            
            // 获取SQL内容
            fetch(`/api/sql_content/${ruleId}/${encodeURIComponent(visitType)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('获取SQL内容失败：' + data.error);
                        return;
                    }
                    
                    // 解码HTML实体
                    const decodedSql = data.sql_content.replace(/&quot;/g, '"')
                                                   .replace(/&#039;/g, "'")
                                                   .replace(/&lt;/g, '<')
                                                   .replace(/&gt;/g, '>')
                                                   .replace(/&amp;/g, '&');
                    $('#sqlEditor').val(decodedSql);
                })
                .catch(error => {
                    console.error('获取SQL内容失败:', error);
                    $('#sqlEditor').val('获取SQL内容失败，请重试');
                });
        }

        // 修改关闭模态框函数
        function closeEditModal() {
            sqlEditModal.hide();
            currentEditingId = null;
        }

        // 修改保存SQL函数
        function saveSqlChanges() {
            const newSql = $('#sqlEditor').val();
            const table = $('#rulesTable').DataTable();
            //const visit_type =  $('#visitType').val();
            const ruleId = currentEditingId;
            const row = $(`input[value="${ruleId}"]`).closest('tr');
            const cells = row.find('td');
            const visit_type = cells.eq(6).text().trim(); // 获取第7列的就诊类型值
            const currentPage = table.page();
            
            // 保存当前的筛选条件
            const currentFilters = {
                city: $('#city').val(),
                ruleSource: $('#ruleSource').val(),
                visit_type: $('#visitType').val(),
                ruleType: $('#ruleType').val(),
                ruleName: $('#ruleName').val(),
                ruleContent: $('#ruleContent').val()
            };
            
            //console.log('准备保存SQL:', {
            //    rule_id: currentEditingId,
            //    sql_content_length: newSql.length,
            //    visit_type: visit_type
            //});
            
            fetch('/api/update_sql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rule_id: currentEditingId,
                    sql_content: newSql,
                    visit_type: visit_type
                })
            })
            .then(response => {
                //console.log('收到响应:', response.status);
                return response.json();
            })
            .then(data => {
                //console.log('响应数据:', data);
                if (data.success) {
                    sqlEditModal.hide();
                    
                    // 仅重新加载当前表格数据，保持筛选条件
                    table.ajax.reload(function() {
                        // 恢复筛选条件
                        $('#city').val(currentFilters.city);
                        $('#ruleSource').val(currentFilters.ruleSource);
                        $('#visitType').val(currentFilters.visit_type);
                        $('#ruleType').val(currentFilters.ruleType);
                        $('#ruleName').val(currentFilters.ruleName);
                        $('#ruleContent').val(currentFilters.ruleContent);
                        // 恢复到之前的页码
                        table.page(currentPage).draw('page');
                    }, false);  // false 表示保持当前页面位置
                    
                    showMessage('保存成功', 'success');
                } else {
                    throw new Error(data.error || '保存失败');
                }
            })
            .catch(error => {
                console.error('保存SQL失败:', error);
                console.error('错误详情:', error.stack);
                showMessage('保存失败：' + error.message, 'error');
            });
        }

        // 添加消息提示函数
        function showMessage(message, type = 'info') {
            const alertDiv = $(`
                <div class="alert alert-${type} alert-dismissible fade show position-fixed top-0 end-0 m-3" role="alert" style="z-index: 1050;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `);
            
            $('body').append(alertDiv);
            
            // 3秒后自动关闭
            setTimeout(() => {
                alertDiv.alert('close');
            }, 3000);
        }

        // 加载筛选选项
        function loadFilterOptions() {
            fetch('/api/filter_options')
                .then(response => response.json())
                .then(data => {
                    // 填充城市下拉框
                    fillSelectOptions('city', data.cities);
                    // 填充规则来源下拉框
                    fillSelectOptions('ruleSource', data.rule_sources);
                    // 填充规则类型下拉框
                    fillSelectOptions('ruleType', data.rule_types);
                })
                .catch(error => {
                    console.error('加载筛选选项失败:', error);
                    showMessage('加载筛选选项失败，请刷新页面重试', 'error');
                });
        }

        // 加载医院选项
        function loadHospitalOptions() {
            fetch('/api/hospitals')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.hospitals) {
                        const hospitalSelect = document.getElementById('hospitalFilter');
                        hospitalSelect.innerHTML = '<option value="">全部医院</option>';

                        data.hospitals.forEach(hospital => {
                            const option = document.createElement('option');
                            option.value = hospital.医院ID;
                            option.textContent = hospital.医院名称;
                            hospitalSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载医院列表失败:', error);
                    showMessage('加载医院列表失败', 'error');
                });
        }

        // 获取医院已采用规则的对照ID列表
        function getHospitalAdoptedRules(hospitalId) {
            if (!hospitalId) {
                hospitalAdoptedRuleIds = [];
                return Promise.resolve([]);
            }

            return fetch(`/api/hospital-rules/adopted/${hospitalId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.rules) {
                        // 直接从已采用规则中提取对照ID
                        hospitalAdoptedRuleIds = data.rules
                            .map(rule => rule.对照ID)
                            .filter(id => id != null)
                            .map(id => id.toString());
                        return hospitalAdoptedRuleIds;
                    }
                    return [];
                })
                .catch(error => {
                    console.error('获取医院已采用规则失败:', error);
                    showMessage('获取医院已采用规则失败', 'error');
                    return [];
                });
        }

        // 填充下拉框选项
        function fillSelectOptions(elementId, options) {
            const select = document.getElementById(elementId);
            const currentValue = select.value; // 保存当前选中值
            
            // 清空现有选项，保留默认选项
            select.innerHTML = `<option value="">选择${select.options[0].text.replace('选择', '')}</option>`;
            
            // 添加新选项
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });
            
            // 恢复之前的选中值
            if (currentValue) {
                select.value = currentValue;
            }
        }

        // 添加回车键搜索功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为搜索输入框添加回车键事件监听
            document.getElementById('ruleName').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            document.getElementById('ruleContent').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            // 为下拉框添加回车键事件监听
            document.getElementById('city').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            document.getElementById('ruleSource').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            document.getElementById('behaviorType').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });
            
            document.getElementById('ruleType').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchRules();
                }
            });

            // 为医院筛选器添加change事件监听
            document.getElementById('hospitalFilter').addEventListener('change', function(e) {
                searchRules();
            });
        });

        // 修改搜索函数，支持特殊字符和通配符
        function searchRules() {
            const hospitalId = $('#hospitalFilter').val();

            // 如果选择了医院，先获取该医院的已采用规则
            if (hospitalId) {
                showMessage('正在获取医院已采用规则...', 'info');
                getHospitalAdoptedRules(hospitalId).then(() => {
                    performSearch();
                });
            } else {
                hospitalAdoptedRuleIds = [];
                performSearch();
            }
        }

        // 执行实际的搜索逻辑
        function performSearch() {
            // 获取搜索条件
            const searchParams = {
                city: $('#city').val(),
                rule_source: $('#ruleSource').val(),
                behavior_type: $('#behaviorType').val(),
                rule_type: $('#ruleType').val(),
                type: $('#type').val(),
                rule_id: $('#ruleIdInput').val(),
                rule_name: $('#ruleName').val(),
                rule_content: $('#ruleContent').val(),
                visit_type: $('#visitType').val(),
                hospital_filter: $('#hospitalFilter').val()
            };
            
            console.log('搜索条件:', searchParams);
            
            // 获取DataTable实例
            const table = $('#rulesTable').DataTable();
            
            // 使用客户端过滤
            if (Object.values(searchParams).some(value => value)) {
                // 使用DataTables的搜索API进行客户端过滤
                $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                    // 检查每个搜索条件
                    if (searchParams.city && data[4] !== searchParams.city) return false;
                    if (searchParams.rule_source && data[5] !== searchParams.rule_source) return false;
                    if (searchParams.visit_type && data[6] !== searchParams.visit_type) return false;
                    if (searchParams.rule_type && data[7] !== searchParams.rule_type) return false;
                    if (searchParams.type && data[8] !== searchParams.type) return false;
                    
                    // 对文本字段使用包含搜索，支持特殊字符
                    if (searchParams.rule_name) {
                        const searchValue = processSearchValue(searchParams.rule_name);
                        const regex = new RegExp(searchValue, 'i');
                        if (!regex.test(data[3])) return false;
                    }
                    
                    if (searchParams.rule_content) {
                        const searchValue = processSearchValue(searchParams.rule_content);
                        const regex = new RegExp(searchValue, 'i');
                        if (!regex.test(data[9])) return false;
                    }

                    // 规则ID多值检索
                    if (searchParams.rule_id) {
                        // 支持分隔符 ; | , 空格
                        const idArr = searchParams.rule_id.split(/[;|,\s]+/).filter(Boolean);
                        if (idArr.length > 0) {
                            if (idArr.length === 1) {
                                if (data[2] !== idArr[0]) return false; // 规则ID列索引2
                            } else {
                                if (!idArr.includes(data[2])) return false;
                            }
                        }
                    }

                    // 医院筛选：只显示该医院已采用的规则
                    if (searchParams.hospital_filter && hospitalAdoptedRuleIds.length > 0) {
                        const compareId = data[1]; // 对照ID列索引1
                        if (!hospitalAdoptedRuleIds.includes(compareId.toString())) {
                            return false;
                        }
                    }

                    return true;
                });

                // 重绘表格应用过滤器
                table.draw();
                
                // 移除过滤器以避免影响后续操作
                $.fn.dataTable.ext.search.pop();
            } else {
                // 如果没有搜索条件，重置为原始数据
                table.ajax.url('/api/sql_history').load(function() {
                    // 重置全选框状态
                    document.getElementById('selectAll').checked = false;
                    // 更新选中计数
                    updateSelectedCount();
                });
            }
        }

        // 处理搜索值的函数，支持特殊字符和通配符
        function processSearchValue(value) {
            if (!value) return '';
            
            // 转义正则表达式中的特殊字符，但保留%
            let escaped = value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            
            // 将%替换为.*（正则表达式中的任意字符匹配）
            escaped = escaped.replace(/%/g, '.*');
            
            return escaped;
        }

        // 重置过滤器
        function resetFilters() {
            // 清空所有输入框和下拉框
            $('#city').val('');
            $('#ruleSource').val('');
            $('#behaviorType').val('');
            $('#ruleType').val('');
            $('#ruleName').val('');
            $('#ruleContent').val('');
            $('#hospitalFilter').val('');
            $('#visitType').val('');
            $('#type').val('');
            $('#ruleIdInput').val('');

            // 重置医院已采用规则列表
            hospitalAdoptedRuleIds = [];

            // 重置表格到初始状态
            const table = $('#rulesTable').DataTable();
            table.ajax.url('/api/sql_history').load(function() {
                // 重置全选框状态
                document.getElementById('selectAll').checked = false;
                // 更新选中计数
                updateSelectedCount();
            });
        }

        // 修复删除SQL函数，保留检索条件
        function deleteSql(ruleId, visitType) {
            if (!visitType) {
                showMessage('无法获取visit_type，删除失败', 'error');
                return;
            }
            if (!confirm('确定要删除此规则的SQL吗？此操作不可恢复。')) {
                return;
            }
            fetch(`/api/delete_sql/${ruleId}/${encodeURIComponent(visitType)}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('SQL记录已成功删除', 'success');
                    refreshTableWithCurrentFilters();
                } else {
                    showMessage('删除SQL记录失败：' + (data.error || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('删除SQL记录失败:', error);
                showMessage('删除SQL记录失败: ' + error.message, 'error');
            });
        }

        // 添加一个新函数，用于保留当前检索条件刷新表格
        function refreshTableWithCurrentFilters() {
            // 获取当前的检索条件
            const city = $('#city').val();
            const ruleSource = $('#ruleSource').val();
            const behaviorType = $('#behaviorType').val();
            const ruleType = $('#ruleType').val();
            const ruleName = $('#ruleName').val();
            const ruleContent = $('#ruleContent').val();
            const visitType = $('#visitType').val();
            const type = $('#type').val();
            const ruleIdInput = $('#ruleIdInput').val();
            const hospitalFilter = $('#hospitalFilter').val();

            // 如果有搜索条件，使用客户端过滤
            if (city || ruleSource || behaviorType || ruleType || ruleName || ruleContent || visitType || type || ruleIdInput || hospitalFilter) {
                // 先加载完整数据
                const table = $('#rulesTable').DataTable();
                table.ajax.url('/api/sql_history').load(function() {
                    // 然后应用搜索条件
                    searchRules();
                    // 重置全选框状态
                    document.getElementById('selectAll').checked = false;
                    // 更新选中计数
                    updateSelectedCount();
                });
            } else {
                // 如果没有搜索条件，直接加载原始数据
                const table = $('#rulesTable').DataTable();
                table.ajax.url('/api/sql_history').load(function() {
                    // 重置全选框状态
                    document.getElementById('selectAll').checked = false;
                    // 更新选中计数
                    updateSelectedCount();
                });
            }
        }

        // 添加全选/取消全选功能
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const isChecked = selectAllCheckbox.checked;
            
            // 获取所有规则复选框
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            
            // 设置所有复选框状态与全选框一致
            checkboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            
            // 更新UI状态
            updateSelectedCount();
        }

        // 处理单个复选框变化
        function handleCheckboxChange() {
            // 更新选中数量
            updateSelectedCount();
            
            // 检查是否所有复选框都被选中
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
            
            // 更新全选框状态
            document.getElementById('selectAll').checked = allChecked;
        }

        // 更新选中规则数量显示
        function updateSelectedCount() {
            const selectedCount = document.querySelectorAll('.rule-checkbox:checked').length;
            const countElement = document.getElementById('selectedCount');
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');

            if (countElement) {
                countElement.textContent = selectedCount;
            }

            // 控制批量删除按钮的启用状态
            if (batchDeleteBtn) {
                batchDeleteBtn.disabled = selectedCount === 0;
            }
        }

        // 获取选中的规则ID
        function getSelectedRuleIds() {
            const selectedCheckboxes = document.querySelectorAll('.rule-checkbox:checked');
            return Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
        }

        // 批量删除SQL记录
        function batchDeleteSql() {
            const selectedRules = [];
            $('.rule-checkbox:checked').each(function() {
                const row = $(this).closest('tr');
                const cells = row.find('td');
                selectedRules.push({
                    compare_id: $(this).val(),
                    visit_type: cells.eq(6).text().trim() // 获取就诊类型
                });
            });

            if (selectedRules.length === 0) {
                showMessage('请至少选择一条规则', 'warning');
                return;
            }

            // 确认删除
            if (!confirm(`确定要删除选中的 ${selectedRules.length} 条SQL记录吗？此操作不可恢复！`)) {
                return;
            }

            // 显示加载状态
            const deleteBtn = document.getElementById('batchDeleteBtn');
            const originalText = deleteBtn.innerHTML;
            deleteBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 删除中...';
            deleteBtn.disabled = true;

            // 发送批量删除请求
            fetch('/api/batch_delete_sql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rules: selectedRules
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`成功删除 ${data.deleted_count} 条SQL记录`, 'success');

                    // 使用现有的函数来保持筛选条件刷新表格
                    refreshTableWithCurrentFilters();

                    // 重置选中状态
                    document.getElementById('selectAll').checked = false;
                    updateSelectedCount();
                } else {
                    throw new Error(data.error || '删除失败');
                }
            })
            .catch(error => {
                console.error('批量删除失败:', error);
                showMessage('删除失败：' + error.message, 'error');
            })
            .finally(() => {
                // 恢复按钮状态
                deleteBtn.innerHTML = originalText;
                deleteBtn.disabled = false;
            });
        }

        // 修改导出SQL文件函数，正确处理二进制响应
        function exportSQLFile() {
            const selectedIds = getSelectedRuleIds();
            
            if (selectedIds.length === 0) {
                showMessage('请至少选择一条规则', 'error');
                return;
            }
            
            // 显示加载状态
            const exportBtn = document.getElementById('exportSQLBtn');
            const originalText = exportBtn.innerHTML;
            exportBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 导出中...';
            exportBtn.disabled = true;
            
            // 直接使用表单提交方式下载文件，避免JSON解析问题
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/api/export_system_sql';
            form.style.display = 'none';
            
            // 添加选中的规则ID
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'rule_ids';
            input.value = JSON.stringify(selectedIds);
            form.appendChild(input);
            
            // 添加到文档并提交
            document.body.appendChild(form);
            form.submit();
            
            // 恢复按钮状态
            setTimeout(() => {
                exportBtn.innerHTML = originalText;
                exportBtn.disabled = false;
                document.body.removeChild(form);
            }, 1000);
        }

        function importToPG() {
            const selectedRuleId = document.querySelector('#rulesTable tbody tr td input[type="checkbox"]:checked')?.value;
            
            if (!selectedRuleId) {
                showMessage('请选择一条规则', 'warning');
                return;
            }

            // 显示配置模态框
            const modal = new bootstrap.Modal(document.getElementById('pgImportModal'));
            modal.show();
        }

        function submitImport() {
            // 获取所有选中的规则及其就诊类型
            const selectedRules = [];
            document.querySelectorAll('#rulesTable tbody tr td input[type="checkbox"]:checked').forEach(checkbox => {
                const row = checkbox.closest('tr');
                const cells = row.querySelectorAll('td');
                const visitType = cells[6].textContent.trim(); // 获取第7列的就诊类型值

                selectedRules.push({
                    id: checkbox.value,
                    visit_type: visitType
                });
            });

            if (selectedRules.length === 0) {
                showMessage('请至少选择一条规则', 'warning');
                return;
            }

            const formData = new FormData(document.getElementById('pgImportForm'));
            const baseParams = new URLSearchParams();

            // 将表单数据转换为URL参数（不包含visit_type，因为每个规则有自己的visit_type）
            for (const [key, value] of formData.entries()) {
                baseParams.append(key, value);
            }

            console.log("选择的规则:", selectedRules);
            console.log("基础表单数据:", baseParams.toString());

            // 获取每个选中规则的SQL内容并导入
            Promise.all(selectedRules.map(rule => {
                const params = new URLSearchParams(baseParams);
                params.append('visit_type', rule.visit_type); // 为每个规则添加其对应的就诊类型

                return fetch(`/api/import_sql_content/${rule.id}?${params.toString()}`)
                    .then(response => response.json());
            }))
            .then(responses => {
                const validResponses = responses.filter(data => !data.error);
                if (validResponses.length === 0) {
                    showMessage('获取SQL失败或没有有效的SQL内容', 'error');
                    return;
                }

                // 显示加载状态
                showMessage('正在导入...', 'info');

                // 发送完整数据到后端
                return fetch('/api/import_to_pg', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(validResponses)
                });
            })
            .then(response => response?.json())
            .then(data => {
                if (!data) return;
                
                // 关闭模态框
                bootstrap.Modal.getInstance(document.getElementById('pgImportModal')).hide();
                
                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage('导入失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showMessage('导入失败: ' + error.message, 'error');
                console.error('导入错误:', error);
            });
        }

        function importRuleToPG() {
            // 获取所有选中的规则ID和对应的visit_type
            const selectedRules = [];
            $('.rule-checkbox:checked').each(function() {
                const row = $(this).closest('tr');
                const cells = row.find('td');
                selectedRules.push({
                    id: $(this).val(),
                    visit_type: cells.eq(6).text().trim() // 使用第7列(index=6)的数据作为visit_type
                });
            });
            
            if (selectedRules.length === 0) {
                showMessage('请至少选择一条规则', 'warning');
                return;
            }

            // 确认导入
            if (!confirm(`确定要导入 ${selectedRules.length} 条规则的SQL到PG数据库吗？`)) {
                return;
            }

            // 显示加载状态
            const importBtn = $('#importRuleToPGBtn');
            const originalText = importBtn.html();
            importBtn.html('<i class="bi bi-hourglass-split"></i> 导入中...').prop('disabled', true);

            // 按visit_type分组规则
            const rulesByVisitType = selectedRules.reduce((acc, rule) => {
                if (!acc[rule.visit_type]) {
                    acc[rule.visit_type] = [];
                }
                acc[rule.visit_type].push(rule.id);
                return acc;
            }, {});

            // 为每个visit_type组发送一个请求
            const importPromises = Object.entries(rulesByVisitType).map(([visit_type, rule_ids]) => {
                return fetch('/api/import_rule_to_pg', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        rule_ids: rule_ids,
                        visit_type: visit_type
                    })
                }).then(response => response.json());
            });

            // 处理所有请求的结果
            Promise.all(importPromises)
                .then(results => {
                    const successCount = results.reduce((count, data) => {
                        if (data.success) {
                            const importedCount = parseInt(data.message.match(/\d+/)[0]);
                            return count + importedCount;
                        }
                        return count;
                    }, 0);

                    if (successCount > 0) {
                        showMessage(`成功导入 ${successCount} 条规则的SQL内容`, 'success');
                        // 重置选中状态
                        document.getElementById('selectAll').checked = false;
                        $('.rule-checkbox').prop('checked', false);
                        updateSelectedCount();
                    } else {
                        throw new Error('没有规则被成功导入');
                    }
                })
                .catch(error => {
                    console.error('导入失败:', error);
                    showMessage('导入失败：' + error.message, 'error');
                })
                .finally(() => {
                    // 恢复按钮状态
                    importBtn.html(originalText).prop('disabled', false);
                });
        }

        function showDbConfigModal() {
            fetch('/api/pg_config')
                .then(response => response.json())
                .then(data => {
                    const form = document.getElementById('dbConfigForm');
                    form.host.value = data.host || '';
                    form.port.value = data.port || '5432';
                    form.dbname.value = data.dbname || '';
                    form.user.value = data.user || '';
                    form.password.value = data.password || '';
                    form.schema.value = data.schema || 'public';
                    new bootstrap.Modal(document.getElementById('dbConfigModal')).show();
                })
                .catch(error => {
                    showMessage('读取数据库配置失败: ' + error, 'error');
                });
        }

        function saveDbConfig() {
            const form = document.getElementById('dbConfigForm');
            const data = {
                host: form.host.value,
                port: form.port.value,
                dbname: form.dbname.value,
                user: form.user.value,
                password: form.password.value,
                schema: form.schema.value
            };
            fetch('/api/pg_config', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(res => {
                if (res.success) {
                    showMessage('数据库配置已保存', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('dbConfigModal')).hide();
                } else {
                    showMessage('保存失败: ' + (res.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                showMessage('保存失败: ' + error, 'error');
            });
        }

        // 检查URL参数并自动过滤
        function checkUrlParameters() {
            const urlParams = new URLSearchParams(window.location.search);
            const ruleId = urlParams.get('ruleId');

            if (ruleId) {
                // 设置规则ID输入框的值
                document.getElementById('ruleIdInput').value = ruleId;

                // 延迟执行搜索，确保表格已经初始化完成
                setTimeout(() => {
                    searchRules();
                    // 显示提示信息
                    showMessage(`已自动过滤规则ID: ${ruleId}`, 'info');
                }, 1000);
            }
        }
    </script>
</body>
</html>