#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试年龄字段修复
"""

import requests
import json

# 测试配置
base_url = 'http://localhost:5001'

def test_age_field(age_value, description):
    """测试年龄字段"""
    print(f'\n=== 测试年龄字段: {description} ===')
    
    test_rule_data = {
        '规则名称': f'年龄测试_{description}',
        '行为认定': '测试行为认定',
        '类型': '药品',
        '规则类型': '0',
        '系统规则': 0,
        '年龄': age_value
    }
    
    try:
        response = requests.post(
            f'{base_url}/api/rules',
            json=test_rule_data,
            timeout=30
        )
        
        print(f'状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'✅ 年龄字段 {description} 测试成功，ID: {data.get("id")}')
            return True
        else:
            print(f'❌ 年龄字段 {description} 测试失败: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 年龄字段 {description} 请求失败: {e}')
        return False

def test_complete_rule():
    """测试完整规则创建（不包含年龄字段）"""
    print(f'\n=== 测试完整规则创建（不包含年龄字段）===')
    
    test_rule_data = {
        '序号': '001',
        '规则名称': '完整测试规则_无年龄',
        '行为认定': '测试行为认定',
        '类型': '药品',
        '规则类型': '0',
        '系统规则': 0,
        '性别': '男',
        '备注': '测试备注',
        '涉及科室': '内科',
        '违规数量': 10,
        '违规金额': 1000,
        '违规天数': 7,
        '违规小时数': 24,
        '国家医保编码1': 'GJ001',
        '国家医保名称1': '国家医保测试1',
        '国家医保编码2': 'GJ002',
        '国家医保名称2': '国家医保测试2',
        '排除诊断': '排除测试诊断',
        '排除科室': '排除测试科室',
        '包含诊断': '包含测试诊断',
        '包含科室': '包含测试科室',
        '时间类型': '天',
        '项目数量': 5,
        '用途': '测试用途'
    }
    
    try:
        response = requests.post(
            f'{base_url}/api/rules',
            json=test_rule_data,
            timeout=30
        )
        
        print(f'状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'✅ 完整规则创建成功，ID: {data.get("id")}')
            return True
        else:
            print(f'❌ 完整规则创建失败: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 完整规则创建请求失败: {e}')
        return False

def main():
    """主测试函数"""
    print('=' * 60)
    print('年龄字段修复测试')
    print('=' * 60)
    
    # 测试不同的年龄值
    age_tests = [
        (25, '纯数字'),
        ('30', '字符串数字'),
        ('18-65', '年龄范围'),
        ('', '空值'),
        (None, 'None值')
    ]
    
    for age_value, description in age_tests:
        test_age_field(age_value, description)
    
    # 测试完整规则创建
    test_complete_rule()
    
    print('\n' + '=' * 60)
    print('测试完成')
    print('=' * 60)
    print('说明：')
    print('1. 年龄字段如果包含非数字字符（如"18-65"），会被跳过')
    print('2. 纯数字的年龄值会被正常保存')
    print('3. 其他字段不受影响')

if __name__ == "__main__":
    main()
