"""
深度SQL解析器综合测试
测试所有功能的集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sql_deep_parser import DeepSQLParser, RuleType
import json


def test_comprehensive_rule_parsing():
    """综合测试：复杂的医保规则SQL解析"""
    print("=== 综合测试：复杂医保规则SQL解析 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    # 一个包含多种特征的复杂SQL
    sql = """
    -- 规则名称: 血液透析与血液灌流同日重复收费检测
    -- 城市: 北京市
    -- 行为认定: 重复收费
    WITH duplicate_check AS (
        SELECT A.结算单据号, to_char(B.项目使用日期,'yyyy-MM-dd hh24') 使用日期,
               SUM(B.数量) AS 总数量,
               COUNT(DISTINCT B.医保项目名称) AS 项目种类数
        FROM 医保住院结算主单 A
        JOIN 医保住院结算明细 B ON A.结算单据号 = B.结算单据号
        WHERE A.患者年龄 BETWEEN 18 AND 80
          AND A.患者性别 = '男'
          AND B.医保项目名称 IN ('血液透析', '血液灌流')
        GROUP BY A.结算单据号, to_char(B.项目使用日期,'yyyy-MM-dd hh24')
        HAVING SUM(B.数量) > 1 AND COUNT(DISTINCT B.医保项目名称) > 1
    ),
    intersect_pattern AS (
        SELECT 结算单据号, 使用日期 FROM 医保住院结算明细 
        WHERE 医保项目名称 = '血液透析'
        INTERSECT
        SELECT 结算单据号, 使用日期 FROM 医保住院结算明细 
        WHERE 医保项目名称 = '血液灌流'
    )
    SELECT D.结算单据号, D.使用日期, D.总数量, D.项目种类数
    FROM duplicate_check D
    JOIN intersect_pattern I ON D.结算单据号 = I.结算单据号 AND D.使用日期 = I.使用日期
    """
    
    result = parser.parse(sql)
    
    print(f"解析成功: {result.success}")
    if result.success:
        rule_ir = result.rule_ir
        print(f"规则类型: {rule_ir.rule_type.value}")
        print(f"数据源数量: {len(rule_ir.data_sources)}")
        print(f"条件数量: {len(rule_ir.conditions)}")
        print(f"聚合函数数量: {len(rule_ir.aggregations)}")
        
        # 检查重复收费模式
        if rule_ir.duplicate_pattern:
            print("\n重复收费模式:")
            print(f"  主要项目: {rule_ir.duplicate_pattern.primary_items}")
            print(f"  冲突项目: {rule_ir.duplicate_pattern.conflict_items}")
            print(f"  时间精度: {rule_ir.duplicate_pattern.time_precision}")
            print(f"  检测方法: {rule_ir.duplicate_pattern.detection_method}")
        
        # 检查条件类型分布
        condition_types = {}
        for cond in rule_ir.conditions:
            field_type = cond.field.field_type.value
            if field_type not in condition_types:
                condition_types[field_type] = 0
            condition_types[field_type] += 1
        
        print(f"\n条件类型分布: {condition_types}")
        
        # 检查聚合函数类型
        if rule_ir.aggregations:
            agg_types = [agg.function.value for agg in rule_ir.aggregations]
            print(f"聚合函数类型: {agg_types}")
        
        # 输出JSON格式（截断以避免过长）
        json_output = rule_ir.to_json()
        json_obj = json.loads(json_output)
        print(f"\nJSON输出长度: {len(json_output)} 字符")
        print(f"JSON结构键: {list(json_obj.keys())}")
        
        return True
    else:
        print(f"解析失败: {result.error_message}")
        return False


def test_rule_type_classification():
    """测试规则类型分类"""
    print("\n=== 测试规则类型分类 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    test_cases = [
        # 年龄限制规则
        ("SELECT * FROM 医保住院结算明细 WHERE 患者年龄 > 65", RuleType.AGE_RESTRICTION),
        
        # 性别限制规则
        ("SELECT * FROM 医保住院结算明细 WHERE 患者性别 = '女'", RuleType.GENDER_RESTRICTION),
        
        # 超量使用规则
        ("SELECT 结算单据号, SUM(数量) FROM 医保住院结算明细 GROUP BY 结算单据号 HAVING SUM(数量) > 2", RuleType.EXCESSIVE_USAGE),
        
        # 重复收费规则（INTERSECT）
        ("SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称='A' INTERSECT SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称='B'", RuleType.DUPLICATE_BILLING),
        
        # 重复收费规则（多表JOIN）
        ("SELECT * FROM 医保住院结算明细 A JOIN 医保住院结算主单 B ON A.结算单据号 = B.结算单据号", RuleType.DUPLICATE_BILLING)
    ]
    
    passed = 0
    for i, (sql, expected_type) in enumerate(test_cases):
        result = parser.parse(sql)
        if result.success:
            actual_type = result.rule_ir.rule_type
            is_correct = actual_type == expected_type
            status = "✅" if is_correct else "❌"
            print(f"  {i+1}. {status} 预期: {expected_type.value}, 实际: {actual_type.value}")
            if is_correct:
                passed += 1
        else:
            print(f"  {i+1}. ❌ 解析失败: {result.error_message}")
    
    print(f"分类准确率: {passed}/{len(test_cases)} ({passed/len(test_cases)*100:.1f}%)")
    return passed >= len(test_cases) * 0.8  # 80%准确率


def test_performance():
    """测试解析性能"""
    print("\n=== 测试解析性能 ===")
    
    parser = DeepSQLParser(dialect="postgres")
    
    # 复杂SQL
    complex_sql = """
    WITH patient_filter AS (
        SELECT 结算单据号 FROM 医保住院结算主单 
        WHERE 患者年龄 BETWEEN 20 AND 80 AND 患者性别 IN ('男', '女')
    ),
    item_usage AS (
        SELECT 结算单据号, 医保项目名称, SUM(数量) as 总数量, COUNT(*) as 使用次数
        FROM 医保住院结算明细
        WHERE 医保项目名称 IN ('项目A', '项目B', '项目C', '项目D', '项目E')
        GROUP BY 结算单据号, 医保项目名称
        HAVING SUM(数量) > 1
    ),
    duplicate_items AS (
        SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = '项目A'
        INTERSECT
        SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = '项目B'
        INTERSECT
        SELECT 结算单据号 FROM 医保住院结算明细 WHERE 医保项目名称 = '项目C'
    )
    SELECT P.结算单据号, U.医保项目名称, U.总数量, U.使用次数
    FROM patient_filter P
    JOIN item_usage U ON P.结算单据号 = U.结算单据号
    JOIN duplicate_items D ON P.结算单据号 = D.结算单据号
    """
    
    import time
    
    # 测试多次解析的平均时间
    times = []
    for i in range(5):
        start_time = time.time()
        result = parser.parse(complex_sql)
        end_time = time.time()
        
        if result.success:
            times.append(end_time - start_time)
        else:
            print(f"解析失败: {result.error_message}")
            return False
    
    avg_time = sum(times) / len(times)
    print(f"平均解析时间: {avg_time:.3f}秒")
    print(f"解析速度: {len(complex_sql)/avg_time:.0f} 字符/秒")
    
    # 检查最后一次解析的结果
    if result.success:
        rule_ir = result.rule_ir
        print(f"解析结果: {rule_ir.rule_type.value}")
        print(f"数据源: {len(rule_ir.data_sources)}, 条件: {len(rule_ir.conditions)}, 聚合: {len(rule_ir.aggregations)}")
    
    return avg_time < 1.0  # 期望在1秒内完成


def main():
    """主测试函数"""
    print("开始深度SQL解析器综合测试...\n")
    
    tests = [
        ("综合规则解析", test_comprehensive_rule_parsing),
        ("规则类型分类", test_rule_type_classification),
        ("解析性能", test_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print(f"\n综合测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 深度SQL解析器综合测试全部通过！")
        print("✨ 系统已准备好用于生产环境。")
    else:
        print("⚠️  存在失败的测试，建议进一步优化。")


if __name__ == "__main__":
    main()
