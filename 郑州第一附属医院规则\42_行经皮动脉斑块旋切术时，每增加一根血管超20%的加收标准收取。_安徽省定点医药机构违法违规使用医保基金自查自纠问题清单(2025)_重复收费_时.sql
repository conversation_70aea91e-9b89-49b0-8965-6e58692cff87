
-- ============================================================
-- 序号: 42
-- 规则名称: 合肥_住院_42_行经皮动脉斑块旋切术时，每增加一根血管超20%的加收标准收取。  
-- 城市: 合肥
-- 规则来源: 安徽省定点医药机构违法违规使用医保基金自查自纠问题清单(2025)
-- 行为认定: 超标准收费
-- 医保名称1: 经皮动脉斑块旋切术
-- 医保名称2(违规项): 经皮动脉斑块旋切术(每增加一根血管加收20%)
-- ============================================================
WITH tab1 AS (
  SELECT
    结算单据号,
    to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
  FROM
    医保住院结算明细 B
  WHERE
    B.医保项目名称 IN ('经皮动脉斑块旋切术') GROUP BY  结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') HAVING SUM(数量)>0
  INTERSECT
  SELECT
    结算单据号,
    to_char(项目使用日期,'yyyy-MM-dd hh24') 项目使用日期
  FROM
    医保住院结算明细 B
  WHERE
    (B.医保项目名称 LIKE '经皮动脉斑块旋切术(每增加一根血管加收20%)') GROUP BY  结算单据号, to_char(项目使用日期,'yyyy-MM-dd hh24') HAVING SUM(数量)>1
)
SELECT
  A.病案号,
  A.结算单据号,
  A.医疗机构编码,
  A.医疗机构名称,
  A.结算日期,
  A.住院号,
  A.个人编码,
  A.患者社会保障号码,
  A.身份证号,
  A.险种类型,
  A.入院科室,
  A.出院科室,
  A.主诊医师姓名,
  A.患者姓名,
  A.患者年龄,
	A.患者性别,
  A.异地标志,
  A.入院日期,
  A.出院日期,
  (A.出院日期 :: DATE) - (A.入院日期 :: DATE) + 1 AS 住院天数,
  A.医疗总费用,
  A.基本统筹支付,
  A.个人自付,
  A.个人自费,
  A.符合基本医疗保险的费用,
  A.入院诊断编码,
  A.入院诊断名称,
  A.出院诊断编码,
  A.出院诊断名称,
  A.主手术及操作编码,
  A.主手术及操作名称,
  A.其他手术及操作编码,
  A.其他手术及操作名称,
  B.开单科室名称,
  B.执行科室名称,
  B.开单医师姓名,
  B.费用类别,
  B.项目使用日期,
  B.医院项目编码,
  B.医院项目名称,
  B.医保项目编码,
  B.医保项目名称,
  B.支付类别,
  B.报销比例,
  B.自付比例,
  B.支付地点类别,
  B.记账流水号,
  B.规格,
  B.单价,
  B.数量,
  B.金额,
  B.医保范围内金额,
	CASE WHEN (B.医保项目名称 LIKE '经皮动脉斑块旋切术(每增加一根血管加收20%)') THEN B.数量 ELSE 0 END AS 使用总数量,
	CASE WHEN (B.医保项目名称 LIKE '经皮动脉斑块旋切术(每增加一根血管加收20%)') THEN B.金额 ELSE 0 END AS 使用总金额,
	CASE WHEN (B.医保项目名称 LIKE '经皮动脉斑块旋切术(每增加一根血管加收20%)') THEN B.数量 ELSE 0 END AS 违规数量,
	CASE WHEN (B.医保项目名称 LIKE '经皮动脉斑块旋切术(每增加一根血管加收20%)') THEN B.金额 ELSE 0 END AS 违规金额
FROM
  医保住院结算明细 B
  JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号
  JOIN tab1 C ON B.结算单据号 = C.结算单据号 AND C.项目使用日期 = to_char(B.项目使用日期,'yyyy-MM-dd hh24')
WHERE
  (B.医保项目名称 IN ('经皮动脉斑块旋切术') OR (B.医保项目名称 LIKE '经皮动脉斑块旋切术(每增加一根血管加收20%)'))
  and 1=1
  and 2=2
  and 3=3
  and 4=4    
ORDER BY
  A.患者姓名,
  B.项目使用日期
