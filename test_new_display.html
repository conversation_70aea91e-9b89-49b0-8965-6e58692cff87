<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试新的规则显示方式</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        /* 同规则ID分组样式 */
        .same-rule-group {
            position: relative;
        }

        /* 为同规则ID的记录添加左侧颜色条 */
        .same-rule-group::before {
            content: '';
            position: absolute;
            left: -3px;
            top: 0;
            bottom: 0;
            width: 4px;
            border-radius: 0 2px 2px 0;
        }

        /* 不同规则ID使用不同颜色 */
        .rule-color-1::before { background: linear-gradient(to bottom, #e3f2fd, #1976d2); }
        .rule-color-2::before { background: linear-gradient(to bottom, #f3e5f5, #7b1fa2); }
        .rule-color-3::before { background: linear-gradient(to bottom, #e8f5e8, #388e3c); }
        .rule-color-4::before { background: linear-gradient(to bottom, #fff3e0, #f57c00); }
        .rule-color-5::before { background: linear-gradient(to bottom, #fce4ec, #c2185b); }
        .rule-color-6::before { background: linear-gradient(to bottom, #e0f2f1, #00796b); }

        /* 同规则ID的背景色 */
        .rule-color-1 .card-body { background: linear-gradient(to right, rgba(25, 118, 210, 0.03), transparent); }
        .rule-color-2 .card-body { background: linear-gradient(to right, rgba(123, 31, 162, 0.03), transparent); }
        .rule-color-3 .card-body { background: linear-gradient(to right, rgba(56, 142, 60, 0.03), transparent); }
        .rule-color-4 .card-body { background: linear-gradient(to right, rgba(245, 124, 0, 0.03), transparent); }
        .rule-color-5 .card-body { background: linear-gradient(to right, rgba(194, 24, 91, 0.03), transparent); }
        .rule-color-6 .card-body { background: linear-gradient(to right, rgba(0, 121, 107, 0.03), transparent); }

        /* 已选择同规则ID其他记录的样式 */
        .rule-disabled {
            opacity: 0.6;
            background-color: #f8f9fa !important;
        }

        .rule-disabled .card-body {
            background: linear-gradient(to right, rgba(108, 117, 125, 0.1), transparent) !important;
        }

        /* 规则ID标识 */
        .rule-id-badge {
            font-size: 0.75rem;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 500;
        }

        /* 状态标签 */
        .rule-tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .tag-recommended { background-color: #e3f2fd; color: #1976d2; }
        .tag-adopted { background-color: #e8f5e8; color: #2e7d32; }
        .tag-ignored { background-color: #fafafa; color: #757575; }

        /* 匹配度样式 */
        .match-high { color: #2e7d32; font-weight: bold; }
        .match-medium { color: #f57c00; font-weight: bold; }
        .match-low { color: #d32f2f; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>测试新的规则显示方式</h2>
        <p class="text-muted">演示取消合并显示后的规则列表效果</p>
        
        <div class="row mb-3">
            <div class="col-md-6">
                <button class="btn btn-primary" onclick="loadTestData()">
                    <i class="bi bi-refresh"></i> 加载测试数据
                </button>
                <button class="btn btn-secondary ms-2" onclick="simulateSelection()">
                    <i class="bi bi-check-square"></i> 模拟选择
                </button>
            </div>
        </div>
        
        <div id="rulesContainer">
            <!-- 规则列表将显示在这里 -->
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 模拟测试数据
        const testRules = [
            {
                适用ID: 1001,
                规则ID: 101,
                规则名称: "门诊输液管理规则",
                行为认定: "超范围使用输液",
                匹配项目: "静脉输液",
                城市: "北京",
                规则来源: "国家医保局",
                规则内涵: "门诊患者不得随意使用静脉输液",
                匹配度: 0.95,
                状态: "推荐",
                规则ID分组序号: 1,
                规则ID分组总数: 3,
                是否多记录规则: true,
                颜色类名: "rule-color-1"
            },
            {
                适用ID: 1002,
                规则ID: 101,
                规则名称: "门诊输液管理规则",
                行为认定: "超范围使用输液",
                匹配项目: "输液器",
                城市: "上海",
                规则来源: "国家医保局",
                规则内涵: "门诊患者不得随意使用静脉输液",
                匹配度: 0.88,
                状态: "推荐",
                规则ID分组序号: 2,
                规则ID分组总数: 3,
                是否多记录规则: true,
                颜色类名: "rule-color-1"
            },
            {
                适用ID: 1003,
                规则ID: 101,
                规则名称: "门诊输液管理规则",
                行为认定: "超范围使用输液",
                匹配项目: "输液费",
                城市: "广州",
                规则来源: "国家医保局",
                规则内涵: "门诊患者不得随意使用静脉输液",
                匹配度: 0.82,
                状态: "推荐",
                规则ID分组序号: 3,
                规则ID分组总数: 3,
                是否多记录规则: true,
                颜色类名: "rule-color-1"
            },
            {
                适用ID: 1004,
                规则ID: 102,
                规则名称: "药品使用监管规则",
                行为认定: "重复用药",
                匹配项目: "阿莫西林",
                城市: "深圳",
                规则来源: "省医保局",
                规则内涵: "避免同类药品重复使用",
                匹配度: 0.91,
                状态: "推荐",
                规则ID分组序号: 1,
                规则ID分组总数: 2,
                是否多记录规则: true,
                颜色类名: "rule-color-2"
            },
            {
                适用ID: 1005,
                规则ID: 102,
                规则名称: "药品使用监管规则",
                行为认定: "重复用药",
                匹配项目: "头孢类抗生素",
                城市: "杭州",
                规则来源: "省医保局",
                规则内涵: "避免同类药品重复使用",
                匹配度: 0.86,
                状态: "已采用",
                规则ID分组序号: 2,
                规则ID分组总数: 2,
                是否多记录规则: true,
                颜色类名: "rule-color-2"
            },
            {
                适用ID: 1006,
                规则ID: 103,
                规则名称: "检查项目合理性规则",
                行为认定: "过度检查",
                匹配项目: "CT检查",
                城市: "南京",
                规则来源: "市医保局",
                规则内涵: "避免不必要的重复检查",
                匹配度: 0.78,
                状态: "推荐",
                规则ID分组序号: 1,
                规则ID分组总数: 1,
                是否多记录规则: false,
                颜色类名: ""
            }
        ];

        let selectedRules = new Set();

        function loadTestData() {
            renderRules(testRules);
        }

        function renderRules(rules) {
            let html = '<div class="row">';
            
            rules.forEach((rec, index) => {
                const matchClass = rec.匹配度 >= 0.8 ? 'match-high' : rec.匹配度 >= 0.5 ? 'match-medium' : 'match-low';
                const statusClass = rec.状态 === '已采用' ? 'tag-adopted' : rec.状态 === '已忽略' ? 'tag-ignored' : 'tag-recommended';

                // 检查是否为同规则ID的多条记录
                const isSameRuleGroup = rec.是否多记录规则;
                const ruleGroupClass = isSameRuleGroup ? `same-rule-group ${rec.颜色类名}` : '';
                
                // 检查该规则ID是否已有其他记录被选中（防重复选择）
                const isRuleDisabled = checkRuleIdDisabled(rec.规则ID, rec.适用ID);
                const disabledClass = isRuleDisabled ? 'rule-disabled' : '';
                
                // 生成规则ID标识
                const ruleIdBadge = isSameRuleGroup ? 
                    `<span class="rule-id-badge bg-secondary text-white">规则ID: ${rec.规则ID} (${rec.规则ID分组序号}/${rec.规则ID分组总数})</span>` : 
                    `<span class="rule-id-badge bg-light text-dark">规则ID: ${rec.规则ID}</span>`;

                html += `
                    <div class="col-md-12 mb-2" data-rule-id="${rec.规则ID}" data-record-id="${rec.适用ID}">
                        <div class="card rule-card ${ruleGroupClass} ${disabledClass}" data-rule-id="${rec.适用ID}">
                            <div class="card-body py-2">
                                <div class="row align-items-center">
                                    <div class="col-md-1">
                                        ${rec.状态 === '推荐' || rec.状态 === '已采用' ? `
                                            <input type="checkbox" class="form-check-input rule-checkbox"
                                                   value="${rec.适用ID}" onchange="updateSelection()" ${isRuleDisabled ? 'disabled' : ''}>
                                        ` : ''}
                                        <span class="rule-tag ${statusClass} ms-2">${rec.状态}</span>
                                        <br>${ruleIdBadge}
                                    </div>
                                    <div class="col-md-4">
                                        <h6 class="mb-1 fw-bold">
                                            ${rec.规则名称}
                                        </h6>
                                        <small class="text-muted">
                                            <i class="bi bi-geo-alt"></i> ${rec.城市 || '未知'} |
                                            <i class="bi bi-bookmark"></i> ${rec.规则来源 || '未知'}
                                        </small>
                                        <br>
                                        <small class="text-info">
                                            <i class="bi bi-tag"></i> <strong>匹配项目:</strong> ${rec.匹配项目 || '未知'}
                                        </small>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="mb-1 small"><strong class="text-success">行为认定：</strong>${rec.行为认定}</p>
                                        <p class="mb-0 small text-muted" style="line-height: 1.3;">
                                            <strong class="text-primary">规则内涵：</strong>${rec.规则内涵 || '未设置'}
                                        </p>
                                    </div>
                                    <div class="col-md-1 text-center">
                                        <span class="match-score ${matchClass} h6">${(rec.匹配度 * 100).toFixed(1)}%</span>
                                        <br>
                                        <small class="text-muted">匹配度</small>
                                    </div>
                                    <div class="col-md-2 text-end">
                                        <div class="btn-group btn-group-sm">
                                            ${rec.状态 === '推荐' ? `
                                                <button class="btn btn-outline-success btn-sm" onclick="adoptRule(${rec.适用ID})" title="采用" ${isRuleDisabled ? 'disabled' : ''}>
                                                    <i class="bi bi-check"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="ignoreRule(${rec.适用ID})" title="忽略" ${isRuleDisabled ? 'disabled' : ''}>
                                                    <i class="bi bi-x"></i>
                                                </button>
                                            ` : rec.状态 === '已采用' ? `
                                                <button class="btn btn-outline-warning btn-sm" onclick="ignoreRule(${rec.适用ID})" title="取消采用">
                                                    <i class="bi bi-x-circle"></i>
                                                </button>
                                            ` : ''}
                                            <button class="btn btn-outline-info btn-sm" onclick="viewRuleDetail(${rec.规则ID})" title="查看详情">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        ${isRuleDisabled ? `<br><small class="text-muted">该规则ID已有记录被选择</small>` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            document.getElementById('rulesContainer').innerHTML = html;
        }

        // 检查规则ID是否应该被禁用（防重复选择）
        function checkRuleIdDisabled(ruleId, currentRecordId) {
            const selectedCheckboxes = document.querySelectorAll('.rule-checkbox:checked');
            for (let checkbox of selectedCheckboxes) {
                const recordId = checkbox.value;
                if (recordId !== currentRecordId.toString()) {
                    const record = testRules.find(r => r.适用ID == recordId);
                    if (record && record.规则ID == ruleId) {
                        return true;
                    }
                }
            }
            return false;
        }

        // 更新同规则ID记录的禁用状态
        function updateSameRuleIdStatus() {
            const selectedCheckboxes = document.querySelectorAll('.rule-checkbox:checked');
            const selectedRuleIds = new Set();
            
            selectedCheckboxes.forEach(checkbox => {
                const recordId = checkbox.value;
                const record = testRules.find(r => r.适用ID == recordId);
                if (record) {
                    selectedRuleIds.add(record.规则ID);
                }
            });

            document.querySelectorAll('[data-rule-id]').forEach(element => {
                const ruleId = parseInt(element.getAttribute('data-rule-id'));
                const recordId = element.getAttribute('data-record-id');
                
                if (ruleId && recordId) {
                    const record = testRules.find(r => r.适用ID == recordId);
                    if (record && record.是否多记录规则) {
                        const shouldDisable = selectedRuleIds.has(record.规则ID) && 
                                            !document.querySelector(`.rule-checkbox[value="${recordId}"]:checked`);
                        
                        const card = element.querySelector('.rule-card');
                        if (shouldDisable) {
                            card.classList.add('rule-disabled');
                        } else {
                            card.classList.remove('rule-disabled');
                        }
                        
                        const checkbox = element.querySelector('.rule-checkbox');
                        const buttons = element.querySelectorAll('.btn-outline-success, .btn-outline-secondary');
                        
                        if (checkbox) checkbox.disabled = shouldDisable;
                        buttons.forEach(btn => btn.disabled = shouldDisable);
                    }
                }
            });
        }

        function updateSelection() {
            updateSameRuleIdStatus();
            
            const checkboxes = document.querySelectorAll('.rule-checkbox:checked');
            selectedRules.clear();
            checkboxes.forEach(cb => selectedRules.add(cb.value));
            
            console.log('已选择规则:', Array.from(selectedRules));
        }

        function simulateSelection() {
            // 模拟选择第一条规则
            const firstCheckbox = document.querySelector('.rule-checkbox');
            if (firstCheckbox) {
                firstCheckbox.checked = true;
                updateSelection();
            }
        }

        function adoptRule(id) {
            alert(`采用规则 ${id}`);
        }

        function ignoreRule(id) {
            alert(`忽略规则 ${id}`);
        }

        function viewRuleDetail(id) {
            alert(`查看规则详情 ${id}`);
        }

        // 页面加载时自动加载测试数据
        document.addEventListener('DOMContentLoaded', function() {
            loadTestData();
        });
    </script>
</body>
</html>
