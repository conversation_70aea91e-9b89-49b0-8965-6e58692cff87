WITH tab1 AS(
SELECT 
    结算单据号, 
    医保项目编码, 
    医保项目名称,
    医院项目编码,
    医院项目名称,
    报销比例,
    单价,
    规格,
    费用类别,
    支付类别,
    sum(数量) AS 使用数量
FROM
  ZYMX
WHERE
  医保项目名称 in ({医保名称1})
GROUP BY 
    结算单据号, 
    医保项目编码, 
    医保项目名称,
    医院项目编码,
    医院项目名称,
    报销比例,
    单价,
    规格,
    费用类别,
    支付类别
)
SELECT
  A.病案号,
  A.结算单据号,
  A.医疗机构编码,
  A.医疗机构名称,
  A.结算日期,
  A.住院号,
  A.个人编码,
  A.患者社会保障号码,
  A.身份证号,
  A.险种类型,
  A.入院科室,
  A.出院科室,
  A.主诊医师姓名,
  A.患者姓名,
  A.患者年龄,
  A.异地标志,
  A.入院日期,
  A.出院日期,
  DATEDIFF(a.出院日期,a.入院日期,'DAY') +1 as 住院天数,
  A.医疗总费用,
  A.基本统筹支付,
  <PERSON><PERSON>个人自付,
  A.个人自费,
  A.符合基本医疗保险的费用,
  A.入院诊断编码,
  A.入院诊断名称,
  A.出院诊断编码,
  A.出院诊断名称,
  A.主手术及操作编码,
  A.主手术及操作名称,
  A.其他手术及操作编码,
  A.其他手术及操作名称,
  A.支付地点类别,
  b.医保项目编码,
  b.医保项目名称,
  b.医院项目编码,
  b.医院项目名称,
  b.报销比例,
  b.费用类别,
  b.支付类别,
  b.使用数量,
  b.单价,
  b.规格,
  (b.使用数量 - ceil((( DATEDIFF(a.出院日期,a.入院日期,'DAY') +1))/7) * {违规数量}) AS 超出数量,
  (b.使用数量 - ceil((( DATEDIFF(a.出院日期,a.入院日期,'DAY') +1))/7) * {违规数量}) * b.单价 AS 超出金额
FROM
  ZYZD a
JOIN tab1 B ON
  a.结算单据号 = b.结算单据号
  AND b.使用数量 > ceil((( DATEDIFF(a.出院日期,a.入院日期,'DAY') +1))/7) * {违规数量}
and b.医保项目名称 in ({医保名称1})
