/*院端筛选性别为男、开单科室为精神科的'412503100010000-250310053','00**********000-250310037','00**********000-**********','00**********000-250310004','00**********000-**********','00**********000-250310005','00**********000-**********'项目，
*/
SELECT
A.BRIDGE_ID 病案关联字段,
A.HISID 结算单据号,
A.PATIENT_GENDER 性别,
A.DISCHARGE_DEPT_NAME 出院科室名称,
A.DISCHARGE_DATE  出院日期,
A.ZYTS  住院天数,
C.bn 开单科室名称,
C.ITEM_ID 医保项目编码,
C.ITEM_NAME 医保项目名称,
C.NUM1 总数量,
C.COST1 总金额,
C.BMI_CONVERED_AMOUNT 总医保范围内金额,
diag.zd 出院诊断集合   
 FROM
  (SELECT
  B.HISID  AS  HISID,
  B.ITEM_ID AS ITEM_ID,
  B.ITEM_NAME AS ITEM_NAME,
  B.Billing_Dept_Name AS bn,
  sum(B.NUM)  AS NUM1 ,
  sum(B.COST) AS COST1,
  sum(B.BMI_CONVERED_AMOUNT) AS BMI_CONVERED_AMOUNT
FROM
    住院结算明细_全   B 
WHERE B.ITEM_ID  in ('412503100010000-250310053','00**********000-250310037','00**********000-**********','00**********000-250310004','00**********000-**********','00**********000-250310005','00**********000-**********')  
group  by 
  B.HISID  ,
  B.ITEM_ID ,
  B.Billing_Dept_Name,
  B.ITEM_NAME  ) C
JOIN  住院结算主单_全  A  ON  C.HISID = A.HISID
JOIN   zhenduan diag ON diag.HISID =A.HISID 
/*
where  A.HISID   IN ( 
 SELECT DISTINCT  HISID   FROM  zhenduan 
 WHERE  
 zd  not like '%骨髓%'
 )   */
where C.bn like '%精神%' and A.PATIENT_GENDER='男' 