WITH tab1 AS (
SELECT
	/*+PARALLEL(8)*/
	结算单据号 || to_char(项目使用日期, 'yyyy-mm-dd') guanlian
FROM
	ZZS_YY_ZDYFY_4NG.医保住院结算明细
WHERE
	医保项目编码 IN ('C14080518600000043890000002')
	AND 结算单据号 || to_char(项目使用日期, 'yyyy-mm-dd') IN (
	SELECT
		结算单据号 || to_char(项目使用日期, 'yyyy-mm-dd')
	FROM
		ZZS_YY_ZDYFY_4NG.医保住院结算明细
	WHERE
		医保项目编码 IN ('003107020040000-320400006',
'003205000030000-320500003',
'003206000070000-320600007',
'003206000040000-320600004',
'003201000070000-320100007',
'003206000090000-320600009',
'413204000040000-320400005',
'003201000030000-320100003',
'003206000010000-320600001',
'003205000090000-320500009',
'413206000120000-320600012',
'003206000100000-320600010',
'003206000010000-320600001',
'003206000070000-320600007',
'003201000030000-320100003',
'003205000070000-320500018',
'003201000030000-320100003',
'003206000020000-320600002',
'003205000090000-320500009',
'003205000070000-320500007',
'003202000020000-320200002',
'003206000010000-320600001',
'003205000010000-320500001',
'003202000090000-320200009',
'413200000110000-321000010',
'003201000050000-320100005',
'003205000080000-320500008',
'003205000100000-320500010',
'003201000030000-320100003',
'003202000120000-320200012',
'003201000030000-320100003',
'003201000060000-320100006',
'003205000010000-320500001',
'003206000010000-320600001',
'003202000080000-320200008',
'003205000070000-320500007',
'003205000070000-320500018',
'003205000010000-320500001',
'003205000050000-3205000050',
'003202000080000-320200016',
'003201000040000-320100004',
'003204000030000-320400003',
'003205000030000-320500003',
'003205000080000-320500008',
'003308040750000-320100014',
'003204000010000-320400001',
'003202000030000-320200003',
'003202000100000-320200015',
'003205000100000-320500010',
'003206000030000-320600003',
'003201000040000-320100004',
'003202000040300-320200013',
'003202000050000-320200005',
'003206000080000-320600008',
'003206000010000-320600001',
'003202000130000-320100008',
'003201000050000-320100005',
'003202000010000-320200001',
'003202000080000-320200016',
'413206000170000-320600013',
'003202000010000-320200001',
'003206000080000-320600008',
'003205000150000-320500015',
'003205000160000-320500016',
'413308020490000-320400004',
'003205000020000-320500002',
'003201000090000-320100009',
'003202000050000-320200005',
'003202000100000-320200015',
'003206000080000-320600008',
'003205000110000-320500011',
'003202000050000-320200005',
'003202000110000-320200011',
'003202000040300-320200013',
'003202000040300-320200013',
'003202000100000-320200010',
'003205000030000-320500003',
'003206000080000-320600008',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000070000-320500007',
'003202000030000-320200003',
'003206000050000-320600005',
'003204000020000-320400002',
'003205000110000-320500011',
'003205000090000-320500009',
'003201000090000-320100002',
'003202000020000-320200002',
'003205000070000-320500018',
'003202000040000-320200004',
'003202000020000-320200002',
'003308040750000-320100015',
'003204000010000-320400001',
'003205000010000-320500001',
'003201000060000-320100006',
'003206000010000-320600001',
'003201000030000-320100003',
'003202000100000-320200010',
'003201000040000-320100004',
'003205000020000-320500002',
'003206000090000-320600009',
'003202000120000-320200012',
'003201000030000-320100003',
'003203000010000-320300001',
'003201000010000-320100001',
'003202000100000-320200010',
'003203000010000-320300001',
'003201000060000-320100006',
'003201000010000-320500017',
'003205000030000-320500003',
'003206000040000-320600004',
'003201000040000-320100004',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000030000-320500003',
'003205000050000-320500005',
'003205000030000-320500003',
'003202000040000-320200004',
'003204000030000-320400003',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000060000-320600006',
'003202000070000-320200007',
'003107020040000-320400006',
'003202000020000-320200002',
'413200000110000-321000010',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000030000-320600003',
'003205000130000-320500013',
'003206000040000-320600004',
'003206000110000-320600011',
'003206000010000-320600001',
'003202000130000-320100008',
'003205000020000-320500002',
'003202000030000-320200003',
'003205000090000-320500009',
'003202000070000-320200007',
'003205000020000-320500002',
'003203000030000-320300003',
'003206000030000-320600003',
'003202000130000-320100008',
'003202000040000-320200004',
'003202000030000-320200003',
'003201000010000-320100001',
'003205000060000-320500006',
'003202000090000-320200009',
'003202000040300-320200013',
'003206000100000-320600010',
'003205000030000-320500003')
	GROUP BY
		结算单据号 || to_char(项目使用日期, 'yyyy-mm-dd')
	HAVING
		SUM(数量) > 0
    )
GROUP BY
	结算单据号 || to_char(项目使用日期, 'yyyy-mm-dd')
HAVING
	SUM(数量) > 0
)
SELECT
	/*+PARALLEL(8)*/
	A.病案号,
	A.结算单据号 ,
	A.医疗机构编码 ,
	A.医疗机构名称 ,
	A.结算日期 ,
	A.住院号 ,
	A.个人编码 ,
	A.患者社会保障号码 ,
	A.身份证号 ,
	A.险种类型 ,
	A.入院科室 ,
	A.出院科室,
	A.主诊医师姓名 ,
	A.患者姓名 ,
	A.患者年龄 ,
	A.患者性别 ,
	A.异地标志,
	A.入院日期,
	A.出院日期 ,
	A.医疗总费用 ,
	A.基本统筹支付 ,
	A.个人自付 ,
	A.个人自费 ,
	A.符合基本医疗保险的费用 ,
	A.入院诊断编码 ,
	A.入院诊断名称 ,
	A.出院诊断编码 ,
	A.出院诊断名称 ,
	A.主手术及操作编码 ,
	A.主手术及操作名称 ,
	A.其他手术及操作编码 ,
	A.其他手术及操作名称 ,
	B.开单科室名称 ,
	B.执行科室名称 ,
	B.开单医师姓名 ,
	B.费用类别 ,
	B.项目使用日期 ,
	B.医院项目编码 ,
	B.医院项目名称 ,
	B.医保项目编码 ,
	B.医保项目名称 ,
	B.支付类别 ,
	B.报销比例 ,
	B.自付比例 ,
	B.支付地点类别 ,
	B.规格 ,
	B.单价 ,
	B.数量 ,
	B.金额 ,
	B.医保范围内金额 ,													
	CASE
		WHEN B.医保项目编码 IN ('003107020040000-320400006',
'003205000030000-320500003',
'003206000070000-320600007',
'003206000040000-320600004',
'003201000070000-320100007',
'003206000090000-320600009',
'413204000040000-320400005',
'003201000030000-320100003',
'003206000010000-320600001',
'003205000090000-320500009',
'413206000120000-320600012',
'003206000100000-320600010',
'003206000010000-320600001',
'003206000070000-320600007',
'003201000030000-320100003',
'003205000070000-320500018',
'003201000030000-320100003',
'003206000020000-320600002',
'003205000090000-320500009',
'003205000070000-320500007',
'003202000020000-320200002',
'003206000010000-320600001',
'003205000010000-320500001',
'003202000090000-320200009',
'413200000110000-321000010',
'003201000050000-320100005',
'003205000080000-320500008',
'003205000100000-320500010',
'003201000030000-320100003',
'003202000120000-320200012',
'003201000030000-320100003',
'003201000060000-320100006',
'003205000010000-320500001',
'003206000010000-320600001',
'003202000080000-320200008',
'003205000070000-320500007',
'003205000070000-320500018',
'003205000010000-320500001',
'003205000050000-3205000050',
'003202000080000-320200016',
'003201000040000-320100004',
'003204000030000-320400003',
'003205000030000-320500003',
'003205000080000-320500008',
'003308040750000-320100014',
'003204000010000-320400001',
'003202000030000-320200003',
'003202000100000-320200015',
'003205000100000-320500010',
'003206000030000-320600003',
'003201000040000-320100004',
'003202000040300-320200013',
'003202000050000-320200005',
'003206000080000-320600008',
'003206000010000-320600001',
'003202000130000-320100008',
'003201000050000-320100005',
'003202000010000-320200001',
'003202000080000-320200016',
'413206000170000-320600013',
'003202000010000-320200001',
'003206000080000-320600008',
'003205000150000-320500015',
'003205000160000-320500016',
'413308020490000-320400004',
'003205000020000-320500002',
'003201000090000-320100009',
'003202000050000-320200005',
'003202000100000-320200015',
'003206000080000-320600008',
'003205000110000-320500011',
'003202000050000-320200005',
'003202000110000-320200011',
'003202000040300-320200013',
'003202000040300-320200013',
'003202000100000-320200010',
'003205000030000-320500003',
'003206000080000-320600008',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000070000-320500007',
'003202000030000-320200003',
'003206000050000-320600005',
'003204000020000-320400002',
'003205000110000-320500011',
'003205000090000-320500009',
'003201000090000-320100002',
'003202000020000-320200002',
'003205000070000-320500018',
'003202000040000-320200004',
'003202000020000-320200002',
'003308040750000-320100015',
'003204000010000-320400001',
'003205000010000-320500001',
'003201000060000-320100006',
'003206000010000-320600001',
'003201000030000-320100003',
'003202000100000-320200010',
'003201000040000-320100004',
'003205000020000-320500002',
'003206000090000-320600009',
'003202000120000-320200012',
'003201000030000-320100003',
'003203000010000-320300001',
'003201000010000-320100001',
'003202000100000-320200010',
'003203000010000-320300001',
'003201000060000-320100006',
'003201000010000-320500017',
'003205000030000-320500003',
'003206000040000-320600004',
'003201000040000-320100004',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000030000-320500003',
'003205000050000-320500005',
'003205000030000-320500003',
'003202000040000-320200004',
'003204000030000-320400003',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000060000-320600006',
'003202000070000-320200007',
'003107020040000-320400006',
'003202000020000-320200002',
'413200000110000-321000010',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000030000-320600003',
'003205000130000-320500013',
'003206000040000-320600004',
'003206000110000-320600011',
'003206000010000-320600001',
'003202000130000-320100008',
'003205000020000-320500002',
'003202000030000-320200003',
'003205000090000-320500009',
'003202000070000-320200007',
'003205000020000-320500002',
'003203000030000-320300003',
'003206000030000-320600003',
'003202000130000-320100008',
'003202000040000-320200004',
'003202000030000-320200003',
'003201000010000-320100001',
'003205000060000-320500006',
'003202000090000-320200009',
'003202000040300-320200013',
'003206000100000-320600010',
'003205000030000-320500003') THEN B.数量
		ELSE 0
	END AS 使用总数量,
	CASE
		WHEN B.医保项目编码 IN ('003107020040000-320400006',
'003205000030000-320500003',
'003206000070000-320600007',
'003206000040000-320600004',
'003201000070000-320100007',
'003206000090000-320600009',
'413204000040000-320400005',
'003201000030000-320100003',
'003206000010000-320600001',
'003205000090000-320500009',
'413206000120000-320600012',
'003206000100000-320600010',
'003206000010000-320600001',
'003206000070000-320600007',
'003201000030000-320100003',
'003205000070000-320500018',
'003201000030000-320100003',
'003206000020000-320600002',
'003205000090000-320500009',
'003205000070000-320500007',
'003202000020000-320200002',
'003206000010000-320600001',
'003205000010000-320500001',
'003202000090000-320200009',
'413200000110000-321000010',
'003201000050000-320100005',
'003205000080000-320500008',
'003205000100000-320500010',
'003201000030000-320100003',
'003202000120000-320200012',
'003201000030000-320100003',
'003201000060000-320100006',
'003205000010000-320500001',
'003206000010000-320600001',
'003202000080000-320200008',
'003205000070000-320500007',
'003205000070000-320500018',
'003205000010000-320500001',
'003205000050000-3205000050',
'003202000080000-320200016',
'003201000040000-320100004',
'003204000030000-320400003',
'003205000030000-320500003',
'003205000080000-320500008',
'003308040750000-320100014',
'003204000010000-320400001',
'003202000030000-320200003',
'003202000100000-320200015',
'003205000100000-320500010',
'003206000030000-320600003',
'003201000040000-320100004',
'003202000040300-320200013',
'003202000050000-320200005',
'003206000080000-320600008',
'003206000010000-320600001',
'003202000130000-320100008',
'003201000050000-320100005',
'003202000010000-320200001',
'003202000080000-320200016',
'413206000170000-320600013',
'003202000010000-320200001',
'003206000080000-320600008',
'003205000150000-320500015',
'003205000160000-320500016',
'413308020490000-320400004',
'003205000020000-320500002',
'003201000090000-320100009',
'003202000050000-320200005',
'003202000100000-320200015',
'003206000080000-320600008',
'003205000110000-320500011',
'003202000050000-320200005',
'003202000110000-320200011',
'003202000040300-320200013',
'003202000040300-320200013',
'003202000100000-320200010',
'003205000030000-320500003',
'003206000080000-320600008',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000070000-320500007',
'003202000030000-320200003',
'003206000050000-320600005',
'003204000020000-320400002',
'003205000110000-320500011',
'003205000090000-320500009',
'003201000090000-320100002',
'003202000020000-320200002',
'003205000070000-320500018',
'003202000040000-320200004',
'003202000020000-320200002',
'003308040750000-320100015',
'003204000010000-320400001',
'003205000010000-320500001',
'003201000060000-320100006',
'003206000010000-320600001',
'003201000030000-320100003',
'003202000100000-320200010',
'003201000040000-320100004',
'003205000020000-320500002',
'003206000090000-320600009',
'003202000120000-320200012',
'003201000030000-320100003',
'003203000010000-320300001',
'003201000010000-320100001',
'003202000100000-320200010',
'003203000010000-320300001',
'003201000060000-320100006',
'003201000010000-320500017',
'003205000030000-320500003',
'003206000040000-320600004',
'003201000040000-320100004',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000030000-320500003',
'003205000050000-320500005',
'003205000030000-320500003',
'003202000040000-320200004',
'003204000030000-320400003',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000060000-320600006',
'003202000070000-320200007',
'003107020040000-320400006',
'003202000020000-320200002',
'413200000110000-321000010',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000030000-320600003',
'003205000130000-320500013',
'003206000040000-320600004',
'003206000110000-320600011',
'003206000010000-320600001',
'003202000130000-320100008',
'003205000020000-320500002',
'003202000030000-320200003',
'003205000090000-320500009',
'003202000070000-320200007',
'003205000020000-320500002',
'003203000030000-320300003',
'003206000030000-320600003',
'003202000130000-320100008',
'003202000040000-320200004',
'003202000030000-320200003',
'003201000010000-320100001',
'003205000060000-320500006',
'003202000090000-320200009',
'003202000040300-320200013',
'003206000100000-320600010',
'003205000030000-320500003') THEN B.金额
		ELSE 0
	END AS 使用总金额,
	CASE
		WHEN B.医保项目编码 IN ('003107020040000-320400006',
'003205000030000-320500003',
'003206000070000-320600007',
'003206000040000-320600004',
'003201000070000-320100007',
'003206000090000-320600009',
'413204000040000-320400005',
'003201000030000-320100003',
'003206000010000-320600001',
'003205000090000-320500009',
'413206000120000-320600012',
'003206000100000-320600010',
'003206000010000-320600001',
'003206000070000-320600007',
'003201000030000-320100003',
'003205000070000-320500018',
'003201000030000-320100003',
'003206000020000-320600002',
'003205000090000-320500009',
'003205000070000-320500007',
'003202000020000-320200002',
'003206000010000-320600001',
'003205000010000-320500001',
'003202000090000-320200009',
'413200000110000-321000010',
'003201000050000-320100005',
'003205000080000-320500008',
'003205000100000-320500010',
'003201000030000-320100003',
'003202000120000-320200012',
'003201000030000-320100003',
'003201000060000-320100006',
'003205000010000-320500001',
'003206000010000-320600001',
'003202000080000-320200008',
'003205000070000-320500007',
'003205000070000-320500018',
'003205000010000-320500001',
'003205000050000-3205000050',
'003202000080000-320200016',
'003201000040000-320100004',
'003204000030000-320400003',
'003205000030000-320500003',
'003205000080000-320500008',
'003308040750000-320100014',
'003204000010000-320400001',
'003202000030000-320200003',
'003202000100000-320200015',
'003205000100000-320500010',
'003206000030000-320600003',
'003201000040000-320100004',
'003202000040300-320200013',
'003202000050000-320200005',
'003206000080000-320600008',
'003206000010000-320600001',
'003202000130000-320100008',
'003201000050000-320100005',
'003202000010000-320200001',
'003202000080000-320200016',
'413206000170000-320600013',
'003202000010000-320200001',
'003206000080000-320600008',
'003205000150000-320500015',
'003205000160000-320500016',
'413308020490000-320400004',
'003205000020000-320500002',
'003201000090000-320100009',
'003202000050000-320200005',
'003202000100000-320200015',
'003206000080000-320600008',
'003205000110000-320500011',
'003202000050000-320200005',
'003202000110000-320200011',
'003202000040300-320200013',
'003202000040300-320200013',
'003202000100000-320200010',
'003205000030000-320500003',
'003206000080000-320600008',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000070000-320500007',
'003202000030000-320200003',
'003206000050000-320600005',
'003204000020000-320400002',
'003205000110000-320500011',
'003205000090000-320500009',
'003201000090000-320100002',
'003202000020000-320200002',
'003205000070000-320500018',
'003202000040000-320200004',
'003202000020000-320200002',
'003308040750000-320100015',
'003204000010000-320400001',
'003205000010000-320500001',
'003201000060000-320100006',
'003206000010000-320600001',
'003201000030000-320100003',
'003202000100000-320200010',
'003201000040000-320100004',
'003205000020000-320500002',
'003206000090000-320600009',
'003202000120000-320200012',
'003201000030000-320100003',
'003203000010000-320300001',
'003201000010000-320100001',
'003202000100000-320200010',
'003203000010000-320300001',
'003201000060000-320100006',
'003201000010000-320500017',
'003205000030000-320500003',
'003206000040000-320600004',
'003201000040000-320100004',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000030000-320500003',
'003205000050000-320500005',
'003205000030000-320500003',
'003202000040000-320200004',
'003204000030000-320400003',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000060000-320600006',
'003202000070000-320200007',
'003107020040000-320400006',
'003202000020000-320200002',
'413200000110000-321000010',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000030000-320600003',
'003205000130000-320500013',
'003206000040000-320600004',
'003206000110000-320600011',
'003206000010000-320600001',
'003202000130000-320100008',
'003205000020000-320500002',
'003202000030000-320200003',
'003205000090000-320500009',
'003202000070000-320200007',
'003205000020000-320500002',
'003203000030000-320300003',
'003206000030000-320600003',
'003202000130000-320100008',
'003202000040000-320200004',
'003202000030000-320200003',
'003201000010000-320100001',
'003205000060000-320500006',
'003202000090000-320200009',
'003202000040300-320200013',
'003206000100000-320600010',
'003205000030000-320500003') THEN B.数量
		ELSE 0
	END AS 违规数量,
	CASE
		WHEN B.医保项目编码 IN ('003107020040000-320400006',
'003205000030000-320500003',
'003206000070000-320600007',
'003206000040000-320600004',
'003201000070000-320100007',
'003206000090000-320600009',
'413204000040000-320400005',
'003201000030000-320100003',
'003206000010000-320600001',
'003205000090000-320500009',
'413206000120000-320600012',
'003206000100000-320600010',
'003206000010000-320600001',
'003206000070000-320600007',
'003201000030000-320100003',
'003205000070000-320500018',
'003201000030000-320100003',
'003206000020000-320600002',
'003205000090000-320500009',
'003205000070000-320500007',
'003202000020000-320200002',
'003206000010000-320600001',
'003205000010000-320500001',
'003202000090000-320200009',
'413200000110000-321000010',
'003201000050000-320100005',
'003205000080000-320500008',
'003205000100000-320500010',
'003201000030000-320100003',
'003202000120000-320200012',
'003201000030000-320100003',
'003201000060000-320100006',
'003205000010000-320500001',
'003206000010000-320600001',
'003202000080000-320200008',
'003205000070000-320500007',
'003205000070000-320500018',
'003205000010000-320500001',
'003205000050000-3205000050',
'003202000080000-320200016',
'003201000040000-320100004',
'003204000030000-320400003',
'003205000030000-320500003',
'003205000080000-320500008',
'003308040750000-320100014',
'003204000010000-320400001',
'003202000030000-320200003',
'003202000100000-320200015',
'003205000100000-320500010',
'003206000030000-320600003',
'003201000040000-320100004',
'003202000040300-320200013',
'003202000050000-320200005',
'003206000080000-320600008',
'003206000010000-320600001',
'003202000130000-320100008',
'003201000050000-320100005',
'003202000010000-320200001',
'003202000080000-320200016',
'413206000170000-320600013',
'003202000010000-320200001',
'003206000080000-320600008',
'003205000150000-320500015',
'003205000160000-320500016',
'413308020490000-320400004',
'003205000020000-320500002',
'003201000090000-320100009',
'003202000050000-320200005',
'003202000100000-320200015',
'003206000080000-320600008',
'003205000110000-320500011',
'003202000050000-320200005',
'003202000110000-320200011',
'003202000040300-320200013',
'003202000040300-320200013',
'003202000100000-320200010',
'003205000030000-320500003',
'003206000080000-320600008',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000070000-320500007',
'003202000030000-320200003',
'003206000050000-320600005',
'003204000020000-320400002',
'003205000110000-320500011',
'003205000090000-320500009',
'003201000090000-320100002',
'003202000020000-320200002',
'003205000070000-320500018',
'003202000040000-320200004',
'003202000020000-320200002',
'003308040750000-320100015',
'003204000010000-320400001',
'003205000010000-320500001',
'003201000060000-320100006',
'003206000010000-320600001',
'003201000030000-320100003',
'003202000100000-320200010',
'003201000040000-320100004',
'003205000020000-320500002',
'003206000090000-320600009',
'003202000120000-320200012',
'003201000030000-320100003',
'003203000010000-320300001',
'003201000010000-320100001',
'003202000100000-320200010',
'003203000010000-320300001',
'003201000060000-320100006',
'003201000010000-320500017',
'003205000030000-320500003',
'003206000040000-320600004',
'003201000040000-320100004',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000030000-320500003',
'003205000050000-320500005',
'003205000030000-320500003',
'003202000040000-320200004',
'003204000030000-320400003',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000060000-320600006',
'003202000070000-320200007',
'003107020040000-320400006',
'003202000020000-320200002',
'413200000110000-321000010',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000030000-320600003',
'003205000130000-320500013',
'003206000040000-320600004',
'003206000110000-320600011',
'003206000010000-320600001',
'003202000130000-320100008',
'003205000020000-320500002',
'003202000030000-320200003',
'003205000090000-320500009',
'003202000070000-320200007',
'003205000020000-320500002',
'003203000030000-320300003',
'003206000030000-320600003',
'003202000130000-320100008',
'003202000040000-320200004',
'003202000030000-320200003',
'003201000010000-320100001',
'003205000060000-320500006',
'003202000090000-320200009',
'003202000040300-320200013',
'003206000100000-320600010',
'003205000030000-320500003') THEN B.金额
		ELSE 0
	END AS 违规金额
FROM
	ZZS_YY_ZDYFY_4NG.医保住院结算明细 B
JOIN ZZS_YY_ZDYFY_4NG.医保住院结算主单 A ON
	A.结算单据号 = B.结算单据号
JOIN tab1 C ON
	B.结算单据号 || to_char(项目使用日期, 'yyyy-mm-dd') = C.guanlian
WHERE
	B.医保项目编码 IN ('003107020040000-320400006',
'003205000030000-320500003',
'003206000070000-320600007',
'003206000040000-320600004',
'003201000070000-320100007',
'003206000090000-320600009',
'413204000040000-320400005',
'003201000030000-320100003',
'003206000010000-320600001',
'003205000090000-320500009',
'413206000120000-320600012',
'003206000100000-320600010',
'003206000010000-320600001',
'003206000070000-320600007',
'003201000030000-320100003',
'003205000070000-320500018',
'003201000030000-320100003',
'003206000020000-320600002',
'003205000090000-320500009',
'003205000070000-320500007',
'003202000020000-320200002',
'003206000010000-320600001',
'003205000010000-320500001',
'003202000090000-320200009',
'413200000110000-321000010',
'003201000050000-320100005',
'003205000080000-320500008',
'003205000100000-320500010',
'003201000030000-320100003',
'003202000120000-320200012',
'003201000030000-320100003',
'003201000060000-320100006',
'003205000010000-320500001',
'003206000010000-320600001',
'003202000080000-320200008',
'003205000070000-320500007',
'003205000070000-320500018',
'003205000010000-320500001',
'003205000050000-3205000050',
'003202000080000-320200016',
'003201000040000-320100004',
'003204000030000-320400003',
'003205000030000-320500003',
'003205000080000-320500008',
'003308040750000-320100014',
'003204000010000-320400001',
'003202000030000-320200003',
'003202000100000-320200015',
'003205000100000-320500010',
'003206000030000-320600003',
'003201000040000-320100004',
'003202000040300-320200013',
'003202000050000-320200005',
'003206000080000-320600008',
'003206000010000-320600001',
'003202000130000-320100008',
'003201000050000-320100005',
'003202000010000-320200001',
'003202000080000-320200016',
'413206000170000-320600013',
'003202000010000-320200001',
'003206000080000-320600008',
'003205000150000-320500015',
'003205000160000-320500016',
'413308020490000-320400004',
'003205000020000-320500002',
'003201000090000-320100009',
'003202000050000-320200005',
'003202000100000-320200015',
'003206000080000-320600008',
'003205000110000-320500011',
'003202000050000-320200005',
'003202000110000-320200011',
'003202000040300-320200013',
'003202000040300-320200013',
'003202000100000-320200010',
'003205000030000-320500003',
'003206000080000-320600008',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000070000-320500007',
'003202000030000-320200003',
'003206000050000-320600005',
'003204000020000-320400002',
'003205000110000-320500011',
'003205000090000-320500009',
'003201000090000-320100002',
'003202000020000-320200002',
'003205000070000-320500018',
'003202000040000-320200004',
'003202000020000-320200002',
'003308040750000-320100015',
'003204000010000-320400001',
'003205000010000-320500001',
'003201000060000-320100006',
'003206000010000-320600001',
'003201000030000-320100003',
'003202000100000-320200010',
'003201000040000-320100004',
'003205000020000-320500002',
'003206000090000-320600009',
'003202000120000-320200012',
'003201000030000-320100003',
'003203000010000-320300001',
'003201000010000-320100001',
'003202000100000-320200010',
'003203000010000-320300001',
'003201000060000-320100006',
'003201000010000-320500017',
'003205000030000-320500003',
'003206000040000-320600004',
'003201000040000-320100004',
'003206000040000-320600004',
'003202000090000-320200009',
'003205000030000-320500003',
'003205000050000-320500005',
'003205000030000-320500003',
'003202000040000-320200004',
'003204000030000-320400003',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000060000-320600006',
'003202000070000-320200007',
'003107020040000-320400006',
'003202000020000-320200002',
'413200000110000-321000010',
'003202000070000-320200007',
'003206000010000-320600001',
'003206000030000-320600003',
'003205000130000-320500013',
'003206000040000-320600004',
'003206000110000-320600011',
'003206000010000-320600001',
'003202000130000-320100008',
'003205000020000-320500002',
'003202000030000-320200003',
'003205000090000-320500009',
'003202000070000-320200007',
'003205000020000-320500002',
'003203000030000-320300003',
'003206000030000-320600003',
'003202000130000-320100008',
'003202000040000-320200004',
'003202000030000-320200003',
'003201000010000-320100001',
'003205000060000-320500006',
'003202000090000-320200009',
'003202000040300-320200013',
'003206000100000-320600010',
'003205000030000-320500003', 'C14080518600000043890000002')
ORDER BY
	A.患者姓名,
	B.项目使用日期