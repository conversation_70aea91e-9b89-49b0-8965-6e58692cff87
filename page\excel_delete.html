<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel 行删除工具</title>
    <style>
        :root {
            --primary: #0078D4;
            --primary-hover: #106EBE;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --border-color: rgba(0, 0, 0, 0.1);
            --radius: 12px;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --transition: all 0.2s ease;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        nav {
            margin-bottom: 2rem;
        }

        nav a {
            color: var(--primary);
            text-decoration: none;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: var(--transition);
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav a:hover {
            background-color: rgba(0, 120, 212, 0.1);
        }

        h1 {
            font-size: 1.875rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--text-primary);
            text-align: center;
        }

        .card {
            background: var(--card-bg);
            border-radius: var(--radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-family: inherit;
            transition: var(--transition);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
        }

        .btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            width: 100%;
            font-size: 1rem;
        }

        .btn:hover {
            background: var(--primary-hover);
        }

        .message {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: var(--radius);
            background-color: #f0fdf4;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <nav>
            <a href="{{ url_for('index') }}">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M9.78 12.78a.75.75 0 01-1.06 0L4.47 8.53a.75.75 0 010-1.06l4.25-4.25a.75.75 0 011.06 1.06L6.06 8l3.72 3.72a.75.75 0 010 1.06z"/>
                </svg>
                返回主页
            </a>
        </nav>
        <h1>Excel 行删除工具</h1>
        <div class="card">
            <h2>选择要删除的行</h2>
            <form method="POST" action="{{ url_for('process_excel_delete') }}" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="column_name" class="form-label">请输入要检查的列名:</label>
                    <input type="text" class="form-control" id="column_name" name="column_name" required>
                </div>

                <div class="form-group">
                    <label for="delete_file" class="form-label">选择包含要删除内容的Excel文件:</label>
                    <input type="file" class="form-control" id="delete_file" name="delete_file" accept=".xlsx,.xls" required>
                </div>

                <div class="form-group">
                    <label for="folder_files" class="form-label">或选择一个或多个要处理的Excel文件:</label>
                    <input type="file" class="form-control" id="folder_files" name="folder_files[]" accept=".xlsx,.xls" multiple>
                </div>

                <button type="submit" class="btn">处理文件</button>
            </form>
        </div>
        {% if message %}
        <div class="message">
            {{ message }}
        </div>
        {% endif %}
    </div>
</body>
</html> 