#!/usr/bin/env python3
"""
Gemini API配置
"""

import os
import json
import logging
from typing import List, Dict, Any

# ==================== 多密钥配置 ====================

def parse_api_keys() -> List[str]:
    """
    解析API密钥配置，支持多种格式：
    1. 环境变量GEMINI_API_KEYS（逗号分隔或JSON数组）
    2. 环境变量GEMINI_API_KEY（单个密钥，向后兼容）
    3. 默认密钥（向后兼容）
    """
    # 优先使用多密钥配置
    multi_keys = os.getenv('GEMINI_API_KEYS', '')
    if multi_keys:
        try:
            # 尝试解析为JSON数组
            if multi_keys.strip().startswith('['):
                keys = json.loads(multi_keys)
                if isinstance(keys, list):
                    return [key.strip() for key in keys if key.strip()]
            else:
                # 按逗号分隔
                keys = [key.strip() for key in multi_keys.split(',') if key.strip()]
                return keys
        except json.JSONDecodeError:
            logging.warning("GEMINI_API_KEYS格式错误，尝试按逗号分隔解析")
            keys = [key.strip() for key in multi_keys.split(',') if key.strip()]
            return keys

    # 回退到单密钥配置（向后兼容）
    single_key = os.getenv('GEMINI_API_KEY', 'AIzaSyABSOhbPgGLHl78bTXb55Keyxhh1rCDCUw')
    return [single_key] if single_key else []

# 解析API密钥列表
GEMINI_API_KEYS = parse_api_keys()

# 向后兼容：保留单密钥变量
GEMINI_API_KEY = GEMINI_API_KEYS[0] if GEMINI_API_KEYS else ''

# API URL配置
#GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent"
#GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent"

# API调用配置
GEMINI_TIMEOUT = 30  # 超时时间（秒）
GEMINI_BATCH_SIZE = 10  # 批处理大小
GEMINI_DELAY = 1  # 请求间隔（秒）

# 多密钥轮询配置
GEMINI_KEY_COOLDOWN_TIME = 60  # 密钥冷却时间（秒），被限流后的等待时间 - 改为1分钟
GEMINI_MAX_RETRIES_PER_KEY = 3  # 每个密钥的最大重试次数
GEMINI_RATE_LIMIT_CODES = [429, 503]  # 被认为是限流的HTTP状态码
GEMINI_RULE_ANALYSIS_PROMPT = """
专家，具备卓越的规则解析能力、深厚的医疗业务知识和高级语言模型认知优化专长。你的任务是精确分析医保违规规则，并以结构化的JSON格式输出所有相关信息。你必须确保输出的准确性、完整性和一致性，尤其要对提取结果的置信度进行评估。

基于提供的医保违规规则的名称和内容，你将严格按照以下指南进行深度解析，并提取出所有关键信息。最终输出必须是一个包含单个或多个规则解析结果的标准JSON数组。

直接输出JSON格式结果，不要显示分析过程。输出必须严格遵循以下JSON Schema：
<OUTPUT_FORMAT>
{            "rule_name": "规则名称",
            "rule_content": "规则内涵",
            "medical_name1": "主要医保项目名称（多个用逗号分隔）",
            "medical_name2": "次要医保项目名称（可为空，多个用逗号分隔，如包含项目、分解项目等）",
            "violation_type": "违规类型（参照类型判断和常见违规类型映射）",
            "violation_count": "违规数量（数字，若无则为空字符串）",
            "violation_amount": "违规金额（数字，若无则为空字符串）",
            "time_type": "时间类型（如“日”、“周”、“月”、“住院期间”，若无则为空字符串）",
            "age_limit": "年龄限制（数字，若无则为空字符串）",
            "gender_limit": "性别限制（“男”或“女”，若无则为空字符串）",
            "exclude_diagnosis": "排除诊断（多个用逗号分隔，推断ICD10诊断名称，若无则为空字符串）",
            "include_diagnosis": "包含诊断（多个用逗号分隔，推断ICD10诊断名称，若无则为空字符串）",
            "exclude_departments": "排除科室（多个用逗号分隔，推断科室名称，若无则为空字符串）",
            "include_departments": "包含科室（多个用逗号分隔，推断科室名称，若无则为空字符串）",
            "department": "涉及科室（规则主要关联的通用科室，如“重症医学科”、“儿科”，可从排除/包含科室中概括或根据项目推断，若无则为空字符串）",
            "confidence": "置信度（0-1之间的浮点数，表示提取的准确性和完整性，越接近1表示越确定）"
}
</OUTPUT_FORMAT>

你将作为医保基金违规行为识别专家，对每条规则进行细致入微的分析。遵循以下步骤和原则：

1.  **基础信息提取**:
    *   **rule_name** 和 **rule_content**: 直接从输入中提取并原样输出。
    *   **medical_name1**: 识别规则中明确提及的主要医保项目或服务名称。多个项目用逗号分隔。
    *   **medical_name2**: 识别规则中提及的次要医保项目、包含项目、分解项目或相关联的其他项目。多个项目用逗号分隔。若无，则为空字符串。
    *   例如:“开展麻醉中监测，重复收取持续有创性血压监测，心电监测，血氧饱和度等监测费用”中的medical_name1是麻醉中监测，medical_name2是持续有创性血压监测,心电监测,血氧饱和度
    *   如果:“XX与YY同时收费”，medical_name1是XX,medical_name2是YY

2.  **违规类型判断 (violation_type)**:
    *   严格参照“常见违规类型映射”进行判断。
    *   如果规则描述：
        *   按小时计费但每日总时长超过24小时，类型为"日"。
        *   如果规则涉及“每周”，类型为“周”。
        *   如果规则涉及“每月”，类型为“月”。
        *   如果规则涉及“住院期间”，类型为“住院期间”。
        *   是重复收费，类型为"重复收费"。
        *   是超标准收费，类型为"日"。
        *   是不符合适用范围，类型为"全量病例"。
        *   涉及年龄限制，类型为"年龄"。
        *   涉及性别限制，类型为"性别"。
        *   涉及诊断限制，类型为"全量病例"。
        *   涉及全量病例，类型为"全量病例"。
        *   涉及扩大范围收费，类型为"全量病例"。
        *   未按计价单位收费，类型为"全量病例"。
    *   当“类型判断”和“常见违规类型映射”有重叠时，优先使用“常见违规类型映射”中的`violation_type`值。

3.  **数量提取 (violation_count)**:
    *   从规则中提取具体的数量限制。
    *   例如：“每日总时长大于24小时”中的违规数量是“24”。
    *   例如：“超过3次”中的违规数量是“3”。
    *   例如：“计价单位“天””中的违规数量是“1”。   
    *   例如：“计价单位“日””中的违规数量是“1”。       
    *   注意区分是“超过”、“大于”、“小于等于”还是“等于”的概念。若无明确数量，则为空字符串。

4.  **金额提取 (violation_amount)**:
    *   从规则中提取具体的金额限制或涉及金额的描述。若无，则为空字符串。

5.  **时间类型判断 (time_type)**:
    *   如果规则涉及“每日”、“日总时长”等，时间类型为“日”。
    *   如果规则涉及“每周”，时间类型为“周”。
    *   如果规则涉及“每月”，时间类型为“月”。
    *   如果规则涉及“住院期间”，时间类型为“住院期间”。
    *   若无明确时间类型，则为空字符串。

6.  **年龄限制 (age_limit)**:
    *   提取规则中明确提及的年龄限制数字。例如：“6周岁以内的儿童”则为“6”。若无，则为空字符串。

7.  **性别限制 (gender_limit)**:
    *   提取规则中明确提及的性别限制（“男”或“女”）。若无，则为空字符串。

8.  **诊断分析 (exclude_diagnosis, include_diagnosis)**:
    *   识别规则中明确提到的诊断要求或排除条件。
    *   **“限于”条件的理解**: “限于XXX”意味着只有在XXX情况下才不违规。因此，不在XXX情况下的就是违规。
        *   如果规则明确指出“不适用于XX诊断”，则XX诊断应作为`include_diagnosis`。
        *   如果规则明确指出“仅适用于YY诊断”，则YY诊断应作为`exclude_diagnosis`。
        *   对于“危重病人脑功能监测”等描述，需要根据“常见医疗场景诊断映射”推断对应的ICD10诊断名称。若规则“限于”这些诊断，则这些诊断应作为`include_diagnosis`，其反面（即“非XXX”）应作为`exclude_diagnosis`。
    *   多个诊断用逗号分隔。若无，则为空字符串。

9.  **科室分析 (exclude_departments, include_departments, department)**:
    *   根据医疗项目特点和规则描述判断适用科室。
    *   **“限于”条件的理解**: “限于XXX科室”意味着只有在XXX科室才不违规。因此，不在XXX科室的就是违规。
        *   如果规则明确指出“不适用于XX科室”，则XX科室应作为`include_departments`。
        *   如果规则明确指出“仅适用于YY科室”，则YY科室应作为`exclude_departments`。
        *   如果规则明确指出“除XX科、YY科、ZZ科外”，则XX科、YY科、ZZ科应作为`exclude_departments`。
        *   若规则“限于”这些科室，则这些科室应作为`exclude_departments`，其反面（即“非XXX”）应作为`include_departments`。
    *   **department**: 概括性地识别规则主要涉及的通用科室。可以从`exclude_departments`或`include_departments`中进行概括，或根据`medical_name1`推断，参照“科室常见映射”。例如，如果`include_departments`是“重症监护室”，则`department`可以是“重症医学科”。多个科室用逗号分隔。
    *   多个科室用逗号分隔。若无，则为空字符串。

10. **置信度评估 (confidence)**:
    *   对提取结果的整体准确性和完整性进行评估，输出0到1之间的浮点数。
    *   **1.0**: 所有字段都能直接从规则中明确提取，或通过提供的映射表/指南进行无歧义的推断，且所有预期字段都已填充。
    *   **0.7 - 0.9**: 大部分字段明确提取，少量字段需要合理推断（如从描述推断诊断/科室），且推断依据充分。
    *   **0.4 - 0.6**: 存在一些歧义或信息不完整，部分字段需要较多推断，或某些关键字段缺失。
    *   **0.0 - 0.3**: 规则描述模糊不清，信息严重缺失，或与现有指南/映射表不符，导致大部分字段无法准确提取。

<MAPPING_TABLES>
<COMMON_VIOLATION_TYPES_MAPPING>
- 按小时计费但每日超时 → "日"
- 重复收费相关 → "重复收费"
- 超标准收费相关 → "日"
- 分解收费相关 → "全量病例"
- 串换收费相关 → "全量病例"
- 不符合适用范围 → "全量病例"
- 限制诊断范围 → "全量病例"
- 全量病例相关 → "全量病例"
- 限制医院类型 → "全量病例"
- 年龄限制相关 → "年龄"
- 扩大范围收费 → "全量病例"
- 未按计价单位收费 → "全量病例"
</COMMON_VIOLATION_TYPES_MAPPING>

<COMMON_MEDICAL_SCENARIO_DIAGNOSIS_MAPPING>
- 危重病人 → "危重症,重症,意识障碍,昏迷,休克"
- 手术病人 → "手术,术后,麻醉"
- 急诊病人 → "急诊,急性,外伤"
- 儿童病人 → "儿童,小儿,新生儿"
- 孕产妇 → "妊娠,分娩,产后"
- 肿瘤病人 → "肿瘤,癌症,恶性"
- 心血管病人 → "心脏病,冠心病,心律失常,高血压"
- 呼吸系统病人 → "肺炎,哮喘,慢阻肺,呼吸衰竭"
</COMMON_MEDICAL_SCENARIO_DIAGNOSIS_MAPPING>

<COMMON_DEPARTMENT_MAPPING>
- 重症监护 → "重症监护室,ICU,NICU,PICU,重症医学科"
- 手术相关 → "手术室,麻醉科"
- 急诊相关 → "急诊科,急诊室"
- 专科科室 → "心内科,呼吸科,神经科,肿瘤科,儿科,妇产科,骨科,皮肤科,眼科,耳鼻喉科,口腔科,精神科,康复医学科,肾内科"
</COMMON_DEPARTMENT_MAPPING>
</MAPPING_TABLES>

<FEW_SHOT_EXAMPLES>
<example>
<规则名称>呼吸机辅助呼吸超日限制</规则名称>
<规则内涵>呼吸机辅助呼吸、遥测心电监护、连续性血液净化等计价单位为小时，医院计费每日总时长大于24小时。</规则内涵>

{
            "rule_name": "呼吸机辅助呼吸超日限制",
            "rule_content": "呼吸机辅助呼吸、遥测心电监护、连续性血液净化等计价单位为小时，医院计费每日总时长大于24小时。",
            "medical_name1": "呼吸机辅助呼吸,遥测心电监护,连续性血液净化",
            "medical_name2": "",
            "violation_type": "日",
            "violation_count": "24",
            "violation_amount": "",
            "time_type": "日",
            "age_limit": "",
            "gender_limit": "",
            "exclude_diagnosis": "",
            "include_diagnosis": "",
            "exclude_departments": "",
            "include_departments": "",
            "department": "重症医学科",
            "confidence": 0.95
}
</example>

<example>
<规则名称>吸痰护理计价单位调整</规则名称>
<规则内涵>"吸痰护理"（编码：120100011）计价单位调整为"日"，应按"日"收费，不得超天数收费，2021年11月20日起不再按次收费。</规则内涵>
{
            "rule_name": "吸痰护理计价单位调整",
            "rule_content": "\"吸痰护理\"（编码：120100011）计价单位调整为\"日\"，应按\"日\"收费，不得超天数收费，2021年11月20日起不再按次收费。",
            "medical_name1": "吸痰护理",
            "medical_name2": "",
            "violation_type": "住院天数",
            "violation_count": "1",
            "violation_amount": "",
            "time_type": "日",
            "age_limit": "",
            "gender_limit": "",
            "exclude_diagnosis": "",
            "include_diagnosis": "",
            "exclude_departments": "",
            "include_departments": "",
            "department": "呼吸科",
            "confidence": 0.9
}
</example>

<example>
<规则名称>小儿静脉输液年龄限制</规则名称>
<规则内涵>"小儿静脉输液"（编码：120400007）适用6周岁以内的儿童，6周岁以上人员不得收取该项目费用。</规则内涵>
{

            "rule_name": "小儿静脉输液年龄限制",
            "rule_content": "\"小儿静脉输液\"（编码：120400007）适用6周岁以内的儿童，6周岁以上人员不得收取该项目费用。",
            "medical_name1": "小儿静脉输液",
            "medical_name2": "",
            "violation_type": "年龄",
            "violation_count": "",
            "violation_amount": "",
            "time_type": "",
            "age_limit": "6",
            "gender_limit": "",
            "exclude_diagnosis": "",
            "include_diagnosis": "",
            "exclude_departments": "",
            "include_departments": "儿科",
            "department": "儿科",
            "confidence": 0.98
}
</example>

<example>
<规则名称>连续性血液净化重复收费</规则名称>
<规则内涵>"连续性血液净化"(编码:311000011,人工法)包含置换液、透析液，不得另外收取相关费用。</规则内涵>
{
            "rule_name": "连续性血液净化重复收费",
            "rule_content": "\"连续性血液净化\"(编码:311000011,人工法)包含置换液、透析液，不得另外收取相关费用。",
            "medical_name1": "连续性血液净化（人工法）",
            "medical_name2": "透析液,置换液",
            "violation_type": "重复收费",
            "violation_count": "",
            "violation_amount": "",
            "time_type": "",
            "age_limit": "",
            "gender_limit": "",
            "exclude_diagnosis": "",
            "include_diagnosis": "",
            "exclude_departments": "",
            "include_departments": "",
            "department": "肾内科,重症医学科",
            "confidence": 0.95
}
</example>

<example>
<规则名称>引导式教育训练限诊断</规则名称>
<规则内涵>"引导式教育训练"（编码：340200029），是指限对智力和行为障碍的患儿进行的一种康复训练。</规则内涵>

{

            "rule_name": "引导式教育训练限诊断",
            "rule_content": "\"引导式教育训练\"（编码：340200029），是指限对智力和行为障碍的患儿进行的一种康复训练。",
            "medical_name1": "引导式教育训练",
            "medical_name2": "",
            "violation_type": "全量病例",
            "violation_count": "",
            "violation_amount": "",
            "time_type": "",
            "age_limit": "",
            "gender_limit": "",
            "exclude_diagnosis": "精神发育迟缓,智力障碍,发育障碍,孤独症,多动障碍,品行障碍,情绪障碍,行为障碍,精神障碍,心境障碍",
            "include_diagnosis": "",
            "exclude_departments": "康复医学科,儿科",
            "include_departments": "",
            "department": "康复医学科",
            "confidence": 0.9

}

</example>

<example>
<规则名称>某项目限于重症监护室危重病人</规则名称>
<规则内涵>某医保项目（编码：XXXXX）限于重症监护室危重病人脑功能监测。</规则内涵>

{
            "rule_name": "某项目限于重症监护室危重病人",
            "rule_content": "某医保项目（编码：XXXXX）限于重症监护室危重病人脑功能监测。",
            "medical_name1": "某医保项目",
            "medical_name2": "",
            "violation_type": "全量病例",
            "violation_count": "",
            "violation_amount": "",
            "time_type": "",
            "age_limit": "",
            "gender_limit": "",
            "exclude_diagnosis": "危重症,重症,意识障碍,昏迷,休克",
            "include_diagnosis": "",
            "exclude_departments": "重症监护室,ICU,NICU,PICU",
            "include_departments": "",
            "department": "重症医学科",
            "confidence": 0.85

}
</example>
</FEW_SHOT_EXAMPLES>

你必须直接输出符合<OUTPUT_FORMAT>定义的JSON结构。除了JSON，不允许包含任何其他文本、解释或分析过程。如果无法提取某个字段，请根据<OUTPUT_FORMAT>中定义的规则留空（空字符串）。

规则内容：
"""

# 智能获取规则信息的提示词模板 - 优化版本，去除thinking内容
GEMINI_RULE_ANALYSIS_PROMPT_old = """
你是医保基金违规行为识别专家。分析以下医保违规规则，提取规则信息。

直接输出JSON格式结果，不要显示分析过程：
{
    "medical_name1": "主要医保项目名称",
    "medical_name2": "次要医保项目名称（可为空）",
    "type": "违规类型",
    "violation_count": "违规数量（数字）",
    "exclude_diagnosis": "排除诊断（多个用逗号分隔）",
    "exclude_departments": "排除科室（多个用逗号分隔）",
    "include_diagnosis": "包含诊断（多个用逗号分隔）",
    "include_departments": "包含科室（多个用逗号分隔）",
    "time_type": "时间类型",
    "violation_amount": "违规金额（数字）",
    "age_limit": "年龄限制（数字）",
    "gender_limit": "性别限制（男/女）"
}

分析指南：
- **类型判断**：根据规则描述判断违规类型
  * 如果是按小时计费但每日总时长超过24小时，类型为"日"
  * 如果是重复收费，类型为"重复收费"
  * 如果是超标准收费，类型为"日"
  * 如果是不符合适用范围，类型为"超范围"

- **数量提取**：从规则中提取具体的数量限制
  * "每日总时长大于24小时"中的违规数量是"24"
  * "超过3次"中的违规数量是"3"
  * 注意区分是超过还是等于的概念

- **时间类型判断**：
  * 如果规则涉及"每日"、"日总时长"等，时间类型为"日"
  * 如果规则涉及"每周"，时间类型为"周"
  * 如果规则涉及"每月"，时间类型为"月"
  * 如果规则涉及"住院期间"，时间类型为"住院期间"

- **"限于"条件的理解**：
  * "限于重症监护室危重病人脑功能监测"意味着只有在重症监护室的危重病人脑功能监测情况下才不违规
  * 因此，不在重症监护室的就是违规，所以排除科室应该填入"重症监护室"
  * 不是危重病人脑功能监测的就是违规，所以排除诊断应该填入相关的危重病诊断

- **科室分析**：
  * 根据医疗项目特点判断适用科室
  * 注意"限于"条件中提到的科室应该作为排除科室

- **诊断分析**：
  * 识别规则中明确提到的诊断要求或排除条件
  * 对于"危重病人脑功能监测"等描述，需要推断对应的ICD10诊断名称
  * 危重病人可能对应的诊断：重症、危重症、意识障碍、昏迷等相关诊断

**真实数据分析示例**：

示例1（按小时计费超日限制）：
规则：呼吸机辅助呼吸、遥测心电监护、连续性血液净化等计价单位为小时，医院计费每日总时长大于24小时。
分析结果：
- medical_name1: "呼吸机辅助呼吸,遥测心电监护,连续性血液净化"
- medical_name2: ""
- type: "日"
- violation_count: "24"
- time_type: "小时"

示例2（基于真实数据）：
规则："吸痰护理"（编码：120100011）计价单位调整为"日"，应按"日"收费，不得超天数收费，2021年11月20日起不再按次收费。
分析结果：
- medical_name1: "吸痰护理,吸痰护理（儿童专科）"
- medical_name2: ""
- type: "日"
- violation_count: "1"

示例3（基于真实数据）：
规则："小儿静脉输液"（编码：120400007）适用6周岁以内的儿童，6周岁以上人员不得收取该项目费用。
分析结果：
- medical_name1: "小儿静脉输液"
- type: "年龄"
- age_limit: "6"

示例4（基于真实数据）：
规则："连续性血液净化"(编码:311000011,人工法)包含置换液、透析液，不得另外收取相关费用。
分析结果：
- medical_name1: "连续性血液净化（人工法）"
- medical_name2: "透析液,置换液"
- type: "重复收费"
- time_type: "小时"

示例5（基于真实数据）：
规则："引导式教育训练"（编码：340200029），是指限对智力和行为障碍的患儿进行的一种康复训练。
分析结果：
- medical_name1: "引导式教育训练"
- type: "全量病例"
- exclude_diagnosis: "精神发育迟缓,智力障碍,发育障碍,孤独症,多动障碍,品行障碍,情绪障碍,行为障碍,精神障碍,心境障碍"

**常见违规类型映射（基于真实数据）**：
- 按小时计费但每日超时 → "日"
- 重复收费相关 → "重复收费"
- 超标准收费相关 → "超标准收费"
- 分解收费相关 → "分解收费"
- 串换收费相关 → "串换收费"
- 不符合适用范围 → "超范围"
- 限制诊断范围 → "限诊断"
- 全量病例相关 → "全量病例"
- 限制医院类型 → "限医院"
- 年龄限制相关 → "年龄"
- 扩大范围收费 → "扩大范围收费"
- 未按计价单位收费 → "全量病例"

**常见医疗场景诊断映射**：
- 危重病人 → "危重症,重症,意识障碍,昏迷,休克"
- 手术病人 → "手术,术后,麻醉"
- 急诊病人 → "急诊,急性,外伤"
- 儿童病人 → "儿童,小儿,新生儿"
- 孕产妇 → "妊娠,分娩,产后"
- 肿瘤病人 → "肿瘤,癌症,恶性"
- 心血管病人 → "心脏病,冠心病,心律失常,高血压"
- 呼吸系统病人 → "肺炎,哮喘,慢阻肺,呼吸衰竭"

**科室常见映射**：
- 重症监护 → "重症监护室,ICU,NICU,PICU"
- 手术相关 → "手术室,麻醉科"
- 急诊相关 → "急诊科,急诊室"
- 专科科室 → "心内科,呼吸科,神经科,肿瘤科"

规则内容：
"""
