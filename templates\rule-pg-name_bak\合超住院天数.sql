WITH tab1 AS (
    SELECT 
        结算单据号, 
        医保项目编码, 
        医保项目名称,
        医院项目编码,
        医院项目名称,
        报销比例,
        单价,
        规格,
        费用类别,
        支付类别,
        SUM(数量) AS 使用数量
    FROM 医保住院结算明细
    WHERE 医保项目名称 IN  ({医保名称1})
    GROUP BY 
        结算单据号, 
        医保项目编码, 
        医保项目名称,
        医院项目编码,
        医院项目名称,
        报销比例,
        单价,
        规格,
        费用类别,
        支付类别
),
tab2 AS (
    SELECT 
        结算单据号, 
        SUM(数量) AS 数量2
    FROM 医保住院结算明细
    WHERE 医保项目名称 IN  ({医保名称1})
    GROUP BY 结算单据号
)
SELECT 
    a.结算单据号,
    a.住院号,
    a.病案号,
    a.个人编码,
    a.患者姓名,
    a.患者性别,
    a.患者社会保障号码,
    a.患者年龄,
    a.险种类型,
    a.入院科室,
    a.入院诊断名称,
    a.入院日期,
    a.出院科室,
    a.出院日期,
    a.出院诊断名称,
    (a.出院日期::DATE) - (a.入院日期::DATE) + 1 AS 住院天数,
    a.结算日期,
    b.医保项目编码,
    b.医保项目名称,
    b.医院项目编码,
    b.医院项目名称,
    b.报销比例,
    b.费用类别,
    b.支付类别,
    b.使用数量,
    b.单价,
    b.规格
FROM 医保住院结算主单 a
JOIN tab1 b ON a.结算单据号 = b.结算单据号
JOIN tab2 c ON a.结算单据号 = c.结算单据号
    AND c.数量2 > (a.出院日期::DATE) - (a.入院日期::DATE)+1
    order by a.结算单据号

