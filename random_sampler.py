#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机病案抽取模块
功能：从指定文件夹中的多个Excel文件中随机抽取结算单据号及其对应的所有记录
"""

import os
import pandas as pd
import random
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RandomSampler:
    """随机病案抽取器"""
    
    def __init__(self):
        """初始化随机抽取器"""
        self.supported_extensions = ['.xlsx', '.xls']
        self.settlement_columns = [
            '结算单据号', '单据号', '结算号', '结算单号',
            '医保结算单据号', '医保单据号', '医保结算号',
            '结算流水号', '流水号', '单据流水号'
        ]

        # 其他重要列的匹配规则
        self.case_number_columns = ['病案号', '病案编号', '住院号', '住院编号', '病历号']
        self.patient_name_columns = ['患者姓名', '姓名', '病人姓名', '患者名称']
        self.settlement_date_columns = ['结算日期', '结算时间', '出院日期', '出院时间', '费用结算日期']
        self.department_columns = ['出院科室', '科室', '出院科室名称', '科室名称', '临床科室']
    
    def find_settlement_column(self, df: pd.DataFrame) -> Optional[str]:
        """
        查找结算单据号列
        
        Args:
            df: DataFrame对象
            
        Returns:
            找到的列名，如果没找到返回None
        """
        columns = df.columns.tolist()
        
        # 精确匹配
        for col_name in self.settlement_columns:
            if col_name in columns:
                return col_name
        
        # 模糊匹配
        for col in columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in ['单据号', '结算', '流水']):
                return col
        
        return None

    def find_column_by_keywords(self, df: pd.DataFrame, keywords: List[str]) -> Optional[str]:
        """
        根据关键词查找列

        Args:
            df: DataFrame对象
            keywords: 关键词列表

        Returns:
            找到的列名，如果没找到返回None
        """
        columns = df.columns.tolist()

        # 精确匹配
        for keyword in keywords:
            if keyword in columns:
                return keyword

        # 模糊匹配
        for col in columns:
            col_str = str(col).lower()
            for keyword in keywords:
                if keyword.lower() in col_str:
                    return col

        return None

    def extract_key_columns(self, df: pd.DataFrame) -> Dict[str, Optional[str]]:
        """
        提取关键列信息

        Args:
            df: DataFrame对象

        Returns:
            关键列映射字典
        """
        key_columns = {
            'settlement': self.find_settlement_column(df),
            'case_number': self.find_column_by_keywords(df, self.case_number_columns),
            'patient_name': self.find_column_by_keywords(df, self.patient_name_columns),
            'settlement_date': self.find_column_by_keywords(df, self.settlement_date_columns),
            'department': self.find_column_by_keywords(df, self.department_columns)
        }

        return key_columns
    
    def process_single_file(self, file_path: str, output_folder: str, 
                          sample_count: int, preserve_headers: bool = True,
                          remove_duplicates: bool = True) -> Dict[str, Any]:
        """
        处理单个Excel文件
        
        Args:
            file_path: 源文件路径
            output_folder: 输出文件夹
            sample_count: 抽取数量
            preserve_headers: 是否保留表头
            remove_duplicates: 是否去除重复
            
        Returns:
            处理结果字典
        """
        result = {
            'file_name': os.path.basename(file_path),
            'status': 'success',
            'sampled_count': 0,
            'output_file': '',
            'message': ''
        }
        
        try:
            # 读取Excel文件
            logger.info(f"正在处理文件: {file_path}")
            
            # 尝试读取第一个工作表
            df = pd.read_excel(file_path, sheet_name=0)
            
            if df.empty:
                result['status'] = 'error'
                result['message'] = '文件为空'
                return result
            
            # 提取关键列信息
            key_columns = self.extract_key_columns(df)
            settlement_col = key_columns['settlement']

            if not settlement_col:
                result['status'] = 'error'
                result['message'] = '未找到结算单据号列'
                return result

            logger.info(f"找到关键列: {key_columns}")
            
            # 去除空值
            df_clean = df.dropna(subset=[settlement_col])
            
            if df_clean.empty:
                result['status'] = 'error'
                result['message'] = '结算单据号列全为空值'
                return result
            
            # 获取唯一的结算单据号列表
            unique_settlements = df_clean[settlement_col].unique()
            logger.info(f"找到 {len(unique_settlements)} 个唯一的结算单据号")

            # 去除重复的结算单据号（如果需要）
            if remove_duplicates:
                # 这里的去重是指去除重复的结算单据号，而不是去除重复的记录
                original_settlement_count = len(unique_settlements)
                logger.info(f"唯一结算单据号数量: {original_settlement_count}")

            # 随机抽取结算单据号
            available_settlement_count = len(unique_settlements)
            actual_sample_count = min(sample_count, available_settlement_count)

            if actual_sample_count == 0:
                result['status'] = 'error'
                result['message'] = '没有可抽取的结算单据号'
                return result

            # 随机选择结算单据号
            sampled_settlements = random.sample(list(unique_settlements), actual_sample_count)
            logger.info(f"随机选择了 {len(sampled_settlements)} 个结算单据号")

            # 根据选中的结算单据号提取所有对应的记录
            sampled_df = df_clean[df_clean[settlement_col].isin(sampled_settlements)]

            # 按结算单据号排序，便于查看
            sampled_df = sampled_df.sort_values(by=settlement_col)

            logger.info(f"提取了 {len(sampled_df)} 条记录（对应 {actual_sample_count} 个结算单据号）")
            
            # 如果需要保留表头结构，确保列顺序与原文件一致
            if preserve_headers:
                sampled_df = sampled_df[df.columns]
            
            # 生成输出文件名
            file_stem = Path(file_path).stem
            record_count = len(sampled_df)
            output_filename = f"{file_stem}_随机抽取_{actual_sample_count}个单据号_{record_count}条记录.xlsx"
            output_path = os.path.join(output_folder, output_filename)

            # 保存结果
            sampled_df.to_excel(output_path, index=False)

            # 收集详细信息用于汇总
            detail_info = []
            for _, row in sampled_df.iterrows():
                detail = {
                    '结算单据号': row.get(settlement_col, '') if settlement_col else '',
                    '病案号': row.get(key_columns['case_number'], '') if key_columns['case_number'] else '',
                    '患者姓名': row.get(key_columns['patient_name'], '') if key_columns['patient_name'] else '',
                    '结算日期': row.get(key_columns['settlement_date'], '') if key_columns['settlement_date'] else '',
                    '出院科室': row.get(key_columns['department'], '') if key_columns['department'] else '',
                    '源文件': os.path.basename(file_path)

                }
                detail_info.append(detail)

            result['sampled_count'] = record_count  # 实际记录数量
            result['sampled_settlements'] = actual_sample_count  # 抽取的结算单据号数量
            result['output_file'] = output_filename
            result['message'] = f'成功抽取 {actual_sample_count} 个结算单据号，共 {record_count} 条记录'
            result['detail_info'] = detail_info  # 详细信息
            
            logger.info(f"文件处理完成: {output_filename}")
            
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
            result['status'] = 'error'
            result['message'] = f'处理失败: {str(e)}'
        
        return result
    
    def get_excel_files(self, folder_path: str) -> List[str]:
        """
        获取文件夹中的所有Excel文件
        
        Args:
            folder_path: 文件夹路径
            
        Returns:
            Excel文件路径列表
        """
        excel_files = []
        
        try:
            for file_name in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file_name)
                if os.path.isfile(file_path):
                    file_ext = os.path.splitext(file_name)[1].lower()
                    if file_ext in self.supported_extensions:
                        # 跳过汇总文件和随机抽取文件
                        if ('汇总' not in file_name and 'summary' not in file_name.lower() and
                            '随机抽取' not in file_name and 'random' not in file_name.lower()):
                            excel_files.append(file_path)
        except Exception as e:
            logger.error(f"扫描文件夹 {folder_path} 时出错: {str(e)}")
        
        return excel_files
    
    def process_folder(self, source_folder: str, output_folder: str, 
                      sample_count: int, preserve_headers: bool = True,
                      remove_duplicates: bool = True) -> Dict[str, Any]:
        """
        处理整个文件夹
        
        Args:
            source_folder: 源文件夹路径
            output_folder: 输出文件夹路径
            sample_count: 每个文件的抽取数量
            preserve_headers: 是否保留表头结构
            remove_duplicates: 是否去除重复
            
        Returns:
            处理结果字典
        """
        start_time = time.time()
        
        # 获取所有Excel文件
        excel_files = self.get_excel_files(source_folder)
        
        if not excel_files:
            return {
                'success': False,
                'error': '源文件夹中没有找到Excel文件',
                'processed_files': 0,
                'success_files': 0,
                'failed_files': 0,
                'total_sampled': 0,
                'processing_time': 0,
                'file_results': []
            }
        
        logger.info(f"找到 {len(excel_files)} 个Excel文件")
        
        # 处理结果统计
        file_results = []
        success_count = 0
        failed_count = 0
        total_sampled = 0
        
        # 逐个处理文件
        for i, file_path in enumerate(excel_files, 1):
            logger.info(f"处理进度: {i}/{len(excel_files)} - {os.path.basename(file_path)}")
            
            result = self.process_single_file(
                file_path=file_path,
                output_folder=output_folder,
                sample_count=sample_count,
                preserve_headers=preserve_headers,
                remove_duplicates=remove_duplicates
            )
            
            file_results.append(result)
            
            if result['status'] == 'success':
                success_count += 1
                total_sampled += result['sampled_count']
            else:
                failed_count += 1
        
        processing_time = round(time.time() - start_time, 2)

        # 生成处理详情汇总Excel文件
        summary_file = self.create_summary_excel(file_results, output_folder, processing_time)

        return {
            'processed_files': len(excel_files),
            'success_files': success_count,
            'failed_files': failed_count,
            'total_sampled': total_sampled,
            'processing_time': processing_time,
            'output_folder': output_folder,
            'file_results': file_results,
            'summary_file': summary_file
        }

    def create_summary_excel(self, file_results: List[Dict], output_folder: str, processing_time: float) -> str:
        """
        创建处理详情汇总Excel文件

        Args:
            file_results: 文件处理结果列表
            output_folder: 输出文件夹
            processing_time: 处理时间

        Returns:
            汇总文件路径
        """
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            summary_filename = f"随机抽取汇总_{timestamp}.xlsx"
            summary_path = os.path.join(output_folder, summary_filename)

            with pd.ExcelWriter(summary_path, engine='openpyxl') as writer:
                # 1. 处理统计汇总
                summary_data = []
                total_settlements = 0
                total_records = 0

                for result in file_results:
                    summary_data.append({
                        '文件名': result['file_name'],
                        '处理状态': '成功' if result['status'] == 'success' else '失败',
                        '抽取单据号数量': result.get('sampled_settlements', 0),
                        '记录数量': result.get('sampled_count', 0),
                        '输出文件': result.get('output_file', ''),
                        '处理信息': result.get('message', '')
                    })

                    if result['status'] == 'success':
                        total_settlements += result.get('sampled_settlements', 0)
                        total_records += result.get('sampled_count', 0)

                # 添加汇总行
                summary_data.append({
                    '文件名': '=== 汇总统计 ===',
                    '处理状态': f'成功: {sum(1 for r in file_results if r["status"] == "success")} / 总计: {len(file_results)}',
                    '抽取单据号数量': total_settlements,
                    '记录数量': total_records,
                    '输出文件': f'处理时间: {processing_time} 秒',
                    '处理信息': f'生成时间: {time.strftime("%Y-%m-%d %H:%M:%S")}'
                })

                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='处理统计', index=False)

                # 2. 详细记录汇总（按源文件和结算单据号去重）
                all_details = []
                for result in file_results:
                    if result['status'] == 'success' and 'detail_info' in result:
                        # 对每个源文件的结算单据号进行去重
                        file_details = result['detail_info']
                        if file_details:
                            # 转换为DataFrame进行去重处理
                            file_df = pd.DataFrame(file_details)
                            # 按结算单据号去重，保留第一条记录
                            unique_settlements = file_df.drop_duplicates(subset=['结算单据号'], keep='first')
                            all_details.extend(unique_settlements.to_dict('records'))

                if all_details:
                    detail_df = pd.DataFrame(all_details)
                    # 按源文件和结算单据号排序
                    detail_df = detail_df.sort_values(['源文件', '结算单据号'])
                    detail_df.to_excel(writer, sheet_name='抽取详情', index=False)

            logger.info(f"生成汇总文件: {summary_filename}")
            return summary_filename

        except Exception as e:
            logger.error(f"生成汇总文件时出错: {str(e)}")
            return ""


def main():
    """测试函数"""
    sampler = RandomSampler()
    
    # 测试参数
    source_folder = r"D:\测试数据\源文件夹"
    output_folder = r"D:\测试数据\抽取结果"
    sample_count = 5
    
    if os.path.exists(source_folder):
        result = sampler.process_folder(
            source_folder=source_folder,
            output_folder=output_folder,
            sample_count=sample_count
        )
        
        print("处理结果:")
        print(f"处理文件数: {result['processed_files']}")
        print(f"成功文件数: {result['success_files']}")
        print(f"失败文件数: {result['failed_files']}")
        print(f"总抽取记录: {result['total_sampled']}")
        print(f"处理时间: {result['processing_time']} 秒")
    else:
        print(f"测试文件夹不存在: {source_folder}")


if __name__ == "__main__":
    main()
