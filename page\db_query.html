<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库查询生成Excel</title>
    <style>
        :root {
            --primary: #0078D4;
            --primary-hover: #106EBE;
            --danger: #dc2626;
            --danger-hover: #b91c1c;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --border-color: rgba(0, 0, 0, 0.1);
            --radius: 12px;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --transition: all 0.2s ease;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        nav {
            margin-bottom: 2rem;
        }

        nav a {
            color: var(--primary);
            text-decoration: none;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: var(--transition);
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav a:hover {
            background-color: rgba(0, 120, 212, 0.1);
        }

        h1 {
            font-size: 1.875rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--text-primary);
            text-align: center;
        }

        h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
        }

        .card {
            background: var(--card-bg);
            border-radius: var(--radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        input[type="text"],
        input[type="password"],
        textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-family: inherit;
            font-size: 1rem;
            transition: var(--transition);
            background-color: var(--card-bg);
        }

        input:focus,
        textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
        }

        .split-columns {
            margin: 1rem 0;
        }

        .split-columns label {
            display: block;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
            font-weight: 500;
        }

        .split-column-row {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }

        .btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            font-size: 1rem;
        }

        .btn:hover {
            background: var(--primary-hover);
        }

        .btn-add {
            background: var(--primary);
        }

        .btn-add:hover {
            background: var(--primary-hover);
        }

        .btn-remove {
            background: var(--danger);
            padding: 0.5rem 1rem;
        }

        .btn-remove:hover {
            background: var(--danger-hover);
        }

        .messages {
            list-style: none;
            padding: 0;
            margin: 1rem 0;
        }

        .messages li {
            background-color: #f0fdf4;
            color: #166534;
            padding: 1rem;
            border-radius: var(--radius);
            margin-bottom: 0.5rem;
            border: 1px solid #bbf7d0;
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
        }

        .input-group {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1rem;
            align-items: center;
        }

        .input-group input {
            flex: 1;
            min-width: 0;
            height: 2.5rem;
            padding: 0 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 0.875rem;
        }

        .input-group input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
        }

        /* 在媒体查询中添加响应式处理 */
        @media (max-width: 768px) {
            .input-group {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .input-group input {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <nav>
            <a href="{{ url_for('index') }}">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M9.78 12.78a.75.75 0 01-1.06 0L4.47 8.53a.75.75 0 010-1.06l4.25-4.25a.75.75 0 011.06 1.06L6.06 8l3.72 3.72a.75.75 0 010 1.06z"/>
                </svg>
                返回主页
            </a>
        </nav>
        <h1>数据库查询生成Excel</h1>
        
        <div class="card">
            <h2>数据库连接信息</h2>
            <form action="{{ url_for('query_db') }}" method="post">
                <div class="input-group">
                    <input type="text" name="db_username" placeholder="数据库用户名" required value="{{ db_username }}">
                    <input type="password" name="db_password" placeholder="数据库密码" required value="{{ db_password }}">
                    <input type="text" name="db_dsn" placeholder="数据库DSN (如: 192.168.1.102:1521/orcl)" required value="{{ db_dsn }}">
                </div>
                <textarea name="query" rows="5" placeholder="输入SQL查询语句" required></textarea>
                <div class="split-columns">
                    <label>拆分列（可选，用于将结果拆分为多个Excel文件）：</label>
                    <div id="split-columns-container">
                        <div class="split-column-row">
                            <input type="text" name="split_columns[]" placeholder="输入列名">
                            <button type="button" class="btn btn-add" onclick="addSplitColumn()">+ 添加拆分列</button>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn">执行查询并生成Excel</button>
            </form>
        </div>

        {% with messages = get_flashed_messages() %}
            {% if messages %}
                <ul class="messages">
                    {% for message in messages %}
                        <li>{{ message }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
    </div>

    <script>
        function addSplitColumn() {
            var container = document.getElementById('split-columns-container');
            var row = document.createElement('div');
            row.className = 'split-column-row';
            row.innerHTML = `
                <input type="text" name="split_columns[]" placeholder="输入列名">
                <button type="button" class="btn btn-remove" onclick="removeSplitColumn(this)">- 删除</button>
            `;
            container.appendChild(row);
        }

        function removeSplitColumn(button) {
            var row = button.parentElement;
            row.parentElement.removeChild(row);
        }
    </script>
</body>
</html>
