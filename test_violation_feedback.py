#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
违规反馈功能测试脚本
"""

import requests
import json
import os

# 测试违规反馈相关API
base_url = 'http://localhost:5001'

print('=== 违规反馈功能测试 ===')

# 1. 测试文件夹测试API
def test_folder_api():
    print('\n1. 测试文件夹测试API')
    try:
        # 使用一个不存在的路径进行测试
        test_data = {
            'folder_path': 'D:/不存在的路径'
        }
        response = requests.post(f'{base_url}/api/test_folder', 
                               json=test_data, timeout=10)
        print(f'文件夹测试API - 状态码: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'响应: {data}')
        else:
            print(f'错误响应: {response.text}')
    except Exception as e:
        print(f'文件夹测试API失败: {e}')

# 2. 测试规则汇总API
def test_rule_summary_api():
    print('\n2. 测试规则汇总API')
    try:
        test_data = {
            'folder_path': 'D:/不存在的路径'
        }
        response = requests.post(f'{base_url}/api/rule_summary', 
                               json=test_data, timeout=10)
        print(f'规则汇总API - 状态码: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'响应: {data}')
        else:
            print(f'错误响应: {response.text}')
    except Exception as e:
        print(f'规则汇总API失败: {e}')

# 3. 测试违规统计API
def test_static_money_api():
    print('\n3. 测试违规统计API')
    try:
        test_data = {
            'folder_path': 'D:/不存在的路径',
            'summary_file': 'D:/不存在的文件.xlsx'
        }
        response = requests.post(f'{base_url}/api/static_money', 
                               json=test_data, timeout=10)
        print(f'违规统计API - 状态码: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'响应: {data}')
        else:
            print(f'错误响应: {response.text}')
    except Exception as e:
        print(f'违规统计API失败: {e}')

# 4. 测试一键处理API
def test_violation_processing_api():
    print('\n4. 测试一键处理API')
    try:
        test_data = {
            'folder_path': 'D:/不存在的路径',
            'optimize_performance': True
        }
        response = requests.post(f'{base_url}/api/violation_processing', 
                               json=test_data, timeout=10)
        print(f'一键处理API - 状态码: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'响应: {data}')
        else:
            print(f'错误响应: {response.text}')
    except Exception as e:
        print(f'一键处理API失败: {e}')

# 5. 测试随机抽样API
def test_random_sample_api():
    print('\n5. 测试随机抽样API')
    try:
        test_data = {
            'source_folder': 'D:/不存在的路径',
            'output_folder': 'D:/输出路径',
            'sample_count': 10,
            'preserve_headers': True,
            'remove_duplicates': True
        }
        response = requests.post(f'{base_url}/random_sample_stream', 
                               json=test_data, timeout=10)
        print(f'随机抽样API - 状态码: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'响应: {data}')
        else:
            print(f'错误响应: {response.text}')
    except Exception as e:
        print(f'随机抽样API失败: {e}')

# 6. 测试API可用性
def test_api_availability():
    print('\n6. 测试API可用性')
    apis = [
        ('文件夹测试', '/api/test_folder'),
        ('规则汇总', '/api/rule_summary'),
        ('违规统计', '/api/static_money'),
        ('一键处理', '/api/violation_processing'),
        ('随机抽样', '/random_sample_stream')
    ]
    
    for name, endpoint in apis:
        try:
            # 使用GET请求测试端点是否存在（即使不支持GET也会返回405而不是404）
            response = requests.get(f'{base_url}{endpoint}', timeout=5)
            if response.status_code in [200, 405, 400]:  # 405=方法不允许，400=请求错误，都说明端点存在
                print(f'✅ {name} API 可用')
            else:
                print(f'❌ {name} API 不可用 (状态码: {response.status_code})')
        except Exception as e:
            print(f'❌ {name} API 测试失败: {e}')

# 运行所有测试
if __name__ == "__main__":
    test_api_availability()
    test_folder_api()
    test_rule_summary_api()
    test_static_money_api()
    test_violation_processing_api()
    test_random_sample_api()
    
    print('\n=== 测试完成 ===')
    print('注意：以上测试使用了不存在的路径，主要验证API的错误处理能力。')
    print('在实际使用中，请提供有效的文件夹路径。')
