"""
调试COUNT函数识别问题
"""

import sqlglot
from sqlglot import expressions as exp

sql = """
SELECT 
    结算单据号,
    SUM(数量) as 总数量,
    COUNT(*) as 记录数,
    AVG(金额) as 平均金额,
    MAX(金额) as 最大金额,
    MIN(金额) as 最小金额
FROM 医保住院结算明细
WHERE 医保项目名称 = '测试项目'
GROUP BY 结算单据号
"""

# 解析SQL
ast = sqlglot.parse_one(sql)
print("AST结构:")
print(ast)

# 查找SELECT表达式
select_node = ast
if isinstance(select_node, exp.Select):
    print(f"\nSELECT表达式数量: {len(select_node.expressions)}")
    
    for i, expr in enumerate(select_node.expressions):
        print(f"\n表达式 {i+1}:")
        print(f"  类型: {type(expr)}")
        print(f"  内容: {expr}")
        
        if isinstance(expr, exp.Alias):
            print(f"  别名: {expr.alias}")
            print(f"  实际表达式类型: {type(expr.this)}")
            print(f"  实际表达式内容: {expr.this}")
            
            # 检查各种聚合函数类型
            actual_expr = expr.this
            print(f"  是否为Sum: {isinstance(actual_expr, exp.Sum)}")
            print(f"  是否为Count: {isinstance(actual_expr, exp.Count)}")
            print(f"  是否为Avg: {isinstance(actual_expr, exp.Avg)}")
            print(f"  是否为Max: {isinstance(actual_expr, exp.Max)}")
            print(f"  是否为Min: {isinstance(actual_expr, exp.Min)}")
            
            if hasattr(actual_expr, 'this'):
                print(f"  函数参数: {actual_expr.this} (类型: {type(actual_expr.this)})")

# 查找所有聚合函数
print(f"\n查找所有聚合函数:")
for func_type, func_name in [(exp.Sum, "SUM"), (exp.Count, "COUNT"), (exp.Avg, "AVG"), (exp.Max, "MAX"), (exp.Min, "MIN")]:
    funcs = list(ast.find_all(func_type))
    print(f"  {func_name}: {len(funcs)}")
    for j, func in enumerate(funcs):
        print(f"    {j+1}. {func} (参数: {func.this})")
