<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL 生成器</title>
    <style>
        :root {
            --primary: #0078D4;
            --primary-hover: #106EBE;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --border-color: rgba(0, 0, 0, 0.1);
            --radius: 8px;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --transition: all 0.2s ease;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 20px;
        }
        nav {
            margin-bottom: 2rem;
        }

        nav a {
            color: var(--primary);
            text-decoration: none;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: var(--transition);
            font-weight: 500;
        }

        nav a:hover {
            background-color: rgba(0, 120, 212, 0.1);
        }

        h1, h2 {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        form {
            background: var(--card-bg);
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            font-weight: 500;
        }

        input[type="text"],
        select,
        textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            margin-bottom: 1rem;
            font-family: inherit;
            transition: var(--transition);
        }

        input[type="text"]:focus,
        select:focus,
        textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
        }

        .btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn:hover {
            background: var(--primary-hover);
        }

        .file-label {
            display: inline-flex;
            align-items: center;
            background: var(--primary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .file-label:hover {
            background: var(--primary-hover);
        }

        .file-input {
            display: none;
        }

        #copyMessage {
            color: #16a34a;
            font-weight: 500;
            margin-left: 1rem;
        }

        .flashes {
            list-style: none;
            padding: 0;
            margin: 0 0 2rem;
        }

        .flashes li {
            background-color: #fee2e2;
            color: #991b1b;
            padding: 1rem;
            border-radius: var(--radius);
            margin-bottom: 0.5rem;
        }

        .generated-sql {
            background: var(--card-bg);
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-top: 2rem;
            box-shadow: var(--shadow);
        }

        textarea {
            width: 100%;
            min-height: 200px;
            font-family: 'Consolas', monospace;
            line-height: 1.5;
            resize: vertical;
        }

        .download-link {
            display: inline-flex;
            align-items: center;
            color: var(--primary);
            text-decoration: none;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: var(--transition);
            font-weight: 500;
        }

        .download-link:hover {
            background-color: rgba(0, 120, 212, 0.1);
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            form {
                padding: 1rem;
            }
        }

        .card {
            background: var(--card-bg);
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        textarea {
            width: 100%;
            min-height: 200px;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            resize: vertical;
            line-height: 1.5;
            background-color: var(--bg-color);
        }

        textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
        }

        .text-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            margin-bottom: 1rem;
            font-family: inherit;
        }

        .text-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
        }

        .rule-list table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }

        .rule-list th,
        .rule-list td {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            text-align: left;
        }

        .rule-list th {
            background-color: var(--bg-color);
            font-weight: 500;
        }

        .rule-list tr:hover {
            background-color: rgba(0, 120, 212, 0.05);
        }

        .rule-checkbox {
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav>
            <a href="{{ url_for('index') }}">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M9.78 12.78a.75.75 0 01-1.06 0L4.47 8.53a.75.75 0 010-1.06l4.25-4.25a.75.75 0 011.06 1.06L6.06 8l3.72 3.72a.75.75 0 010 1.06z"/>
                </svg>
                返回主页
            </a>
        </nav>
    <h1>SQL 生成器</h1>
    
    {% with messages = get_flashed_messages() %}
        {% if messages %}
            <ul class="flashes">
                {% for message in messages %}
                    <li>{{ message }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}

    <!-- 修改上传模板的表单 -->
    <form action="/upload_template" method="post" enctype="multipart/form-data" class="upload-form">
        <input type="file" name="template_file" id="template_file" class="file-input" accept=".sql" onchange="this.form.submit()">
        <label for="template_file" class="file-label">选择并上传模板文件</label>
        <span id="file-name"></span>
    </form>

    <form action="{{ url_for('template_variables') }}" method="post" id="templateForm">
        <div style="margin-bottom: 1rem;">
            <label style="display: inline-block; margin-right: 1rem;">
                <input type="radio" name="template_type" value="rule" 
                       {% if not template_type or template_type == 'rule' %}checked{% endif %}
                       onchange="this.form.submit()">
                规则
            </label>
            <label style="display: inline-block; margin-right: 1rem;">
                <input type="radio" name="template_type" value="manual" 
                       {% if template_type == 'manual' %}checked{% endif %}
                       onchange="this.form.submit()">
                手动
            </label>
            <label style="display: inline-block;">
                <input type="radio" name="template_type" value="excel" 
                       {% if template_type == 'excel' %}checked{% endif %}
                       onchange="this.form.submit()">
                EXCEL
            </label>
        </div>

        <label for="template">选择 SQL 模板文件:</label>
        <select name="template" id="template" required onchange="this.form.submit()">
            <option value="">-- 请选择 --</option>
            {% for template in templates %}
                <option value="{{ template }}" {% if template == selected_template %}selected{% endif %}>
                    {{ template[:-4] }}
                </option>
            {% endfor %}
        </select><br><br>
    </form>

    {% if variables  %}
        {% if template_type == 'rule' %}
        <div class="card">
            <h2>规则生成SQL</h2>
            <div class="rule-list">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <div>
                        <!-- 添加搜索功能 -->
                        <div style="display: flex; gap: 1rem; align-items: center;">
                            <select id="behaviorType" class="form-control" style="width: 200px;">
                                <option value="">全部行为认定</option>
                            </select>
                            <input type="text" id="searchKeyword" class="form-control" placeholder="输入关键字搜索" style="width: 200px;">
                            <button onclick="searchRules()" class="btn">搜索</button>
                        </div>
                    </div>
                    <div>
                        <button onclick="generateSelectedSQL()" class="btn">生成选中SQL</button>
                    </div>
                </div>
                <table class="table" style="width: 100%; margin-top: 1rem;">
                    <thead>
                        <tr>
                            <th style="width: 40px;">
                                <input type="checkbox" id="selectAll" onclick="toggleSelectAll()">
                            </th>
                            <th>序号</th>
                            <th>行为认定</th>
                            <th>规则内涵</th>
                            <th>适用范围</th>
                            <th>城市</th>
                            <th>规则来源</th>
                        </tr>
                    </thead>
                    <tbody id="rulesList">
                        <!-- 规则列表将通过JavaScript动态填充 -->
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        {% if template_type == 'manual' %}
        <div class="card">
            <h2>手动生成SQL</h2>
            <form action="{{ url_for('generate_sql') }}" method="post">
                <input type="hidden" name="template" value="{{ selected_template }}">
                <input type="hidden" name="template_type" value="{{ template_type }}">
                <h2>请输入变量值:</h2>
                {% for variable in variables %}
                    <label for="{{ variable }}">{{ variable }}:</label>
                    <input type="text" name="{{ variable }}" class="text-input" required><br><br>
                {% endfor %}
                <input type="submit" value="生成 SQL" class="btn">
            </form>
        </div>
        {% endif %}

        {% if template_type == 'excel' %}
        <div class="card">
            <h2>Excel批量生成SQL</h2>
            <form action="{{ url_for('generate_sql') }}" method="post" enctype="multipart/form-data">
                <div style="display: flex; gap: 1rem; align-items: center; margin-bottom: 1rem;">
                    <label for="excel_file" class="btn" style="margin: 0;">
                        选择 Excel 文件
                        <input type="file" 
                               name="excel_file" 
                               id="excel_file" 
                               class="file-input" 
                               accept=".xlsx,.xls" 
                               onchange="updateExcelFileName()" 
                               style="display: none;"
                               required>
                    </label>
                    <span id="excel-file-name" style="color: var(--text-secondary);"></span>
                </div>
                <input type="hidden" name="template" value="{{ selected_template }}">
                <input type="text" name="filename_columns" placeholder="用于文件名的列（用逗号分隔）" class="text-input">
                <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                    <button type="submit" name="action" value="合成一个SQL" class="btn" onclick="return validateForm()">合成一个SQL</button>
                    <button type="submit" name="action" value="批量生成单独SQL" class="btn" onclick="return validateForm()">批量生成单独SQL</button>
                    <button type="button" class="btn" onclick="addBatchToRules()">加入规则库</button>
                </div>
            </form>
        </div>
        {% endif %}
    {% endif %}

    {% if message %}
    <div>
        <h2>{{ message }}</h2>
        {% if generated_files %}
        <p>生成的SQL文件：</p>
        <ul>
            {% for file in generated_files %}
            <li>{{ file }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    {% endif %}

    {% if generated_sql %}
    <div class="card">
        <h2>生成的 SQL:</h2>
        <textarea id="generated_sql" name="generated_sql" style="font-family: 'Consolas', monospace; font-size: 0.875rem;">{{ generated_sql }}</textarea>
        <div style="display: flex; align-items: center; margin-top: 1rem;">
            <button id="copyButton" onclick="copyToClipboard()" class="btn">复制 SQL</button>
            <button onclick="addToRules()" class="btn" style="margin-left: 1rem;">加入规则库</button>
            <span id="copyMessage" style="display:none; margin-left: 1rem;">完成复制</span>
        </div>
    </div>
    {% endif %}

    <!-- 在显示生成的文件列表后添加下载链接 -->
    {% if download_link %}
    <p><a href="{{ download_link }}" download>下载 ZIP 文件</a></p>
        {% endif %}
    </div>

    {% if rules %}
    <script data-rules='{{ rules|tojson|safe }}'></script>
    {% endif %}

    <script>
    function updateExcelFileName() {
        const fileInput = document.getElementById('excel_file');
        const fileNameSpan = document.getElementById('excel-file-name');
        if (fileInput.files.length > 0) {
            fileNameSpan.textContent = fileInput.files[0].name;
        } else {
            fileNameSpan.textContent = '';
        }
    }
    function copyToClipboard() {
            var copyText = document.getElementById("generated_sql");
            copyText.select();
            document.execCommand("copy");
            var message = document.getElementById("copyMessage");
            message.style.display = "inline";  // 显示消息
            setTimeout(function() {
                message.style.display = "none";  // 10秒后隐藏消息
            }, 10000);
        }
    function validateForm() {
        const fileInput = document.getElementById('excel_file');
        if (!fileInput.files.length) {
            alert('请选择Excel文件');
            return false;
        }
        return true;
    }

    // 添加文件类型验证
    document.getElementById('excel_file').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const fileType = file.name.split('.').pop().toLowerCase();
            if (!['xlsx', 'xls'].includes(fileType)) {
                alert('请选择正确的Excel文件格式(.xlsx或.xls)');
                this.value = '';
                document.getElementById('excel-file-name').textContent = '';
            }
        }
    });

    function addToRules() {
        const sql = document.getElementById('generated_sql').value;
        if (!sql) {
            alert('没有可添加的SQL');
            return;
        }
        
        // 发送到后端的规则添加接口
        fetch('/add_to_rules', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                sql: sql,
                template: document.querySelector('select[name="template"]').value
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('成功添加到规则库');
            } else {
                alert('添加失败：' + data.message);
            }
        })
        .catch(error => {
            alert('添加失败：' + error);
        });
    }

    function addBatchToRules() {
        const fileInput = document.getElementById('excel_file');
        if (!fileInput.files.length) {
            alert('请先选择Excel文件');
            return;
        }

        const formData = new FormData();
        formData.append('excel_file', fileInput.files[0]);
        formData.append('template', document.querySelector('input[name="template"]').value);
        formData.append('filename_columns', document.querySelector('input[name="filename_columns"]').value);

        // 发送到后端的批量规则添加接口
        fetch('/add_batch_to_rules', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('成功批量添加到规则库');
            } else {
                alert('添加失败：' + data.message);
            }
        })
        .catch(error => {
            alert('添加失败：' + error);
        });
    }

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('behaviorType')) {
            loadBehaviorTypes();
            loadRules();
        }
    });

    // 加载行为认定选项
    function loadBehaviorTypes() {
        fetch('/api/behavior_types')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const select = document.getElementById('behaviorType');
                    select.innerHTML = '<option value="">全部</option>';
                    data.types.forEach(type => {
                        select.innerHTML += `<option value="${type}">${type}</option>`;
                    });
                } else {
                    alert('加载行为认定失败：' + data.error);
                }
            })
            .catch(error => {
                alert('加载行为认定失败：' + error);
            });
    }

    // 加载规则列表
    function loadRules() {
        const behaviorType = document.getElementById('behaviorType').value;
        const keyword = document.getElementById('searchKeyword').value;
        const params = new URLSearchParams({
            behavior_type: behaviorType,
            keyword: keyword
        });

        fetch(`/api/rules/search?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const tbody = document.getElementById('rulesList');
                    tbody.innerHTML = '';
                    
                    if (!data.rules || data.rules.length === 0) {
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="7" style="text-align: center;">暂无数据</td>
                            </tr>
                        `;
                    } else {
                        data.rules.forEach((rule, index) => {
                            tbody.innerHTML += `
                                <tr>
                                    <td>
                                        <input type="checkbox" class="rule-checkbox" value="${rule.id}" 
                                               onchange="handleCheckboxChange()">
                                    </td>
                                    <td>${index + 1}</td>
                                    <td>${rule.behavior_type || ''}</td>
                                    <td>${rule.rule_content || ''}</td>
                                    <td>${rule.applicable_scope || ''}</td>
                                    <td>${rule.city || ''}</td>
                                    <td>${rule.rule_source || ''}</td>
                                </tr>
                            `;
                        });
                    }
                    // 重置全选框状态
                    document.getElementById('selectAll').checked = false;
                } else {
                    alert('加载规则列表失败：' + data.error);
                }
            })
            .catch(error => {
                alert('加载规则列表失败：' + error);
            });
    }

    // 全选/取消全选
    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.rule-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
    }

    // 处理单个复选框变化
    function handleCheckboxChange() {
        const checkboxes = document.querySelectorAll('.rule-checkbox');
        const selectAllCheckbox = document.getElementById('selectAll');
        const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
        selectAllCheckbox.checked = allChecked;
    }

    // 生成选中规则的SQL
    function generateSelectedSQL() {
        const selectedCheckboxes = document.querySelectorAll('.rule-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            alert('请至少选择一个规则');
            return;
        }

        const selectedIds = Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
        
        fetch('/api/generate_rule_sql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                rule_ids: selectedIds,
                template: document.querySelector('select[name="template"]').value
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('generated_sql').value = data.sql;
            } else {
                alert('生成SQL失败：' + data.error);
            }
        })
        .catch(error => {
            alert('生成SQL失败：' + error);
        });
    }

    // 搜索规则
    function searchRules() {
        loadRules();
    }
    </script>
</body>
</html>
