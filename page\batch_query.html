<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量SQL查询生成Excel</title>
    <style>
        :root {
            --primary: #0078D4;
            --primary-hover: #106EBE;
            --bg-color: #f6f8fa;
            --card-bg: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --border-color: rgba(0, 0, 0, 0.1);
            --radius: 12px;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --transition: all 0.2s ease;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-primary);
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        nav {
            margin-bottom: 2rem;
        }

        nav a {
            color: var(--primary);
            text-decoration: none;
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: var(--transition);
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        nav a:hover {
            background-color: rgba(0, 120, 212, 0.1);
        }

        h1 {
            font-size: 1.875rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--text-primary);
            text-align: center;
        }

        h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
        }

        .card {
            background: var(--card-bg);
            border-radius: var(--radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .input-group {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .input-group input {
            flex: 1;
            min-width: 0;
            height: 2.5rem;
            padding: 0 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 0.875rem;
        }

        .input-group input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
        }

        .file-input {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            background-color: var(--card-bg);
            margin-bottom: 1rem;
        }

        .file-input span {
            color: var(--text-secondary);
            flex-grow: 1;
        }

        .file-input button {
            background: var(--primary);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: var(--radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
        }

        .file-input button:hover {
            background: var(--primary-hover);
        }

        .text-input {
            width: 100%;
            height: 2.5rem;
            padding: 0 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .text-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
        }

        .submit-button {
            width: 100%;
            background: var(--primary);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            font-size: 1rem;
        }

        .submit-button:hover {
            background: var(--primary-hover);
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .input-group {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .input-group input {
                width: 100%;
            }
        }

        .button-group {
            margin-top: 1rem;
            display: flex;
            align-items: center;
        }
        
        .save-config-btn {
            background-color: #4CAF50;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s;
        }
        
        .save-config-btn:hover {
            background-color: #45a049;
        }
        
        #save-status {
            padding: 0.5rem;
            border-radius: 4px;
        }
        
        #save-status.success {
            background-color: #dcfce7;
            color: #166534;
        }
        
        #save-status.error {
            background-color: #fee2e2;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav>
            <a href="{{ url_for('index') }}">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M9.78 12.78a.75.75 0 01-1.06 0L4.47 8.53a.75.75 0 010-1.06l4.25-4.25a.75.75 0 011.06 1.06L6.06 8l3.72 3.72a.75.75 0 010 1.06z"/>
                </svg>
                返回主页
            </a>
        </nav>
        <h1>批量SQL查询生成Excel</h1>
        <div class="card">
            <h2>数据库连接信息</h2>
            <form action="/batch_query" method="post" enctype="multipart/form-data">
                <div class="input-group">
                    <input type="text" name="db_username" placeholder="数据库用户名" required value="{{ db_username }}">
                    <input type="password" name="db_password" placeholder="数据库密码" required value="{{ db_password }}">
                    <input type="text" name="db_dsn" placeholder="数据库DSN (如: *************:1521/orcl)" required value="{{ db_dsn }}">
                </div>
                <div class="button-group">
                    <button type="button" class="save-config-btn" onclick="saveConfig()">保存配置</button>
                    <span id="save-status" style="display: none; margin-left: 10px;"></span>
                </div>
                <div class="file-input">
                    <span>未选择任何文件</span>
                    <button type="button" onclick="document.getElementById('sql-files').click()">选择文件</button>
                    <input id="sql-files" type="file" name="sql_files" accept=".sql" multiple required style="display: none;">
                </div>
                <input type="text" name="filename_columns" placeholder="用于文件名的列,用逗号隔开" class="text-input">
                <button type="submit" class="submit-button">执行批量查询并生成Excel</button>
            </form>
            {% if message %}
            <p>{{ message }}</p>
            {% endif %}
        </div>
    </div>
    <script>
        document.getElementById('sql-files').addEventListener('change', function() {
            var fileCount = this.files.length;
            document.querySelector('.file-input span').textContent = fileCount > 0 ? `已选择 ${fileCount} 个文件` : '未选择任何文件';
        });

        function saveConfig() {
            const data = {
                db_username: document.querySelector('input[name="db_username"]').value,
                db_password: document.querySelector('input[name="db_password"]').value,
                db_dsn: document.querySelector('input[name="db_dsn"]').value
            };

            fetch('/save_db_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                const statusElement = document.getElementById('save-status');
                statusElement.style.display = 'inline-block';
                
                if (data.success) {
                    statusElement.className = 'success';
                    statusElement.textContent = '配置已保存';
                } else {
                    statusElement.className = 'error';
                    statusElement.textContent = data.message || '保存失败';
                }
                
                // 3秒后隐藏状态消息
                setTimeout(() => {
                    statusElement.style.display = 'none';
                }, 3000);
            })
            .catch(error => {
                const statusElement = document.getElementById('save-status');
                statusElement.style.display = 'inline-block';
                statusElement.className = 'error';
                statusElement.textContent = '保存失败：' + error.message;
            });
        }
    </script>
</body>
</html>
