#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库实例选择功能的脚本
"""

import urllib.request
import json
import time
from datetime import datetime

def test_database_instances():
    """测试数据库实例选择功能"""
    base_url = "http://127.0.0.1:5001"
    
    print("=" * 70)
    print("测试数据库实例选择功能")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 1. 测试获取数据库实例列表
    print("\n🔍 测试1: 获取数据库实例列表")
    try:
        with urllib.request.urlopen(f"{base_url}/api/database_instances", timeout=10) as response:
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                data = json.loads(content)
                
                if data.get('success'):
                    instances = data.get('instances', {})
                    print("✅ 获取实例列表成功!")
                    
                    for db_type, db_instances in instances.items():
                        print(f"   {db_type.upper()}实例:")
                        for instance in db_instances:
                            print(f"     - {instance['id']}: {instance['name']}")
                            print(f"       描述: {instance['description']}")
                    
                    # 保存实例信息用于后续测试
                    global available_instances
                    available_instances = instances
                else:
                    print(f"❌ 获取实例列表失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.getcode()}")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 2. 测试不同数据库实例的SQL执行
    if 'available_instances' in globals():
        print("\n🔍 测试2: 在不同数据库实例中执行SQL")
        
        test_cases = []
        
        # 为每个Oracle实例创建测试用例
        for instance in available_instances.get('oracle', []):
            test_cases.append({
                'name': f"Oracle实例 - {instance['id']}",
                'database': 'oracle',
                'instance': instance['id'],
                'sql': 'SELECT 1 as test_column FROM dual'
            })
        
        # 为每个PostgreSQL实例创建测试用例
        for instance in available_instances.get('postgresql', []):
            test_cases.append({
                'name': f"PostgreSQL实例 - {instance['id']}",
                'database': 'pg',
                'instance': instance['id'],
                'sql': 'SELECT 1 as test_column'
            })
        
        for test_case in test_cases:
            print(f"\n   测试: {test_case['name']}")
            print(f"   数据库: {test_case['database']}")
            print(f"   实例: {test_case['instance']}")
            print(f"   SQL: {test_case['sql']}")
            
            try:
                test_data = {
                    "sql": test_case['sql'],
                    "database": test_case['database'],
                    "instance": test_case['instance']
                }
                
                req_data = json.dumps(test_data).encode('utf-8')
                req = urllib.request.Request(
                    f"{base_url}/api/rules/execute_sql",
                    data=req_data,
                    headers={'Content-Type': 'application/json'},
                    method='POST'
                )
                
                with urllib.request.urlopen(req, timeout=15) as response:
                    if response.getcode() == 200:
                        content = response.read().decode('utf-8')
                        data = json.loads(content)
                        
                        if data.get('success'):
                            print(f"   ✅ 执行成功!")
                            print(f"      数据库: {data.get('database', 'Unknown')}")
                            print(f"      实例: {data.get('instance', 'Unknown')}")
                            print(f"      返回行数: {data.get('affected_rows', 0)}")
                            print(f"      列名: {data.get('columns', [])}")
                            if data.get('data'):
                                print(f"      数据: {data['data'][:2]}...")  # 只显示前2行
                        else:
                            print(f"   ❌ 执行失败: {data.get('error', 'Unknown error')}")
                    else:
                        print(f"   ❌ HTTP错误: {response.getcode()}")
                        
            except Exception as e:
                print(f"   ❌ 请求异常: {str(e)}")
            
            time.sleep(1)  # 避免请求过快
    
    # 3. 测试错误处理
    print("\n🔍 测试3: 错误处理")
    
    # 测试不存在的实例
    print("\n   测试: 不存在的Oracle实例")
    try:
        test_data = {
            "sql": "SELECT 1 FROM dual",
            "database": "oracle",
            "instance": "nonexistent_instance"
        }
        
        req_data = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/rules/execute_sql",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=10) as response:
            content = response.read().decode('utf-8')
            data = json.loads(content)
            
            if not data.get('success') and '不存在' in data.get('error', ''):
                print("   ✅ 正确处理不存在的实例")
            else:
                print(f"   ❌ 错误处理异常: {data}")
                
    except urllib.error.HTTPError as e:
        if e.code == 400:
            print("   ✅ 正确返回400错误")
        else:
            print(f"   ❌ 意外的HTTP错误: {e.code}")
    except Exception as e:
        print(f"   ❌ 测试异常: {str(e)}")
    
    print("\n" + "=" * 70)
    print("数据库实例选择功能测试完成!")

if __name__ == "__main__":
    test_database_instances()
