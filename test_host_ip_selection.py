#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主机IP选择功能的脚本
"""

import urllib.request
import json
import time
from datetime import datetime

def test_host_ip_selection():
    """测试主机IP选择功能"""
    base_url = "http://127.0.0.1:5001"
    
    print("=" * 70)
    print("测试主机IP选择功能")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 1. 测试获取默认PostgreSQL主机的schema列表
    print("\n🔍 测试1: 获取默认PostgreSQL主机的Schema列表")
    try:
        test_data = {
            "database": "pg",
            "host": "default"
        }
        
        req_data = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/database_schemas",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=15) as response:
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                data = json.loads(content)
                
                if data.get('success'):
                    schemas = data.get('schemas', [])
                    print("✅ 获取PostgreSQL Schema列表成功!")
                    print(f"   找到 {len(schemas)} 个Schema:")
                    for schema in schemas:
                        print(f"     - {schema}")
                    
                    # 保存第一个schema用于后续测试
                    global pg_test_schema
                    pg_test_schema = schemas[0] if schemas else None
                else:
                    print(f"❌ 获取Schema列表失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.getcode()}")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 2. 测试获取默认Oracle主机的schema列表（过滤系统schema）
    print("\n🔍 测试2: 获取默认Oracle主机的Schema列表（已过滤系统schema）")
    try:
        test_data = {
            "database": "oracle",
            "host": "default"
        }
        
        req_data = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/database_schemas",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=15) as response:
            if response.getcode() == 200:
                content = response.read().decode('utf-8')
                data = json.loads(content)
                
                if data.get('success'):
                    schemas = data.get('schemas', [])
                    print("✅ 获取Oracle Schema列表成功!")
                    print(f"   找到 {len(schemas)} 个用户Schema:")
                    for schema in schemas:
                        print(f"     - {schema}")
                    
                    # 保存第一个schema用于后续测试
                    global oracle_test_schema
                    oracle_test_schema = schemas[0] if schemas else None
                    
                    # 验证系统schema已被过滤
                    system_schemas = ['SCOTT', 'SYSMAN', 'EXFSYS', 'MDDATA']
                    filtered_out = [s for s in system_schemas if s not in schemas]
                    if filtered_out:
                        print(f"   ✅ 系统Schema已正确过滤: {filtered_out}")
                else:
                    print(f"❌ 获取Schema列表失败: {data.get('error', 'Unknown error')}")
            else:
                print(f"❌ HTTP错误: {response.getcode()}")
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
    
    # 3. 测试在默认主机的指定Schema中执行SQL
    if 'pg_test_schema' in globals() and pg_test_schema:
        print(f"\n🔍 测试3: 在默认PostgreSQL主机的Schema '{pg_test_schema}' 中执行SQL")
        try:
            test_data = {
                "sql": "SELECT current_schema() as current_schema",
                "database": "pg",
                "host": "default",
                "schema": pg_test_schema
            }
            
            req_data = json.dumps(test_data).encode('utf-8')
            req = urllib.request.Request(
                f"{base_url}/api/rules/execute_sql",
                data=req_data,
                headers={'Content-Type': 'application/json'},
                method='POST'
            )
            
            with urllib.request.urlopen(req, timeout=15) as response:
                if response.getcode() == 200:
                    content = response.read().decode('utf-8')
                    data = json.loads(content)
                    
                    if data.get('success'):
                        print("✅ 在指定Schema中执行SQL成功!")
                        print(f"   数据库: {data.get('database', 'Unknown')}")
                        print(f"   主机: {data.get('host', 'Unknown')}")
                        print(f"   Schema: {data.get('schema', 'Unknown')}")
                    else:
                        print(f"❌ 执行失败: {data.get('error', 'Unknown error')}")
                else:
                    print(f"❌ HTTP错误: {response.getcode()}")
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
    
    if 'oracle_test_schema' in globals() and oracle_test_schema:
        print(f"\n🔍 测试4: 在默认Oracle主机的Schema '{oracle_test_schema}' 中执行SQL")
        try:
            test_data = {
                "sql": "SELECT SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA') as current_schema FROM dual",
                "database": "oracle",
                "host": "default",
                "schema": oracle_test_schema
            }
            
            req_data = json.dumps(test_data).encode('utf-8')
            req = urllib.request.Request(
                f"{base_url}/api/rules/execute_sql",
                data=req_data,
                headers={'Content-Type': 'application/json'},
                method='POST'
            )
            
            with urllib.request.urlopen(req, timeout=15) as response:
                if response.getcode() == 200:
                    content = response.read().decode('utf-8')
                    data = json.loads(content)
                    
                    if data.get('success'):
                        print("✅ 在指定Schema中执行SQL成功!")
                        print(f"   数据库: {data.get('database', 'Unknown')}")
                        print(f"   主机: {data.get('host', 'Unknown')}")
                        print(f"   Schema: {data.get('schema', 'Unknown')}")
                        print(f"   当前Schema: {data.get('data', [[]])[0][0] if data.get('data') else 'Unknown'}")
                    else:
                        print(f"❌ 执行失败: {data.get('error', 'Unknown error')}")
                else:
                    print(f"❌ HTTP错误: {response.getcode()}")
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
    
    # 5. 测试错误处理 - 无效主机IP
    print(f"\n🔍 测试5: 测试无效主机IP的错误处理")
    try:
        test_data = {
            "database": "pg",
            "host": "999.999.999.999"  # 无效IP
        }
        
        req_data = json.dumps(test_data).encode('utf-8')
        req = urllib.request.Request(
            f"{base_url}/api/database_schemas",
            data=req_data,
            headers={'Content-Type': 'application/json'},
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=10) as response:
            content = response.read().decode('utf-8')
            data = json.loads(content)
            
            if not data.get('success') and '失败' in data.get('error', ''):
                print("✅ 正确处理无效主机IP")
            else:
                print(f"❌ 错误处理异常: {data}")
                
    except urllib.error.HTTPError as e:
        if e.code == 500:
            print("✅ 正确返回500错误")
        else:
            print(f"❌ 意外的HTTP错误: {e.code}")
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
    
    print("\n" + "=" * 70)
    print("主机IP选择功能测试完成!")

if __name__ == "__main__":
    test_host_ip_selection()
