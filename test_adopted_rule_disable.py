#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试已采用规则的禁用功能
"""

import requests
import json
import sys
from collections import defaultdict

def test_adopted_rule_disable():
    """测试已采用规则的禁用功能"""
    base_url = 'http://localhost:5001'
    
    print("=== 测试已采用规则的禁用功能 ===")
    
    try:
        # 1. 获取医院列表
        print("1. 获取医院列表...")
        response = requests.get(f'{base_url}/api/hospitals', timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 获取医院列表失败: HTTP {response.status_code}")
            return False
            
        hospitals_data = response.json()
        if not hospitals_data.get('success') or not hospitals_data.get('hospitals'):
            print(f"❌ 医院列表API返回失败")
            return False
            
        hospitals = hospitals_data['hospitals']
        print(f"✅ 成功获取 {len(hospitals)} 家医院")
        
        # 选择第一家医院进行测试
        test_hospital = hospitals[0]
        hospital_id = test_hospital['医院ID']
        hospital_name = test_hospital['医院名称']
        print(f"📋 测试医院: {hospital_name} (ID: {hospital_id})")
        
        # 2. 获取已采用规则
        print("\n2. 获取已采用规则...")
        response = requests.get(f'{base_url}/api/hospital-rules/adopted/{hospital_id}', timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 获取已采用规则失败: HTTP {response.status_code}")
            return False
            
        adopted_data = response.json()
        if not adopted_data.get('success'):
            print(f"❌ 已采用规则API返回失败")
            return False
            
        adopted_rules = adopted_data.get('rules', [])
        print(f"✅ 获取到 {len(adopted_rules)} 条已采用规则")
        
        if len(adopted_rules) == 0:
            print("⚠️  没有已采用规则，无法测试禁用功能")
            return True
        
        # 3. 分析已采用规则
        print("\n3. 分析已采用规则...")
        
        # 按规则ID分组
        rule_id_groups = defaultdict(list)
        adopted_rule_ids = set()
        
        for rule in adopted_rules:
            rule_id = rule.get('规则ID')
            adopted_rule_ids.add(rule_id)
            rule_id_groups[rule_id].append(rule)
        
        print(f"   已采用规则ID数: {len(adopted_rule_ids)}")
        print(f"   已采用记录总数: {len(adopted_rules)}")
        
        # 显示一些已采用规则的示例
        print("\n   已采用规则示例 (前3条):")
        for i, rule in enumerate(adopted_rules[:3]):
            print(f"   规则 {i+1}:")
            print(f"     适用ID: {rule.get('适用ID')}")
            print(f"     规则ID: {rule.get('规则ID')}")
            print(f"     规则名称: {rule.get('规则名称')}")
            print(f"     状态: {rule.get('状态')}")
            print(f"     城市: {rule.get('城市')}")
            print()
        
        # 4. 生成规则推荐，检查是否正确禁用
        print("4. 生成规则推荐，检查禁用状态...")
        response = requests.post(f'{base_url}/api/hospital-rules/generate', 
                               json={'hospital_id': hospital_id, 'limit': 200}, timeout=60)
        
        if response.status_code != 200:
            print(f"❌ 生成规则推荐失败: HTTP {response.status_code}")
            return False
            
        recommendations_data = response.json()
        if not recommendations_data.get('success'):
            print(f"❌ 规则推荐API返回失败: {recommendations_data.get('error', '无数据')}")
            return False
            
        recommendations = recommendations_data.get('recommendations', [])
        print(f"✅ 成功生成 {len(recommendations)} 条推荐记录")
        
        # 5. 检查推荐中是否包含已采用规则ID的其他记录
        print("\n5. 检查推荐中的已采用规则ID...")
        
        # 按规则ID分组推荐记录
        rec_rule_id_groups = defaultdict(list)
        for rec in recommendations:
            rule_id = rec.get('规则ID')
            rec_rule_id_groups[rule_id].append(rec)
        
        # 找出推荐中包含已采用规则ID的记录
        conflicting_rule_ids = []
        for rule_id in adopted_rule_ids:
            if rule_id in rec_rule_id_groups:
                conflicting_rule_ids.append(rule_id)
        
        print(f"   推荐中包含已采用规则ID的数量: {len(conflicting_rule_ids)}")
        
        if len(conflicting_rule_ids) > 0:
            print("   冲突规则ID详情 (显示前5个):")
            for i, rule_id in enumerate(conflicting_rule_ids[:5]):
                print(f"   规则ID {rule_id}:")
                
                # 显示已采用的记录
                adopted_records = rule_id_groups[rule_id]
                print(f"     已采用记录: {len(adopted_records)} 条")
                for j, record in enumerate(adopted_records):
                    print(f"       记录 {j+1}: 适用ID={record.get('适用ID')}, 状态={record.get('状态')}, 城市={record.get('城市')}")
                
                # 显示推荐的记录
                rec_records = rec_rule_id_groups[rule_id]
                print(f"     推荐记录: {len(rec_records)} 条")
                for j, record in enumerate(rec_records):
                    print(f"       记录 {j+1}: 适用ID={record.get('适用ID')}, 状态={record.get('状态')}, 城市={record.get('城市')}")
                print()
        
        # 6. 验证推荐记录的状态
        print("6. 验证推荐记录状态...")
        
        # 检查推荐中是否有已采用规则ID的"推荐"状态记录
        problematic_records = []
        for rec in recommendations:
            rule_id = rec.get('规则ID')
            status = rec.get('状态')
            if rule_id in adopted_rule_ids and status == '推荐':
                problematic_records.append(rec)
        
        if len(problematic_records) > 0:
            print(f"❌ 发现问题：有 {len(problematic_records)} 条记录")
            print("   这些记录的规则ID已有'已采用'状态，但仍显示为'推荐'状态")
            print("   问题记录示例 (前3条):")
            for i, rec in enumerate(problematic_records[:3]):
                print(f"   记录 {i+1}:")
                print(f"     适用ID: {rec.get('适用ID')}")
                print(f"     规则ID: {rec.get('规则ID')}")
                print(f"     状态: {rec.get('状态')} (应该被禁用)")
                print(f"     城市: {rec.get('城市')}")
                print()
        else:
            print("✅ 没有发现问题记录")
            print("   所有已采用规则ID的其他记录都正确处理了")
        
        # 7. 测试结果总结
        print("\n=== 测试结果总结 ===")
        print(f"医院: {hospital_name}")
        print(f"已采用规则ID数: {len(adopted_rule_ids)}")
        print(f"推荐记录总数: {len(recommendations)}")
        print(f"冲突规则ID数: {len(conflicting_rule_ids)}")
        print(f"问题记录数: {len(problematic_records)}")
        
        # 判断测试结果
        if len(problematic_records) == 0:
            print("✅ 已采用规则禁用功能测试成功！")
            print("   - 已采用规则ID的其他记录都被正确处理")
            print("   - 没有发现可以重复选择的问题")
            return True
        else:
            print("❌ 发现问题，需要进一步修复")
            print(f"   - 有 {len(problematic_records)} 条记录仍可被选择")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == '__main__':
    print("开始测试已采用规则的禁用功能...\n")
    
    success = test_adopted_rule_disable()
    
    if success:
        print("\n🎉 测试通过！已采用规则禁用功能正常工作。")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，需要进一步检查。")
        sys.exit(1)
