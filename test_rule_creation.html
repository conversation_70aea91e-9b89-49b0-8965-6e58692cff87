<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则创建测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>规则创建ID传递测试</h2>
        
        <div class="card">
            <div class="card-body">
                <form id="testForm">
                    <div class="mb-3">
                        <label for="ruleName" class="form-label">规则名称</label>
                        <input type="text" class="form-control" id="ruleName" name="规则名称" value="测试规则ID传递">
                    </div>
                    
                    <div class="mb-3">
                        <label for="ruleType" class="form-label">规则类型</label>
                        <select class="form-control" id="ruleType" name="规则类型">
                            <option value="定性">定性</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="behavior" class="form-label">行为认定</label>
                        <input type="text" class="form-control" id="behavior" name="行为认定" value="测试行为">
                    </div>
                    
                    <div class="mb-3">
                        <label for="city" class="form-label">城市</label>
                        <select class="form-control" id="city">
                            <option value="">请选择城市</option>
                            <option value="南京">南京</option>
                            <option value="北京">北京</option>
                        </select>
                    </div>
                    
                    <input type="hidden" name="id" value="9999">
                    <input type="hidden" name="序号" value="9999">
                    <input type="hidden" name="类型" value="测试类型">
                    
                    <button type="button" class="btn btn-primary" onclick="testRuleCreation()">测试创建规则</button>
                </form>
                
                <div id="result" class="mt-4"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let editingId = null; // 模拟新建规则的情况
        
        function testRuleCreation() {
            console.log('开始测试规则创建...');
            
            // 收集表单数据
            const formData = {};
            const medicalData = {}; // 分离医保相关数据
            
            document.querySelectorAll('#testForm input, #testForm select').forEach(element => {
                if (element.name) {
                    formData[element.name] = element.value;
                }
            });
            
            const originalRuleId = formData.id;  // 表单中的原始ID
            const city = document.getElementById('city').value;
            
            console.log('表单数据:', formData);
            console.log('原始规则ID:', originalRuleId);
            console.log('选择的城市:', city);
            
            // 先保存规则基本信息
            const url = editingId ? `/api/rules/${editingId}` : '/api/rules';
            const method = editingId ? 'PUT' : 'POST';
            
            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(async response => {
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.error || '保存失败');
                }
                
                console.log('规则保存成功，返回数据:', data);
                
                // 获取实际的规则ID（对于新创建的规则，使用后端返回的ID；对于编辑的规则，使用原始ID）
                const actualRuleId = editingId ? editingId : (data.id || originalRuleId);
                console.log('使用的规则ID:', actualRuleId, '(原始ID:', originalRuleId, ', 编辑ID:', editingId, ', 返回ID:', data.id, ')');
                
                // 显示结果
                document.getElementById('result').innerHTML = `
                    <div class="alert alert-success">
                        <h5>规则创建成功！</h5>
                        <p><strong>原始ID:</strong> ${originalRuleId}</p>
                        <p><strong>后端返回ID:</strong> ${data.id}</p>
                        <p><strong>实际使用ID:</strong> ${actualRuleId}</p>
                        <p><strong>编辑模式:</strong> ${editingId ? '是' : '否'}</p>
                    </div>
                `;
                
                // 如果保存成功，并且选择了城市，则保存医保编码对照
                if (city && city !== '') {
                    console.log('开始创建医保编码对照...');
                    
                    // 构建医保编码对照数据
                    const medicalCodeData = {
                        规则ID: actualRuleId,  // 使用实际的规则ID
                        城市: city,
                        规则内涵: '测试规则内涵',
                        规则来源: '测试来源'
                    };
                    
                    console.log('医保编码对照数据:', medicalCodeData);
                    
                    // 查询城市关联信息（使用实际的规则ID）
                    fetch(`/api/rules/city/${encodeURIComponent(city)}/${actualRuleId}`)
                        .then(response => response.json())
                        .then(cityData => {
                            console.log('城市查询结果:', cityData);
                            if (cityData.COMPARE_ID) {
                                console.log('存在对照ID，执行更新操作');
                                // 如果存在对照ID，执行更新操作
                                return fetch(`/api/rules/${actualRuleId}`, {
                                    method: 'PUT',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify({
                                        ...medicalCodeData,
                                        对照ID: cityData.COMPARE_ID
                                    })
                                });
                            } else {
                                console.log('不存在对照ID，执行新增操作，规则ID:', actualRuleId);
                                // 如果不存在对照ID，执行新增操作
                                return fetch(`/api/rule_medical_codes/${actualRuleId}`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify(medicalCodeData)
                                });
                            }
                        })
                        .then(response => response.json())
                        .then(result => {
                            console.log('医保编码对照操作结果:', result);
                            if (!result.success) {
                                throw new Error(result.error || '保存医保编码对照失败');
                            }
                            
                            // 更新结果显示
                            document.getElementById('result').innerHTML += `
                                <div class="alert alert-info mt-2">
                                    <h6>医保编码对照创建成功！</h6>
                                    <p><strong>使用的规则ID:</strong> ${actualRuleId}</p>
                                    <p><strong>城市:</strong> ${city}</p>
                                </div>
                            `;
                        })
                        .catch(error => {
                            console.error('保存医保编码对照失败:', error);
                            document.getElementById('result').innerHTML += `
                                <div class="alert alert-danger mt-2">
                                    <h6>医保编码对照创建失败！</h6>
                                    <p><strong>错误:</strong> ${error.message}</p>
                                    <p><strong>使用的规则ID:</strong> ${actualRuleId}</p>
                                </div>
                            `;
                        });
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                document.getElementById('result').innerHTML = `
                    <div class="alert alert-danger">
                        <h5>规则创建失败！</h5>
                        <p><strong>错误:</strong> ${error.message}</p>
                    </div>
                `;
            });
        }
    </script>
</body>
</html>
