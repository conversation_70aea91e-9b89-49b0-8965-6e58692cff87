<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini API密钥管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .key-card {
            transition: all 0.3s ease;
        }
        .key-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-available {
            color: #198754;
        }
        .status-unavailable {
            color: #dc3545;
        }
        .key-preview {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
        }
        .refresh-btn {
            animation: none;
        }
        .refresh-btn.spinning {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="bi bi-key"></i> Gemini API密钥管理</h1>
                    <div>
                        <button class="btn btn-success me-2" onclick="showAddKeyModal()">
                            <i class="bi bi-plus-circle"></i> 添加密钥
                        </button>
                        <button class="btn btn-outline-primary me-2" onclick="refreshStatus()">
                            <i class="bi bi-arrow-clockwise refresh-btn" id="refreshIcon"></i> 刷新状态
                        </button>
                        <button class="btn btn-warning" onclick="resetAllKeys()">
                            <i class="bi bi-arrow-counterclockwise"></i> 重置所有密钥
                        </button>
                    </div>
                </div>

                <!-- 总体状态卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">总密钥数</h5>
                                <h2 class="text-primary" id="totalKeys">-</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">可用密钥</h5>
                                <h2 class="text-success" id="availableKeys">-</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">总使用次数</h5>
                                <h2 class="text-info" id="totalUsage">-</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">总错误次数</h5>
                                <h2 class="text-danger" id="totalErrors">-</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 密钥详情 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-list-ul"></i> 密钥详情</h5>
                    </div>
                    <div class="card-body">
                        <div id="keysContainer" class="row">
                            <!-- 密钥卡片将在这里动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 操作日志 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-journal-text"></i> 操作日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="logContainer" style="max-height: 300px; overflow-y: auto;">
                            <!-- 日志将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加密钥模态框 -->
    <div class="modal fade" id="addKeyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle"></i> 添加新密钥
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addKeyForm">
                        <div class="mb-3">
                            <label for="newApiKey" class="form-label">API密钥</label>
                            <textarea class="form-control" id="newApiKey" rows="3"
                                      placeholder="请输入Gemini API密钥（以AIza开头）"
                                      required></textarea>
                            <div class="form-text">
                                密钥格式：以"AIza"开头，长度20-100个字符，只包含字母、数字、下划线和连字符
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="keyDescription" class="form-label">描述（可选）</label>
                            <input type="text" class="form-control" id="keyDescription"
                                   placeholder="为这个密钥添加描述">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" onclick="addNewKey()">
                        <i class="bi bi-plus-circle"></i> 添加密钥
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">系统通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastBody">
                <!-- 通知内容 -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let statusData = null;

        // 页面加载时获取状态
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            // 每30秒自动刷新
            setInterval(refreshStatus, 30000);
        });

        // 刷新状态
        function refreshStatus() {
            const refreshIcon = document.getElementById('refreshIcon');
            refreshIcon.classList.add('spinning');

            fetch('/api/gemini/keys/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        statusData = data.data;
                        updateStatusDisplay();
                        addLog('状态刷新成功', 'success');
                    } else {
                        showToast('获取状态失败: ' + data.error, 'error');
                        addLog('状态刷新失败: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    showToast('网络错误: ' + error.message, 'error');
                    addLog('网络错误: ' + error.message, 'error');
                })
                .finally(() => {
                    refreshIcon.classList.remove('spinning');
                });
        }

        // 更新状态显示
        function updateStatusDisplay() {
            if (!statusData) return;

            // 更新总体统计
            document.getElementById('totalKeys').textContent = statusData.total_keys;
            document.getElementById('availableKeys').textContent = statusData.available_keys;
            document.getElementById('totalUsage').textContent = statusData.total_usage;
            document.getElementById('totalErrors').textContent = statusData.total_errors;

            // 更新密钥详情
            const container = document.getElementById('keysContainer');
            container.innerHTML = '';

            statusData.key_details.forEach(key => {
                const keyCard = createKeyCard(key);
                container.appendChild(keyCard);
            });
        }

        // 创建密钥卡片
        function createKeyCard(keyDetail) {
            const col = document.createElement('div');
            col.className = 'col-md-6 col-lg-4 mb-3';

            const statusClass = keyDetail.is_available ? 'status-available' : 'status-unavailable';
            const statusIcon = keyDetail.is_available ? 'bi-check-circle-fill' : 'bi-x-circle-fill';
            const statusText = keyDetail.is_available ? '可用' : '不可用';

            col.innerHTML = `
                <div class="card key-card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span class="key-preview">${keyDetail.key_preview}</span>
                        <span class="${statusClass}">
                            <i class="bi ${statusIcon}"></i> ${statusText}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <small class="text-muted">使用次数</small>
                                <div class="fw-bold">${keyDetail.usage_count}</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">错误次数</small>
                                <div class="fw-bold text-danger">${keyDetail.error_count}</div>
                            </div>
                            <div class="col-4">
                                <small class="text-muted">连续错误</small>
                                <div class="fw-bold text-warning">${keyDetail.consecutive_errors}</div>
                            </div>
                        </div>
                        
                        ${keyDetail.last_used ? `
                            <div class="mb-2">
                                <small class="text-muted">最后使用:</small>
                                <div class="small">${new Date(keyDetail.last_used).toLocaleString()}</div>
                            </div>
                        ` : ''}
                        
                        ${keyDetail.cooldown_until ? `
                            <div class="mb-2">
                                <small class="text-muted">冷却至:</small>
                                <div class="small text-warning">${new Date(keyDetail.cooldown_until).toLocaleString()}</div>
                            </div>
                        ` : ''}
                        
                        ${keyDetail.last_error ? `
                            <div class="mb-2">
                                <small class="text-muted">最后错误:</small>
                                <div class="small text-danger">${keyDetail.last_error}</div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-sm ${keyDetail.is_available ? 'btn-outline-warning' : 'btn-outline-success'}"
                                    onclick="${keyDetail.is_available ? 'disableKey' : 'enableKey'}(${keyDetail.index})">
                                <i class="bi ${keyDetail.is_available ? 'bi-pause' : 'bi-play'}"></i>
                                ${keyDetail.is_available ? '禁用' : '启用'}
                            </button>
                            <button class="btn btn-sm btn-outline-danger"
                                    onclick="deleteKey(${keyDetail.index})"
                                    ${statusData.total_keys <= 1 ? 'disabled title="无法删除最后一个密钥"' : ''}>
                                <i class="bi bi-trash"></i>
                                删除
                            </button>
                        </div>
                    </div>
                </div>
            `;

            return col;
        }

        // 禁用密钥
        function disableKey(keyIndex) {
            const reason = prompt('请输入禁用原因:', '手动禁用');
            if (!reason) return;

            fetch(`/api/gemini/keys/${keyIndex}/disable`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ reason: reason })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('密钥禁用成功', 'success');
                    addLog(`密钥 ${keyIndex} 已禁用: ${reason}`, 'warning');
                    refreshStatus();
                } else {
                    showToast('禁用失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showToast('网络错误: ' + error.message, 'error');
            });
        }

        // 启用密钥
        function enableKey(keyIndex) {
            fetch(`/api/gemini/keys/${keyIndex}/enable`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('密钥启用成功', 'success');
                    addLog(`密钥 ${keyIndex} 已启用`, 'success');
                    refreshStatus();
                } else {
                    showToast('启用失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showToast('网络错误: ' + error.message, 'error');
            });
        }

        // 重置所有密钥
        function resetAllKeys() {
            if (!confirm('确定要重置所有密钥状态吗？这将清除所有错误记录和冷却状态。')) {
                return;
            }

            fetch('/api/gemini/keys/reset', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('所有密钥状态已重置', 'success');
                    addLog('所有密钥状态已重置', 'info');
                    refreshStatus();
                } else {
                    showToast('重置失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showToast('网络错误: ' + error.message, 'error');
            });
        }

        // 显示添加密钥模态框
        function showAddKeyModal() {
            const modal = new bootstrap.Modal(document.getElementById('addKeyModal'));
            document.getElementById('addKeyForm').reset();
            modal.show();
        }

        // 添加新密钥
        function addNewKey() {
            const newApiKey = document.getElementById('newApiKey').value.trim();
            const keyDescription = document.getElementById('keyDescription').value.trim();

            if (!newApiKey) {
                showToast('请输入API密钥', 'error');
                return;
            }

            // 前端验证
            if (!newApiKey.startsWith('AIza')) {
                showToast('密钥格式错误：应以"AIza"开头', 'error');
                return;
            }

            if (newApiKey.length < 20 || newApiKey.length > 100) {
                showToast('密钥长度错误：应在20-100个字符之间', 'error');
                return;
            }

            fetch('/api/gemini/keys', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    key: newApiKey,
                    description: keyDescription
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('密钥添加成功', 'success');
                    addLog(`新密钥已添加: ${newApiKey.substring(0, 10)}...`, 'success');

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addKeyModal'));
                    modal.hide();

                    refreshStatus();
                } else {
                    showToast('添加失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showToast('网络错误: ' + error.message, 'error');
            });
        }

        // 删除密钥
        function deleteKey(keyIndex) {
            if (!statusData || !statusData.key_details[keyIndex]) {
                showToast('无效的密钥索引', 'error');
                return;
            }

            const keyDetail = statusData.key_details[keyIndex];

            if (statusData.total_keys <= 1) {
                showToast('无法删除最后一个密钥', 'error');
                return;
            }

            if (!confirm(`确定要删除密钥 ${keyDetail.key_preview} 吗？\n\n此操作不可撤销！`)) {
                return;
            }

            fetch(`/api/gemini/keys/${keyIndex}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('密钥删除成功', 'success');
                    addLog(`密钥已删除: ${keyDetail.key_preview}`, 'warning');
                    refreshStatus();
                } else {
                    showToast('删除失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showToast('网络错误: ' + error.message, 'error');
            });
        }

        // 显示Toast通知
        function showToast(message, type = 'info') {
            const toastBody = document.getElementById('toastBody');
            const toast = document.getElementById('toast');
            
            toastBody.textContent = message;
            
            // 设置Toast样式
            toast.className = `toast ${type === 'error' ? 'bg-danger text-white' : 
                                     type === 'success' ? 'bg-success text-white' : 
                                     'bg-info text-white'}`;
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            
            const timestamp = new Date().toLocaleString();
            const iconClass = type === 'error' ? 'bi-exclamation-triangle text-danger' :
                             type === 'success' ? 'bi-check-circle text-success' :
                             type === 'warning' ? 'bi-exclamation-circle text-warning' :
                             'bi-info-circle text-info';
            
            logEntry.className = 'border-bottom pb-2 mb-2';
            logEntry.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi ${iconClass} me-2"></i>
                    <span class="small text-muted me-2">${timestamp}</span>
                    <span>${message}</span>
                </div>
            `;
            
            logContainer.insertBefore(logEntry, logContainer.firstChild);
            
            // 限制日志条数
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.lastChild);
            }
        }
    </script>
</body>
</html>
